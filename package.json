{"name": "ibicash-monorepo", "private": true, "scripts": {"dev:landing": "pnpm --filter ibicash-landing dev", "dev:app": "pnpm --filter plant-your-tree-front dev", "dev": "concurrently \"pnpm run dev:landing\" \"pnpm run dev:app\"", "build:landing": "pnpm --filter ibicash-landing build", "build:app": "pnpm --filter plant-your-tree-front build", "build": "pnpm run build:landing && pnpm run build:app"}, "devDependencies": {"concurrently": "^9.1.2", "npm-run-all": "^4.1.5"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}