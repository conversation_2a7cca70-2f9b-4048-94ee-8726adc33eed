{"name": "plant-your-tree-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "commit": "git commit -m 'init commit'", "format-global": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@gsap/react": "^2.1.0", "@hcaptcha/react-hcaptcha": "^1.11.1", "@mapbox/mapbox-gl-draw": "^1.5.0", "@next/font": "^14.2.15", "@privy-io/react-auth": "^1.82.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@react-google-maps/api": "^2.19.3", "@react-three/drei": "^9.115.0", "@react-three/fiber": "^8.17.10", "@stackso/js-core": "^0.4.16", "@tanstack/react-query": "^5.59.19", "@tanstack/react-query-devtools": "^5.59.19", "@turf/turf": "^7.1.0", "@types/geojson": "^7946.0.16", "@types/mapbox-gl": "^3.4.0", "animate.css": "^4.1.1", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.11.10", "gsap": "^3.12.2", "input-otp": "^1.2.4", "libphonenumber-js": "^1.11.19", "lodash": "^4.17.21", "lucide-react": "^0.439.0", "mapbox-gl": "^3.6.0", "next": "14.2.8", "next-pwa": "^5.6.0", "nextjs-toploader": "^3.6.15", "react": "^18.3.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-map-gl": "^7.1.7", "react-mapbox-gl": "^5.1.1", "recharts": "^2.13.3", "scheduler": "^0.23.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "three": "^0.170.0", "three-globe": "^2.34.3", "use-context-selector": "^2.0.0", "vaul": "^0.9.2", "viem": "^2.23.5", "wagmi": "^2.14.12", "zustand": "^4.5.5"}, "devDependencies": {"@iconify/react": "^5.0.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/lodash": "^4.17.16", "@types/mapbox__mapbox-gl-draw": "^1.4.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.2.8", "husky": "^9.1.5", "lint-staged": "^15.2.10", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,md}": "prettier --write", "src/**/*.{js,jsx,ts,tsx}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && npx cz --hook || true"}}}