"use client";

import { MapProvider } from "@/contexts/MapContext";
import { PrivyAuthProvider } from "@/contexts/PrivyAuthContext";
import { getConfig, privyConfig } from "@/utils/configs";
import { PRIVY_APPID } from "@/utils/constants";
import { handleStorage } from "@/utils/storage";
import { PrivyProvider } from "@privy-io/react-auth";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { WagmiProvider } from "wagmi";

import ReactQueryProvider from "./ReactQueryProvider";

export default function Providers({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [config] = useState(() => getConfig());

  const handleWorldRedirect = () => {
    const pathHistory = handleStorage<string>("session", "pathHistory", "get");
    if (pathHistory) {
      return router.push(pathHistory);
    }

    router.push("/claim");
  };

  return (
    <ReactQueryProvider>
      <WagmiProvider config={config}>
        <PrivyProvider appId={PRIVY_APPID} config={privyConfig} onSuccess={handleWorldRedirect}>
          <MapProvider>
            <PrivyAuthProvider>{children}</PrivyAuthProvider>
          </MapProvider>
        </PrivyProvider>
      </WagmiProvider>
    </ReactQueryProvider>
  );
}
