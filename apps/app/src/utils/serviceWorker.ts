declare global {
  interface Window {
    workbox?: {
      routing: {
        registerRoute: any;
        NavigationRoute: any;
      };
      strategies: {
        NetworkOnly: any;
        NetworkFirst: any;
      };
      expiration: {
        ExpirationPlugin: any;
      };
      cacheableResponse: {
        CacheableResponsePlugin: any;
      };
      addEventListener: (event: string, callback: (event: any) => void) => void;
      register: () => void;
      skipWaiting: () => void;
    };
  }
}

export function registerServiceWorker() {
  if (typeof window !== "undefined" && "serviceWorker" in navigator && window.workbox !== undefined) {
    const wb = window.workbox;

    // Add error handling
    wb.addEventListener("error", (event: any) => {
      console.error("Service worker error:", event);
    });

    // Add message handling for skip waiting
    wb.addEventListener("message", (event: any) => {
      if (event.data.type === "SKIP_WAITING") {
        // Use navigator.serviceWorker instead
        navigator.serviceWorker.controller?.postMessage({ type: "SKIP_WAITING" });
      }
    });

    // Add offline fallback
    const offlineFallbackPage = "/offline";

    wb.routing.registerRoute(
      new RegExp("/*"),
      new wb.strategies.NetworkOnly({
        plugins: [
          new wb.routing.NavigationRoute(
            new wb.strategies.NetworkFirst({
              cacheName: "offline-cache",
              plugins: [
                new wb.expiration.ExpirationPlugin({
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
                }),
                new wb.cacheableResponse.CacheableResponsePlugin({
                  statuses: [0, 200],
                }),
                {
                  handlerDidError: async () => Response.redirect(offlineFallbackPage),
                },
              ],
            }),
          ),
        ],
      }),
    );

    // Add success/update/failure handlers
    wb.addEventListener("installed", (event: any) => {
      console.log(`Event ${event.type} is triggered.`);
      console.log(event);
    });

    wb.addEventListener("controlling", (event: any) => {
      console.log(`Event ${event.type} is triggered.`);
      console.log(event);
    });

    wb.addEventListener("activated", (event: any) => {
      console.log(`Event ${event.type} is triggered.`);
      console.log(event);
    });

    wb.register();
  }
}
