type StorageType = "local" | "session";
type StorageMethod = "create" | "update" | "remove" | "get";

export function handleStorage<T>(storageType: StorageType, key: string, method: StorageMethod, value?: T): T | null {
  const storage = storageType === "local" ? localStorage : sessionStorage;

  try {
    switch (method) {
      case "create":
      case "update":
        storage.setItem(key, JSON.stringify(value));
        break;

      case "remove":
        storage.removeItem(key);
        break;

      case "get":
        const item = storage.getItem(key);
        if (item) {
          return JSON.parse(item) as T;
        } else {
          return null;
        }
        break;
    }
  } catch (error) {
    console.error(error);
    return null;
  }

  return null;
}
