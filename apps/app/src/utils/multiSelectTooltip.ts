interface MultiSelectTooltipOptions {
  id: string;
  message: string;
  duration?: number;
  position?: {
    x?: number;
    y?: number;
  };
}

export const createMultiSelectTooltip = (options: MultiSelectTooltipOptions) => {
  const { id, message, duration = 3000, position } = options;

  const existingTooltip = document.getElementById(id);
  if (existingTooltip) {
    existingTooltip.remove();
  }

  const tooltip = document.createElement("div");
  tooltip.id = id;
  tooltip.style.position = "fixed";
  tooltip.style.backgroundColor = "rgba(0, 0, 0, 0.9)";
  tooltip.style.color = "white";
  tooltip.style.padding = "10px 15px";
  tooltip.style.borderRadius = "4px";
  tooltip.style.fontSize = "14px";
  tooltip.style.fontWeight = "bold";
  tooltip.style.zIndex = "9999";
  tooltip.style.maxWidth = "300px";
  tooltip.style.pointerEvents = "none";
  tooltip.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.5)";
  tooltip.style.border = "1px solid rgba(255, 255, 255, 0.2)";
  tooltip.innerHTML = message;

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  const x = position?.x ?? viewportWidth / 2 - 150;
  const y = position?.y ?? viewportHeight / 2 - 30;

  tooltip.style.left = `${x}px`;
  tooltip.style.top = `${y}px`;
  tooltip.style.transform = "translate(-50%, -50%)";

  document.body.appendChild(tooltip);

  tooltip.style.opacity = "0";
  tooltip.style.transition = "opacity 0.3s ease";

  setTimeout(() => {
    tooltip.style.opacity = "1";
    setTimeout(() => {
      tooltip.style.opacity = "0";
      setTimeout(() => tooltip.remove(), 300);
    }, duration);
  }, 100);
};

export const TOOLTIP_MESSAGES = {
  HYDROSHEDS: "💡 <strong>Pro tip:</strong> Hold CTRL (CMD) key to select multiple hydrosheds",
  SOILS: "💡 <strong>Pro tip:</strong> Hold CTRL (CMD) key to select multiple soils",
} as const;
