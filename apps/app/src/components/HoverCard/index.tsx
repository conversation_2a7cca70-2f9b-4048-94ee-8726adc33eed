import { AnimatePresence, motion } from "framer-motion";
import React from "react";

import IbiIcon from "../IbiUi/IbiIcon";

const HoverCard = ({
  children,
  title,
  isVisible = true,
}: {
  children: React.ReactNode;
  title?: string;
  isVisible?: boolean;
}) => {
  return (
    <div className="pointer-events-none" style={{ position: "relative", minHeight: isVisible ? "150px" : "0px" }}>
      <AnimatePresence mode="wait">
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
            }}
            className="absolute top-0 left-0 right-0 overflow-visible rounded-b-lg rounded-r-lg rounded-l-lg min-w-[320px] w-fit h-fit min-h-[150px] border border-[#ffffff15] bg-[#0c1033c4]"
          >
            <div className="rounded-lg absolute flex justify-between items-center -left-[3px] -top-2 w-[101.5%] h-[50px] bg-[#b4b0ff] header-card-clip-path text-black px-3">
              <div className="flex items-center gap-1 5">
                <IbiIcon icon="jam:qr-code" className="text-sm" />
                <h1 className="font-bold">{title}</h1>
              </div>
              <div className="flex items-center gap-0.5">
                <IbiIcon icon="tabler:square-filled" className="text-[0.5rem]" />
                <IbiIcon icon="tabler:square" className="text-[0.5rem]" />
                <IbiIcon icon="tabler:square" className="text-[0.5rem]" />
              </div>
            </div>
            <div className="p-4 mt-10">{children}</div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default HoverCard;
