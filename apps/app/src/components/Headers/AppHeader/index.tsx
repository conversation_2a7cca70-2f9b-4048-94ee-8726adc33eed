"use client";

import GoogleSearchPlace from "@/components/GoogleSearchInput";
import IbiIcon from "@/components/IbiUi/IbiIcon";
import useWeather from "@/hooks/useWeather";
import { cn } from "@/lib/utils";
import { ABC_WHITE_PLUS_BOLD, ABC_WHITE_PLUS_REGULAR } from "@/utils/configs";
import { usePathname } from "next/navigation";

export const AppHeader = () => {
  const pathname = usePathname();
  const {
    loadingWeather: loading,
    failedToFetchWeather,
    temperature,
    weatherIcon,
    currentAddress: address,
    currentTime,
  } = useWeather();

  return (
    <header
      className={cn("flex items-center justify-between px-3 w-full h-full", {
        "justify-end": pathname !== "/claim",
      })}
    >
      {pathname === "/claim" && (
        <div className="flex-1 flex justify-center items-center">
          <div className="w-[420px] ml-20">
            <GoogleSearchPlace />
          </div>
        </div>
      )}
      <div className="flex items-center gap-3 px-3">
        <div className="flex items-center gap-6">
          {address.data && (
            <div className="flex items-center gap-1.5 font-[700] [&>*]:text-[12px]">
              <p style={ABC_WHITE_PLUS_REGULAR.style}>
                {address.data[0]?.locality} {address.data[0]?.region_code},{" "}
              </p>
              <p style={ABC_WHITE_PLUS_REGULAR.style}>{address.data[0]?.country}</p>
            </div>
          )}
          {!failedToFetchWeather && (
            <>
              {loading || !temperature() ? (
                <IbiIcon icon="eos-icons:three-dots-loading" />
              ) : (
                <div className="flex items-center gap-1.5 text-[14px] font-[700]">
                  <IbiIcon icon={weatherIcon()} className="text-lg" />
                  <p style={ABC_WHITE_PLUS_BOLD.style}>{temperature()}</p>
                </div>
              )}
            </>
          )}
        </div>
        <div className="flex items-center gap-1">
          <IbiIcon icon="lets-icons:clock" className="text-sm" />
          <p style={ABC_WHITE_PLUS_BOLD.style} className="font-bold text-sm w-[55px]">
            {currentTime}
          </p>
        </div>
      </div>
    </header>
  );
};
