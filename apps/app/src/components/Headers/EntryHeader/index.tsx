"use client";

// import GoogleSearchPlace from "@/components/GoogleSearchInput";
// import IbiConfirmDialog from "@/components/IbiUi/IbiConfirmDialog";
import IbiIcon from "@/components/IbiUi/IbiIcon";
// import { Button } from "@/components/ui/button";
// import usePrivyAuth from "@/hooks/usePrivyAuth";
// import useWeather from "@/hooks/useWeather";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

// import { useAppHeaderSelector } from "./header.store";

export const EntryHeader = () => {
  // const { onLogout } = usePrivyAuth();
  // const {
  //   loadingWeather: loading,
  //   failedToFetchWeather,
  //   temperature,
  //   weatherIcon,
  //   currentAddress: address,
  //   currentTime,
  // } = useWeather();

  // const confirmLogout = useAppHeaderSelector.use.confirmLogout();
  // const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  // const { onLogin, authenticated } = usePrivyAuth();
  // const router = useRouter();
  const pathname = usePathname();

  return (
    <>
      <div className="flex justify-center items-center h-full gap-8">
        <Link
          href="/"
          className={cn("ml-16 flex items-center gap-1 text-xs w-[65px] relative justify-end hover:!opacity-100", {
            "opacity-50": pathname !== "/",
          })}
        >
          {pathname === "/" && <IbiIcon icon="ic:sharp-square" className="text-[7px] absolute left-0" />}
          PROJECT
        </Link>
        <Link
          href="/philosophy"
          className={cn("flex items-center gap-1 text-xs w-[85px] relative justify-end hover:!opacity-100", {
            "opacity-50": pathname !== "/philosophy",
          })}
        >
          {pathname === "/philosophy" && <IbiIcon icon="ic:sharp-square" className="text-[7px] absolute left-0" />}
          PHILOSOPHY
        </Link>
      </div>
      {/* <div className="h-full flex-1 flex relative">
        {pathname === "/" || pathname === "/philosophy" ? (
          <>
            <div className="flex-1 flex justify-center items-center gap-8 border-[#ffffff4b] border-t border-b">
              <Link
                href="/"
                className={cn("flex items-center gap-1 text-xs w-[65px] relative justify-end hover:!opacity-100", {
                  "opacity-50": pathname !== "/",
                })}
              >
                {pathname === "/" && <IbiIcon icon="ic:sharp-square" className="text-[7px] absolute left-0" />}
                PROJECT
              </Link>
              <Link
                href="/philosophy"
                className={cn("flex items-center gap-1 text-xs w-[85px] relative justify-end hover:!opacity-100", {
                  "opacity-50": pathname !== "/philosophy",
                })}
              >
                {pathname === "/philosophy" && (
                  <IbiIcon icon="ic:sharp-square" className="text-[7px] absolute left-0" />
                )}
                PHILOSOPHY
              </Link>
            </div>
            <hr className="w-[16px] border-[#ffffff4b] absolute bottom-0 right-[92px]" />
            <div className="btn-custom-clip-path bg-[#ffffff4b] hover:bg-[#ffffffad] transition-colors p-[1px]">
              {!authenticated ? (
                <Button
                  className="btn-custom-clip-path h-full px-8 bg-[#000] hover:bg-[#000] rounded-tr-none rounded-tl-none rounded-br-none"
                  onClick={onLogin}
                >
                  LOGIN
                </Button>
              ) : (
                <Button
                  className="btn-custom-clip-path h-full px-8 bg-[#000] hover:bg-[#000] rounded-tr-none rounded-tl-none rounded-br-none"
                  onClick={() => router.push("/claim")}
                >
                  ENTER WORLD
                </Button>
              )}
            </div>
          </>
        ) : (
          <header
            className={cn("flex items-center justify-between px-3 w-full", { "justify-end": pathname !== "/claim" })}
          >
            {pathname === "/claim" && (
              <div className="w-[600px]">
                <GoogleSearchPlace />
              </div>
            )}
            <div className="flex items-center gap-3 px-3">
              <div className="flex items-center gap-6">
                {address.data && (
                  <div className="flex items-center gap-1.5 font-[700] [&>*]:text-[12px]">
                    <p>
                      {address.data[0]?.locality} {address.data[0]?.region_code},{" "}
                    </p>
                    <p>{address.data[0]?.country}</p>
                  </div>
                )}
                {!failedToFetchWeather && (
                  <>
                    {loading || !temperature() ? (
                      <IbiIcon icon="eos-icons:three-dots-loading" />
                    ) : (
                      <div className="flex items-center gap-1.5 text-[16px] font-[700]">
                        <IbiIcon icon={weatherIcon()} className="text-lg" />
                        <p>{temperature()}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
              <div className="flex items-center gap-1">
                <IbiIcon icon="lets-icons:clock" className="text-sm" />
                <p className="font-bold text-sm w-[55px]">{currentTime}</p>
              </div>
            </div>
          </header>
        )}
      </div>
     */}
    </>
  );
};
