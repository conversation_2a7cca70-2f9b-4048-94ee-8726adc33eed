"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { cn } from "@/lib/utils";
import { ABC_WHITE_PLUS_BOLD } from "@/utils/configs";
// import dynamic from "next/dynamic";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

import { AnimatedButton } from "../AnimatedButton";
import EntryLoading from "../EntryLoading";
import { useAppHeaderSelector } from "../Headers/EntryHeader/header.store";
import IbiConfirmDialog from "../IbiUi/IbiConfirmDialog";
import IbiIcon from "../IbiUi/IbiIcon";

// const BackgroundBeams = dynamic(() => import("../ui/beans-bg"));

const AppWrapper = ({ children }: { children: React.ReactNode }) => {
  const { ready = false } = usePrivyAuth();
  const pathname = usePathname();
  const router = useRouter();
  const { confirmLogout: setConfirmLogout } = usePrivyAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const confirmLogout = useAppHeaderSelector.use.confirmLogout();
  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();
  const { onLogin, authenticated } = usePrivyAuth();

  const shouldRender = pathname === "/drop" || pathname === "/philosophy";

  return (
    <EntryLoading wrapperClass="h-full" condition={ready}>
      {/* <BackgroundBeams /> */}
      <div className="w-full h-full flex bg-primary-dark min-h-screen overflow-y-hidden overflow-x-hidden">
        {shouldRender && (
          <aside
            className={cn(
              "border-r border-[#ffffff1a] width-n-height-transition z-10 flex flex-col justify-between items-center max-[600px]:hidden",
              sidebarOpen ? "w-[160px]" : "w-[64px]",
            )}
            onMouseEnter={() => setSidebarOpen(true)}
            onMouseLeave={() => setSidebarOpen(false)}
          >
            <div className={cn("p-6 mb-6", !sidebarOpen && "px-4")}>
              <Link href="/drop" className="flex items-center gap-2">
                <img
                  src="https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581"
                  alt="logo"
                  className="size-[20px]"
                />
                {sidebarOpen && (
                  <span style={ABC_WHITE_PLUS_BOLD.style} className="text-sm">
                    YBYCASH
                  </span>
                )}
              </Link>
            </div>

            <div className="py-6 border-t border-[#ffffff1a] flex justify-center items-center w-full">
              <IbiIcon
                icon="icon-park-outline:pull-door"
                className="text-lg text-[#ffffff40] hover:text-white transition-colors cursor-pointer"
              />
            </div>
          </aside>
        )}

        <div className="flex-1 flex flex-col">
          <div className="flex-1 flex flex-col">
            {shouldRender && (
              <header
                className={cn(
                  "h-16 border-b border-[#ffffff1a] px-3 sm:px-6 flex items-center justify-between min-h-[64px]",
                )}
              >
                <div className="flex items-center gap-2">
                  <IbiIcon icon="ic:sharp-square" className="text-[7px]" />
                  <span className="text-xs">{pathname === "/drop" ? "1" : pathname === "/philosophy" ? "2" : "0"}</span>
                </div>

                <div className="flex-1 flex justify-center items-center gap-4 pl-[64px]">
                  <Link
                    href="/drop"
                    className={cn(
                      "text-sm opacity-50 hover:opacity-100 transition-opacity z-10",
                      pathname === "/drop" && "opacity-100",
                    )}
                  >
                    DROP
                  </Link>
                  <Link
                    href="/philosophy"
                    className={cn(
                      "text-sm opacity-50 hover:opacity-100 transition-opacity z-10",
                      pathname === "/philosophy" && "opacity-100",
                    )}
                  >
                    ABOUT
                  </Link>
                </div>

                <div className="ml-auto mt-2">
                  <AnimatedButton
                    onClick={authenticated ? () => router.push("/claim") : onLogin}
                    text={authenticated ? "ENTER WORLD" : "LOGIN"}
                    width={authenticated ? 170 : 100}
                    height={42}
                    className={authenticated ? "theme-dark" : ""}
                  />
                </div>
              </header>
            )}

            <div className="flex-1 h-full">{children}</div>
          </div>
        </div>
      </div>
      <IbiConfirmDialog
        title="Confirm logout"
        description="Are you sure you want to log out?"
        open={confirmLogout}
        onOpenChange={toggleConfirmLogout}
        onCancel={toggleConfirmLogout}
        onConfirm={() => {
          setConfirmLogout(true);
          toggleConfirmLogout();
        }}
      />
    </EntryLoading>
  );
};

export default AppWrapper;
