"use client";

import { useToast } from "@/hooks/useToast";
import { useEffect, useState } from "react";

export default function PWAUpdatePrompt() {
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (typeof window !== "undefined" && "serviceWorker" in navigator) {
      window.workbox?.addEventListener("waiting", (event: any) => {
        setWaitingWorker(event.sw);

        toast({
          title: "Update Available",
          description: "New version available. Click to update.",
          action: (
            <button
              onClick={() => {
                waitingWorker?.postMessage({ type: "SKIP_WAITING" });
                window.location.reload();
              }}
              className="bg-[#ffffff0d] hover:bg-[#ffffff1a] px-3 py-1 rounded-lg transition-colors"
            >
              Update
            </button>
          ),
          duration: 0,
        });
      });
    }
  }, [waitingWorker, toast]);

  return null;
}
