"use client";

import HydroMap from "@/components/HydroMap";
import IbiIcon from "@/components/IbiUi/IbiIcon";
import MapSideContent from "@/components/IbiUi/IbiMap/MapSideContent";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useForestHistoryHydrosheds } from "@/hooks/useForestHistoryHydrosheds";
import { useForestHistorySoils } from "@/hooks/useForestHistorySoils";
import { useSoilsManagementStore } from "@/hooks/useLayerControl/useSoils/store";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import React, { useEffect, useState } from "react";

import AnimalsMap from "./AnimalsMap";
import SoilsMap from "./SoilsMap";

interface BondMapContainerProps {
  className?: string;
}

const BondMapContainer: React.FC<BondMapContainerProps> = ({ className }) => {
  const [selectedTab, setSelectedTab] = useState("carbon");

  const { fetchForestHistoryHydroshed } = useForestHistoryHydrosheds();
  const { fetchForestHistorySoils } = useForestHistorySoils();

  const selectedHydroshedId = useMapSelector.use.selectedHydroshedId();
  // const selectedSoilsId = useMapSelector.use.selectedSoilsId();
  const setPadding = useMapSelector.use.setPadding();

  const { selectedSoilId } = useSoilsManagementStore();

  useEffect(() => {
    setPadding({
      right: 426,
      top: 56,
      bottom: 0,
      left: 0,
    });
  }, [setPadding]);

  useEffect(() => {
    if (selectedTab === "carbon" && selectedHydroshedId) {
      fetchForestHistoryHydroshed(selectedHydroshedId.toString());
    }
  }, [selectedTab, selectedHydroshedId, fetchForestHistoryHydroshed]);

  useEffect(() => {
    if (selectedTab === "soils" && selectedSoilId) {
      fetchForestHistorySoils(selectedSoilId.toString());
    }
  }, [selectedTab, selectedSoilId, fetchForestHistorySoils]);

  const tabsData = [
    {
      id: "carbon",
      label: "Carbon",
      icon: "material-symbols:eco",
      color: "bg-green-500/20 text-green-400 border-green-500/30",
    },
    // {
    //   id: "biodiversity",
    //   label: "Biodiversity",
    //   icon: "material-symbols:nature-people",
    //   color: "bg-blue-500/20 text-blue-400 border-blue-500/30",
    // },
    {
      id: "soils",
      label: "Soils",
      icon: "material-symbols:landscape",
      color: "bg-amber-500/20 text-amber-400 border-amber-500/30",
    },
  ];

  const mapData = {
    carbon: {
      metrics: [
        { label: "Price", value: "$12,458" },
        { label: "Total Volume", value: "$1,224.50" },
        { label: "Owners", value: "305,221" },
      ],
    },
    biodiversity: {
      metrics: [
        { label: "Price", value: "$1,458" },
        { label: "Total Volume", value: "$61,624.50" },
        { label: "Owners", value: "777" },
      ],
    },
    soils: {
      metrics: [
        { label: "Price", value: "$58" },
        { label: "Total Volume", value: "$224.50" },
        { label: "Owners", value: "12" },
      ],
    },
  };

  const currentData = mapData[selectedTab as keyof typeof mapData];

  const renderMap = () => {
    switch (selectedTab) {
      case "carbon":
        return <HydroMap />;
      case "biodiversity":
        return <AnimalsMap />;
      case "soils":
        return <SoilsMap />;
      default:
        return <HydroMap />;
    }
  };

  const getCurrentMapType = (): "default" | "biome" | "hydro" => {
    switch (selectedTab) {
      case "carbon":
        return "hydro";
      case "soils":
        return "default";
      default:
        return "default";
    }
  };

  return (
    <div className={cn("w-full h-screen relative", className)}>
      <div className="absolute inset-0">{renderMap()}</div>

      <div className="absolute top-16 left-[75px] z-10">
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-auto">
          <TabsList className="bg-black/80 backdrop-blur-md border border-[#ffffff2a] p-0">
            {tabsData.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 text-sm font-medium transition-all",
                  "data-[state=active]:bg-[#ffffff1a] data-[state=active]:text-white",
                  "text-white/70 hover:text-white hover:bg-[#ffffff0a]",
                )}
              >
                <IbiIcon icon={tab.icon} className="w-4 h-4" />
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      <div className="absolute bottom-16 left-20 z-20">
        <div className="bg-black/85 backdrop-blur-md border border-[#ffffff2a] rounded-lg p-4">
          <div className="flex items-center gap-8">
            <div
              className={cn(
                "p-2 rounded-lg border flex items-center justify-center",
                tabsData.find((tab) => tab.id === selectedTab)?.color,
              )}
            >
              <IbiIcon
                icon={tabsData.find((tab) => tab.id === selectedTab)?.icon || "material-symbols:eco"}
                className="w-5 h-5"
              />
            </div>

            {currentData.metrics.map((metric, index) => (
              <div key={index} className="flex flex-col items-center">
                <span className="text-xs text-gray-400 mb-1">{metric.label}</span>
                <span className="text-lg font-semibold text-white">{metric.value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="fixed right-0 top-0 min-[750px]:top-14 bottom-0 overflow-visible z-30">
        <aside
          className={cn(
            "w-[280px] min-[440px]:w-[426px] bg-[#01050dD9] backdrop-blur-md border-r border-r-[#CFD4F733] transition-all duration-300 ease-in-out",
          )}
        >
          <div className="h-full overflow-auto">
            <MapSideContent currentMapType={getCurrentMapType()} />
          </div>
        </aside>
      </div>
    </div>
  );
};

export default BondMapContainer;
