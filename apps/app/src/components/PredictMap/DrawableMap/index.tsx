"use client";

import { MAPBOX_TOKEN } from "@/utils/constants";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import "@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css";
import "mapbox-gl/dist/mapbox-gl.css";
import React, { useCallback, useRef } from "react";
import Map, { MapRef } from "react-map-gl";

import { PolygonFeature } from "../hooks/usePolygonDraw";

interface DrawableMapProps {
  onPolygonCreate: (feature: PolygonFeature) => void;
  onPolygonUpdate: (feature: PolygonFeature) => void;
  onPolygonDelete: () => void;
  onDrawControlsAdd: (draw: MapboxDraw) => void;
}

const DrawableMap: React.FC<DrawableMapProps> = ({
  onPolygonCreate,
  onPolygonUpdate,
  onPolygonDelete,
  onDrawControlsAdd,
}) => {
  const mapRef = useRef<MapRef>(null);
  const drawRef = useRef<MapboxDraw | null>(null);

  const handleMapLoad = useCallback(() => {
    console.log("Map loaded - initializing draw controls");

    if (!mapRef.current) {
      console.error("mapRef.current is null");
      return;
    }

    const map = mapRef.current.getMap();
    console.log("Map instance:", map);

    const draw = new MapboxDraw({
      displayControlsDefault: false,
      defaultMode: "simple_select",
    });

    console.log("Draw instance created:", draw);
    drawRef.current = draw;

    map.addControl(draw, "top-left");
    console.log("Draw control added to map");

    onDrawControlsAdd(draw);
    console.log("Draw controls passed to parent");

    const handleDrawCreate = (e: any) => {
      console.log("Draw create event:", e);
      const feature = e.features[0];
      if (!feature) return;

      const allFeatures = draw.getAll();
      const idsToDelete = allFeatures.features
        .map((f) => f.id)
        .filter((id): id is string => id !== feature.id && id !== undefined);

      if (idsToDelete.length > 0) {
        draw.delete(idsToDelete);
      }

      const polygonFeature: PolygonFeature = {
        id: feature.id,
        geometry: {
          type: "Polygon",
          coordinates: feature.geometry.coordinates,
        },
        properties: {
          polygon_id: crypto.randomUUID(),
          area_ha: 0,
          datetime: "",
        },
      };

      onPolygonCreate(polygonFeature);
    };

    const handleDrawUpdate = (e: any) => {
      console.log("Draw update event:", e);
      const feature = e.features[0];
      if (!feature) return;

      const polygonFeature: PolygonFeature = {
        id: feature.id,
        geometry: {
          type: "Polygon",
          coordinates: feature.geometry.coordinates,
        },
        properties: {
          polygon_id: feature.properties?.polygon_id || crypto.randomUUID(),
          area_ha: 0,
          datetime: feature.properties?.datetime || "",
        },
      };

      onPolygonUpdate(polygonFeature);
    };

    const handleDrawDelete = () => {
      console.log("Draw delete event");
      onPolygonDelete();
    };

    map.on("draw.create", handleDrawCreate);
    map.on("draw.update", handleDrawUpdate);
    map.on("draw.delete", handleDrawDelete);

    return () => {
      console.log("Cleaning up draw controls");
      map.off("draw.create", handleDrawCreate);
      map.off("draw.update", handleDrawUpdate);
      map.off("draw.delete", handleDrawDelete);

      if (drawRef.current) {
        map.removeControl(drawRef.current);
        drawRef.current = null;
      }
    };
  }, [onPolygonCreate, onPolygonUpdate, onPolygonDelete, onDrawControlsAdd]);

  return (
    <div className="w-full h-full">
      <Map
        ref={mapRef}
        mapboxAccessToken={MAPBOX_TOKEN}
        onLoad={handleMapLoad}
        initialViewState={{
          longitude: -46.6333,
          latitude: -23.5505,
          zoom: 11,
        }}
        style={{ width: "100%", height: "100%" }}
        mapStyle="mapbox://styles/mapbox/satellite-streets-v12"
        attributionControl={false}
      />
    </div>
  );
};

export default DrawableMap;
