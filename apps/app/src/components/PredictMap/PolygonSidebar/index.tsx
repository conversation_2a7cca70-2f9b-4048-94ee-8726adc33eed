"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Copy } from "lucide-react";
import React, { useState } from "react";

import { PolygonFeature } from "../hooks/usePolygonDraw";

interface PolygonSidebarProps {
  polygonFeature: PolygonFeature | null;
  eeFeatureCode: string;
  maxArea?: number;
}

const PolygonSidebar: React.FC<PolygonSidebarProps> = ({ polygonFeature, eeFeatureCode, maxArea = 15000 }) => {
  const [copied, setCopied] = useState(false);

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(eeFeatureCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
      const textArea = document.createElement("textarea");
      textArea.value = eeFeatureCode;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand("copy");
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Fallback copy failed: ", err);
      }
      document.body.removeChild(textArea);
    }
  };

  const isAreaTooLarge = polygonFeature ? polygonFeature.properties.area_ha > maxArea : false;

  return (
    <div className="fixed top-1/2 right-4 transform -translate-y-1/2 w-96 h-[calc(100vh-120px)] text-xs rounded-xl bg-black/85 backdrop-blur-xl border-2 border-gray-700 shadow-lg p-4 pr-0 z-10">
      <div className="flex items-center justify-between pr-4">
        <h1 className="text-[14px] font-bold">Polygon to GEE Feature</h1>
        <div className="flex flex-col items-center bg-green-900/50 text-green-400 rounded-lg px-2 py-0.5">
          <span>Status</span>
          <div className="flex items-center gap-1">
            <span>{polygonFeature ? "Ready" : "Waiting"}</span>
          </div>
        </div>
      </div>

      <h2 className="text-xs text-gray-400 font-bold mt-3 uppercase">Overview</h2>
      <p className="text-gray-400 mb-4 mt-2">
        Use the drawing tools on the map to create a polygon. The GEE code will appear below.
      </p>

      {isAreaTooLarge && (
        <div className="mb-4 p-3 bg-red-900/30 border border-red-700/50 rounded-lg">
          <p className="text-red-400 text-xs">
            <strong>Warning:</strong> Area too large. Must be below {maxArea.toLocaleString()} ha.
          </p>
        </div>
      )}

      <div className="h-[calc(100%-200px)] overflow-y-auto pr-4">
        <div className="space-y-4">
          <h2 className="text-xs text-gray-400 font-bold uppercase">GEE Feature Constructor</h2>

          {polygonFeature ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-green-400 font-semibold text-sm">ee.Feature Ready!</span>
              </div>

              <div className="p-4 bg-[#0a0e15] border border-gray-800/30 rounded-lg space-y-3">
                <h3 className="text-xs text-gray-400 font-bold uppercase">Polygon Details</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-400 uppercase">Area (HA)</span>
                    <div className="text-lg font-bold text-white">{polygonFeature.properties.area_ha.toFixed(2)}</div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-400 uppercase">Status</span>
                    <div className={`text-sm font-bold ${isAreaTooLarge ? "text-red-400" : "text-green-400"}`}>
                      {isAreaTooLarge ? "Too Large" : "Valid"}
                    </div>
                  </div>
                </div>

                <div>
                  <span className="text-xs text-gray-400 uppercase">Polygon ID</span>
                  <div className="text-sm font-mono text-gray-300 truncate">{polygonFeature.properties.polygon_id}</div>
                </div>

                <div>
                  <span className="text-xs text-gray-400 uppercase">DateTime</span>
                  <div className="text-sm font-mono text-gray-300">{polygonFeature.properties.datetime}</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-xs text-gray-400 font-bold uppercase">GEE Code</h3>
                  <Button
                    onClick={handleCopyCode}
                    className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 text-xs"
                    size="sm"
                  >
                    {copied ? (
                      <>
                        <Check className="h-3 w-3 mr-1" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </>
                    )}
                  </Button>
                </div>

                <div className="relative">
                  <pre className="bg-gray-900 text-white rounded-md p-4 text-xs overflow-x-auto font-mono whitespace-pre-wrap border border-gray-700">
                    {eeFeatureCode}
                  </pre>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="w-12 h-12 border-2 border-gray-600 border-dashed rounded-lg mx-auto mb-4 flex items-center justify-center">
                <div className="w-4 h-4 bg-gray-600 rounded"></div>
              </div>
              <p className="text-gray-500 text-sm">Awaiting polygon creation...</p>
              <p className="text-gray-600 text-xs mt-2">Click the &quot;Polygon&quot; button to start drawing</p>
            </div>
          )}
        </div>
      </div>

      {polygonFeature && (
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-700 rounded-b-xl">
          <Button
            className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3"
            disabled={isAreaTooLarge}
          >
            Predict
          </Button>
          {isAreaTooLarge && (
            <p className="text-red-400 text-xs mt-2 text-center">Cannot predict: area exceeds limit</p>
          )}
        </div>
      )}
    </div>
  );
};

export default PolygonSidebar;
