"use client";

import MapboxDraw from "@mapbox/mapbox-gl-draw";
import React, { useCallback, useState } from "react";

import DrawControls from "./DrawControls";
import DrawableMap from "./DrawableMap";
import PolygonSidebar from "./PolygonSidebar";
import { PolygonFeature, usePolygonDraw } from "./hooks/usePolygonDraw";

interface PredictMapProps {
  className?: string;
}

const PredictMap: React.FC<PredictMapProps> = ({ className }) => {
  const [drawControls, setDrawControls] = useState<MapboxDraw | null>(null);
  const { polygonFeature, setPolygonFeature, calculateArea, formatTimestamp, clearPolygon, generateEeFeatureCode } =
    usePolygonDraw();

  const handlePolygonCreate = useCallback(
    (feature: PolygonFeature) => {
      const area = calculateArea(feature.geometry);
      const updatedFeature: PolygonFeature = {
        ...feature,
        properties: {
          ...feature.properties,
          area_ha: parseFloat(area.toFixed(4)),
          datetime: formatTimestamp(),
        },
      };
      setPolygonFeature(updatedFeature);
    },
    [calculateArea, formatTimestamp, setPolygonFeature],
  );

  const handlePolygonUpdate = useCallback(
    (feature: PolygonFeature) => {
      const area = calculateArea(feature.geometry);
      const updatedFeature: PolygonFeature = {
        ...feature,
        properties: {
          ...feature.properties,
          area_ha: parseFloat(area.toFixed(4)),
        },
      };
      setPolygonFeature(updatedFeature);
    },
    [calculateArea, setPolygonFeature],
  );

  const handlePolygonDelete = useCallback(() => {
    clearPolygon();
  }, [clearPolygon]);

  const handleDrawControlsAdd = useCallback((draw: MapboxDraw) => {
    setDrawControls(draw);
  }, []);

  const handleDeleteFromSidebar = useCallback(() => {
    if (drawControls) {
      drawControls.deleteAll();
    }
    clearPolygon();
  }, [drawControls, clearPolygon]);

  const eeFeatureCode = polygonFeature ? generateEeFeatureCode(polygonFeature) : "";

  return (
    <div className={`w-full h-screen relative ${className || ""}`}>
      {/* Map Container - Full Width */}
      <DrawableMap
        onPolygonCreate={handlePolygonCreate}
        onPolygonUpdate={handlePolygonUpdate}
        onPolygonDelete={handlePolygonDelete}
        onDrawControlsAdd={handleDrawControlsAdd}
      />

      {/* Draw Controls */}
      <DrawControls draw={drawControls} hasPolygon={!!polygonFeature} onDeletePolygon={handleDeleteFromSidebar} />

      {/* Sidebar - Overlaid */}
      <PolygonSidebar polygonFeature={polygonFeature} eeFeatureCode={eeFeatureCode} />

      <div
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.1) 15%, rgba(0,0,0,0.1) 85%, rgba(0,0,0,0.9) 100%)",
          zIndex: 10,
        }}
      />
    </div>
  );
};

export default PredictMap;
