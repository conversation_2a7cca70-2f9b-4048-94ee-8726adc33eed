"use client";

import PropertyDetailsModal from "@/components/Bookmark/PropertyDetailsModal";
import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useCollections } from "@/queries/hooks/useCollections";
import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";

function CollectionsSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <Card key={i} className="bg-[#010303]/70 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg w-10 h-10 animate-pulse" />
              <div className="space-y-2">
                <div className="h-5 w-32 bg-gray-700 rounded animate-pulse" />
                <div className="h-4 w-24 bg-gray-700/50 rounded animate-pulse" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function CollectionsError() {
  return (
    <Card className="bg-red-500/10 border-red-500/30">
      <CardContent className="p-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-red-500/20 rounded-full">
            <IbiIcon icon="ph:warning-circle" className="text-2xl text-red-400" />
          </div>
          <div>
            <h3 className="font-medium text-red-400">Failed to load collections</h3>
            <p className="text-sm text-red-300/70">Please try refreshing the page or try again later.</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function EmptyCollections() {
  return (
    <Card className="bg-[#010303]/70 border-gray-800">
      <CardContent className="p-8 text-center">
        <div className="flex flex-col items-center gap-4">
          <div className="p-4 bg-blue-500/10 rounded-full">
            <IbiIcon icon="ph:bookmark-simple" className="text-4xl text-blue-400" />
          </div>
          <div>
            <h3 className="font-medium text-white text-lg">No Collections Found</h3>
            <p className="text-sm text-gray-400 mt-1">Start by creating your first collection</p>
          </div>
          <Button variant="outline" className="mt-4" href="/claim">
            <IbiIcon icon="ph:plus" className="mr-2" />
            Create Collection
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export function LandUnitsCollections() {
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [selectedProperty, setSelectedProperty] = useState<string | null>(null);
  const { collections } = useCollections();

  if (collections.isLoading) {
    return <CollectionsSkeleton />;
  }

  if (collections.isError) {
    return <CollectionsError />;
  }

  const generalCollection = collections.data?.data.find((c) => c.name === "General");
  const otherCollections = collections.data?.data.filter((c) => c.name !== "General") || [];

  if (!generalCollection && otherCollections.length === 0) {
    return <EmptyCollections />;
  }

  return (
    <>
      {generalCollection && (
        <div className="mb-8">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <Card
              className={`bg-[#010303]/70 border-gray-800 cursor-pointer transition-all duration-200 hover:bg-[#010303]/80 ${
                selectedCollection === generalCollection.id ? "ring-2 ring-blue-500/50" : ""
              }`}
              onClick={() =>
                setSelectedCollection(generalCollection.id === selectedCollection ? null : generalCollection.id)
              }
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500/20 rounded-lg">
                      <IbiIcon icon="ph:bookmark-simple" className="text-xl text-blue-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">{generalCollection.name}</h3>
                      <p className="text-sm text-gray-400">
                        {generalCollection.properties.length} property
                        {generalCollection.properties.length !== 1 ? "ies" : ""}
                      </p>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                    <IbiIcon
                      icon={selectedCollection === generalCollection.id ? "ph:caret-up" : "ph:caret-down"}
                      className="text-xl"
                    />
                  </Button>
                </div>

                {generalCollection.description && (
                  <p className="text-sm text-gray-400 mb-4">{generalCollection.description}</p>
                )}

                <AnimatePresence>
                  {selectedCollection === generalCollection.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-3 mt-4 pt-4 border-t border-gray-800"
                    >
                      {generalCollection.properties.length === 0 ? (
                        <p className="text-center text-gray-500 py-4">No properties yet</p>
                      ) : (
                        generalCollection.properties.map((property) => (
                          <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            key={property.id}
                            className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                                <IbiIcon icon="ph:tree" className="text-2xl text-gray-400" />
                              </div>
                              <div>
                                <h4 className="font-medium text-white">Property</h4>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedProperty(property.id);
                              }}
                            >
                              <IbiIcon icon="ph:info" className="text-xl" />
                            </Button>
                          </motion.div>
                        ))
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {otherCollections.map((collection) => (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} key={collection.id}>
            <Card
              className={`bg-[#010303]/50 border-gray-800 cursor-pointer transition-all duration-200 hover:bg-[#010303]/70 ${
                selectedCollection === collection.id ? "ring-2 ring-blue-500/50" : ""
              }`}
              onClick={() => setSelectedCollection(collection.id === selectedCollection ? null : collection.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500/10 rounded-lg">
                      <IbiIcon
                        icon={collection.isPrivate ? "ph:lock-simple" : "ph:bookmark-simple"}
                        className="text-xl text-blue-400"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">{collection.name}</h3>
                      <p className="text-sm text-gray-400">
                        {collection.properties.length} property{collection.properties.length !== 1 ? "ies" : ""}
                      </p>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                    <IbiIcon
                      icon={selectedCollection === collection.id ? "ph:caret-up" : "ph:caret-down"}
                      className="text-xl"
                    />
                  </Button>
                </div>

                {collection.description && <p className="text-sm text-gray-400 mb-4">{collection.description}</p>}

                <AnimatePresence>
                  {selectedCollection === collection.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-3 mt-4 pt-4 border-t border-gray-800"
                    >
                      {collection.properties.length === 0 ? (
                        <p className="text-center text-gray-500 py-4">No properties yet</p>
                      ) : (
                        collection.properties.map((property) => (
                          <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            key={property.id}
                            className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                                <IbiIcon icon="ph:tree" className="text-2xl text-gray-400" />
                              </div>
                              <div>
                                <h4 className="font-medium text-white">Property</h4>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedProperty(property.id);
                              }}
                            >
                              <IbiIcon icon="ph:info" className="text-xl" />
                            </Button>
                          </motion.div>
                        ))
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <PropertyDetailsModal
        propertyId={selectedProperty}
        collectionId={selectedCollection}
        onClose={() => setSelectedProperty(null)}
      />
    </>
  );
}
