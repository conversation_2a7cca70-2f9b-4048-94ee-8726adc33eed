"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";

export default function FloatingCard({
  children,
  topHeaderStart,
  topHeaderEnd,
}: Readonly<{
  children: React.ReactNode;
  topHeaderStart?: React.ReactNode;
  topHeaderEnd?: React.ReactNode;
}>) {
  return (
    <div className="flex-1 relative">
      <div className="z-10 absolute w-full p-4">
        <div className="w-full flex">
          <div className="min-h-[51px] border-[#ffffff4b] border h-full flex justify-center items-center min-w-[67px] hover:border-[#ffffffad] rounded-tl-xl">
            {topHeaderStart}
          </div>
          <div className="flex-1 border-[#ffffff4b] border-r border-t border-b min-h-[51px] rounded-tr-xl">
            {topHeaderEnd}
          </div>
        </div>
      </div>
      <div className="z-10 absolute left-0 top-[67px] pl-4 w-[67px] h-[92%]">
        <div className="min-w-[67px] h-full border-[#ffffff4b] border-r border-l flex justify-center items-end">
          <IbiIcon icon="mynaui:dots" />
        </div>
      </div>
      <div className="z-10 absolute right-0 top-[67px] pr-4 w-[90px] h-[92%]">
        <hr className="h-full border-[#ffffff4b] border-r border-t-0" />
      </div>
      <div className="z-10 absolute bottom-[10px] left-0 right-0 px-4">
        <hr className="border-t border-[#ffffff4b]" />
      </div>
      {children}
    </div>
  );
}
