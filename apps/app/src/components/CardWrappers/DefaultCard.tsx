"use client";

import IbiIcon from "../IbiUi/IbiIcon";

export default function DefaultCard({
  children,
  topHeaderStart,
  topHeaderEnd,
}: {
  children: React.ReactNode;
  topHeaderStart?: React.ReactNode;
  topHeaderEnd?: React.ReactNode;
}) {
  return (
    <div className="h-screen p-4">
      <div className="h-full flex flex-col min-h-0">
        <div className="flex h-[51px] min-h-[51px]">
          <div className="border-[#ffffff4b] border h-full flex justify-center items-center w-[67px] hover:border-[#ffffffad] rounded-tl-xl">
            {topHeaderStart}
          </div>
          <div className="flex-1 flex h-full relative">{topHeaderEnd}</div>
        </div>
        <div className="flex-1 flex min-h-0">
          <div className="w-[67px] border-[#ffffff4b] border-r border-l border-b rounded-bl-xl flex justify-center items-end pb-2">
            <IbiIcon icon="mynaui:dots" />
          </div>
          <div className="flex-1 border-[#ffffff4b] border-r border-b overflow-y-hidden relative rounded-br-xl">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
