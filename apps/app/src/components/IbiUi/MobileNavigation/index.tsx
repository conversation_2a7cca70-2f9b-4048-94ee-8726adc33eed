import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { cn } from "@/lib/utils";
import { navigationOptions } from "@/utils/constants";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

import IbiIcon from "../IbiIcon";

const MobileNavigation = () => {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const handleNavigate = (path: string) => {
    router.push(path);
    setOpen(false);
  };

  return (
    <>
      <header
        className={cn(
          "z-20 fixed top-0 left-0 right-0 h-9 backdrop-blur-md flex items-center justify-center min-[750px]:hidden border-b border-[#ffffff11]",
          pathname === "/claim" ? "bg-[#01050dD9]" : "bg-[#010303]",
        )}
      >
        <div className="border-b border-[#ffffff11] rounded-full border-l-transparent border-r-transparent border-t-transparent w-fit h-fit">
          <img
            src="https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581"
            alt="logo"
            className="size-12 cursor-pointer border-[7px] border-b-[#01050dD9] border-l-transparent border-r-transparent border-t-transparent rounded-full"
            onClick={() => router.push("/claim")}
          />
        </div>
      </header>

      <nav className="fixed bottom-0 left-0 right-0 bg-[#01050dD9] backdrop-blur-md border-t border-[#ffffff11] h-14 flex items-center justify-between px-4 min-[750px]:hidden z-10">
        {navigationOptions.slice(0, 4).map((option) => (
          <button
            key={option.pathname}
            onClick={() => handleNavigate(option.pathname)}
            className={cn(
              "flex flex-col items-center gap-1",
              pathname === option.pathname ? "text-white" : "text-gray-400 hover:text-gray-300",
            )}
          >
            <IbiIcon icon={option.icon} className="text-xl" />
            <span className="text-[10px]">{option.label}</span>
          </button>
        ))}
        <button
          onClick={() => setOpen(true)}
          className="flex flex-col items-center gap-1 text-gray-400 hover:text-gray-300"
        >
          <IbiIcon icon="lucide:menu" className="text-xl" />
          <span className="text-[10px]">More</span>
        </button>
      </nav>

      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="bg-[#01050dD9] backdrop-blur-md border-t border-[#ffffff11]">
          <div className="p-4">
            <div className="grid grid-cols-4 gap-4">
              {navigationOptions.map((option) => (
                <div key={option.pathname}>
                  <button
                    onClick={() => handleNavigate(option.pathname)}
                    className={cn(
                      "w-full flex flex-col items-center gap-2 p-3 rounded-lg",
                      pathname === option.pathname
                        ? "bg-[#ffffff11] text-white"
                        : "text-gray-400 hover:bg-[#ffffff08] hover:text-gray-300",
                    )}
                  >
                    <IbiIcon icon={option.icon} className="text-2xl" />
                    <span className="text-xs">{option.label}</span>
                  </button>

                  {option.subpaths && pathname === option.pathname && (
                    <div className="mt-2 space-y-1">
                      {option.subpaths.map((subpath) => (
                        <button
                          key={subpath.pathname}
                          onClick={() => handleNavigate(subpath.pathname)}
                          className={cn(
                            "w-full flex items-center gap-2 px-3 py-2 rounded-lg text-xs",
                            pathname === subpath.pathname
                              ? "bg-[#ffffff11] text-white"
                              : "text-gray-400 hover:bg-[#ffffff08] hover:text-gray-300",
                          )}
                        >
                          <IbiIcon icon={subpath.icon} className="text-sm" />
                          {subpath.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default MobileNavigation;
