import useMap from "@/hooks/useMap";
import { cn } from "@/lib/utils";
import { MAPBOX_TOKEN } from "@/utils/constants";
import throttle from "lodash/throttle";
import React, { useCallback } from "react";
import Map, { type ViewStateChangeEvent } from "react-map-gl";

const MiniMap = () => {
  const { mapRef, minimapRef } = useMap();

  const handleMove = useCallback(
    throttle((e: ViewStateChangeEvent) => {
      if (mapRef?.current) {
        const view = e.viewState;
        mapRef.current.jumpTo({ center: [view.longitude, view.latitude] });
      }
    }, 100),
    [mapRef],
  );

  return (
    <div
      className={cn(
        "absolute bottom-12 min-[750px]:bottom-0 left-0 mb-7 ml-4 min-[750px]:ml-[75px] border border-[#ffffff11] overflow-hidden rounded-lg size-[80px] max-[750px]:hidden",
      )}
    >
      <Map
        ref={minimapRef}
        mapStyle={"mapbox://styles/geodatin/clb2nnu14000o15lhqypivuce?optimize=true"} // Performance: Use optimized style
        initialViewState={{
          latitude: -14.72,
          longitude: -51.419,
          zoom: 1.5,
        }}
        mapboxAccessToken={MAPBOX_TOKEN}
        projection={{
          name: "mercator",
        }}
        interactiveLayerIds={["properties"]}
        style={{ width: "100%", height: "100%" }}
        onMove={handleMove}
        renderWorldCopies={false}
        maxZoom={4}
        minZoom={1}
        attributionControl={false}
        preserveDrawingBuffer={false}
        scrollZoom={false}
        boxZoom={false}
        dragRotate={false}
        touchPitch={false}
        cooperativeGestures={true}
        antialias={false}
      />
    </div>
  );
};

export default MiniMap;
