import NewBookmark from "@/components/Bookmark/NewBookmark";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import useCode from "@/hooks/useCode";
import { usePropertyManagementStore } from "@/hooks/useLayerControl/useProperties/store";
import useMap from "@/hooks/useMap";
import { toast } from "@/hooks/useToast";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import { CurrentBaseMap } from "@/types";
import React, { useCallback, useState } from "react";

import IbiIcon from "../../IbiIcon";

const MapControllers = () => {
  const { mapRef, currentBaseMap, setCurrentBaseMap } = useMap();
  const { selectedPropertyDetails } = usePropertyManagementStore();
  const [copySuccess, setCopySuccess] = useState(false);
  const { inviteCode } = useCode();
  const triggerPropertiesReload = useMapSelector.use.triggerPropertiesReload();

  const handleCopyIbiCode = async () => {
    if (selectedPropertyDetails?.ibiCode) {
      try {
        const baseUrl = `${window.location.origin}/p/${selectedPropertyDetails.ibiCode}`;
        const inviteCodeValue = inviteCode?.code || null;
        const finalUrl = inviteCodeValue ? `${baseUrl}?rc=${inviteCodeValue}` : baseUrl;

        await navigator.clipboard.writeText(finalUrl);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
        toast({
          title: "Property Copied",
          description: "You can now share your property with others.",
          position: "bottom",
          margin: {
            right: 430,
            bottom: 0,
            left: 0,
          },
          exitDirection: "bottom",
        });
      } catch (err) {
        console.error("Failed to copy:", err);
      }
    }
  };

  const handleZoom = useCallback(
    (direction) => {
      if (!mapRef?.current) return;

      const currentZoom = mapRef.current.getZoom();
      const newZoom = direction === "in" ? currentZoom + 1 : currentZoom - 1;

      const minZoom = 1;
      const maxZoom = 20;

      if (newZoom >= minZoom && newZoom <= maxZoom) {
        mapRef.current.flyTo({
          zoom: newZoom,
          duration: 500,
        });
      }
    },
    [mapRef],
  );

  const navigateToInitialPosition = () => {
    if (!mapRef?.current) return;
    mapRef.current.flyTo({
      center: [-61.7299934555, 1],
      zoom: 1.5,
      duration: 500,
    });
  };

  const navigateToNorthPole = () => {
    if (!mapRef?.current) return;
    mapRef.current.flyTo({
      center: [0, 0],
      zoom: 1.5,
      duration: 500,
    });
  };

  const baseMapOptions = [
    {
      id: 1,
      label: "Gray Scale",
      mapState: "dark" as CurrentBaseMap,
    },
    {
      id: 2,
      label: "Streets",
      mapState: "streets" as CurrentBaseMap,
    },
    {
      id: 3,
      label: "Outdoors",
      mapState: "outdoors" as CurrentBaseMap,
    },
    {
      id: 4,
      label: "Light",
      mapState: "light" as CurrentBaseMap,
    },
    {
      id: 5,
      label: "Navigation Day",
      mapState: "navigation-day" as CurrentBaseMap,
    },
    {
      id: 6,
      label: "Navigation Night",
      mapState: "navigation-night" as CurrentBaseMap,
    },
  ];

  return (
    <>
      <div className="absolute -top-[30px] sm:-top-[10px] left-0 mt-[140px] ml-3 sm:ml-[75px] flex flex-col gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <button className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-black/80 backdrop-blur-md flex items-center justify-center px-2 gap-2">
              <IbiIcon icon="mi:map" className="text-xl" />
              Base Map
            </button>
          </PopoverTrigger>
          <PopoverContent
            sideOffset={12}
            side="right"
            align="start"
            className="w-[298px] rounded-none border-none p-[1px] bg-[#ffffff2f] layers-box-clip-path relative text-white"
          >
            <div className="layers-box-clip-path bg-[#0A0E15] size-full px-4 pb-6 pt-5">
              <h1 className="text-gray-400 text-[12px]">Base Map</h1>
              <div className="mt-3 flex flex-col gap-2">
                <div
                  className={cn(
                    "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                    currentBaseMap === "default" && "bg-[#ffffff2f]",
                  )}
                  onClick={() => {
                    setCurrentBaseMap("default");
                    setTimeout(() => {
                      triggerPropertiesReload();
                    }, 500);
                  }}
                >
                  <h2 className="text-gray-300 text-[14px]">Default</h2>
                </div>

                {baseMapOptions.map((option) => (
                  <div
                    key={option.id}
                    className={cn(
                      "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                      currentBaseMap === option.mapState && "bg-[#ffffff2f]",
                    )}
                    onClick={() => {
                      setCurrentBaseMap(option.mapState);
                      setTimeout(() => {
                        triggerPropertiesReload();
                      }, 500);
                    }}
                  >
                    <h2 className="text-gray-300 text-[14px]">{option.label}</h2>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="absolute bottom-16 pb-4 min-[750px]:bottom-0 left-0 ml-3 min-[750px]:ml-[75px] flex flex-col">
        <button
          className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md size-[40px] bg-primary-dark flex items-center justify-center"
          onClick={navigateToNorthPole}
        >
          <IbiIcon icon="lets-icons:compass-north" className="text-xl" />
        </button>
        <button
          className="border border-[#ffffff56] mt-2 hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md size-[40px] bg-primary-dark flex items-center justify-center"
          onClick={navigateToInitialPosition}
        >
          <IbiIcon icon="line-md:my-location-loop" className="text-xl" />
        </button>
        <button
          className="rounded-t-md mt-2 rounded-r-md rounded-l-md border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden !rounded-b-none size-[40px] bg-primary-dark flex items-center justify-center"
          onClick={() => handleZoom("in")}
        >
          <IbiIcon icon="ic:baseline-plus" className="text-xl" />
        </button>
        <button
          className="border-l border-r border-b border-[#ffffff56] hover:border-[#ffffff] hover:border-t hover:bg-[#ffffff56] overflow-hidden rounded-b-md rounded-r-md rounded-l-md rounded-t-none size-[40px] bg-primary-dark flex items-center justify-center"
          onClick={() => handleZoom("out")}
        >
          <IbiIcon icon="ic:baseline-minus" className="text-xl" />
        </button>
      </div>
      {selectedPropertyDetails && (
        <>
          <div className="absolute -top-[95px] min-[750px]:-top-[10px] right-[450px] mt-[140px] ml-3 min-[750px]:ml-[75px] flex items-center gap-1.5">
            <NewBookmark propertyId={selectedPropertyDetails.propertyId?.toString() as string} />
            <IbiIcon
              btn
              icon={copySuccess ? "tabler:check" : "tabler:navigation-share"}
              className={cn("bg-gray-900 text-3xl", copySuccess ? "text-green-500" : "text-gray-300")}
              onClick={handleCopyIbiCode}
            />
          </div>
        </>
      )}
    </>
  );
};

export default MapControllers;
