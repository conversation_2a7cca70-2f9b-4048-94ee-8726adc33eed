import IbiIcon from "@/components/IbiUi/IbiIcon";
import { useBiomeMap } from "@/hooks/useLayerControl/useBiomeMap";
import { useHydroshedsManagementStore } from "@/hooks/useLayerControl/useHydrosheds/store";
import useMap from "@/hooks/useMap";
import { cn } from "@/lib/utils";
import { useBiomeSelector } from "@/stores/biome.store";
import { useMapSelector } from "@/stores/map.store";

interface MapItem {
  id: number;
  label: string;
  icon: string;
  iconSize: string;
  mapState: "default" | "biome" | "hydro";
}

interface MapMenuProps {
  currentMapType: "default" | "biome" | "hydro";
  setCurrentMapType: (mapType: "default" | "biome" | "hydro") => void;
}

const MapMenu = ({ currentMapType, setCurrentMapType }: MapMenuProps) => {
  const { setShowProperties, setShowRestorations } = useMap();
  const setSelectedBiome = useBiomeSelector.use.setSelectedBiome();
  const setSelectedItem = useMapSelector.use.setSelectedItem();
  const setSelectedTerritory = useMapSelector.use.setSelectedTerritory();
  const setSelectedHydroshedId = useMapSelector.use.setSelectedHydroshedId();
  const setForestHistoryHydroshed = useMapSelector.use.setForestHistoryHydroshed();
  const triggerHydroshedsReload = useMapSelector.use.triggerHydroshedsReload();
  const { setCurrentHydroshedLevel, setSelectedHydroshedLevel } = useHydroshedsManagementStore();
  const { clearSelection: clearBiomeSelection } = useBiomeMap();

  const mapMenuItems: MapItem[] = [
    {
      id: 1,
      label: "Claim",
      icon: "mingcute:building-4-fill",
      iconSize: "text-sm",
      mapState: "default",
    },
    // {
    //   id: 2,
    //   label: "Protect",
    //   icon: "mdi:tree-outline",
    //   iconSize: "text-lg",
    //   mapState: "biome",
    // },
    {
      id: 3,
      label: "Predict",
      icon: "mdi:water-outline",
      iconSize: "text-lg",
      mapState: "hydro",
    },
  ];

  const handleMapChange = (mapState: "default" | "biome" | "hydro") => {
    const previousMapType = currentMapType;
    setCurrentMapType(mapState);

    clearBiomeSelection();
    setSelectedItem({ id: null, type: null, properties: null });

    if (previousMapType === "hydro" && mapState !== "hydro") {
      setSelectedHydroshedLevel({
        id: "hybas_12",
        label: "Hybas 12",
      });
      setCurrentHydroshedLevel("Hybas 12 (default)");
      setSelectedHydroshedId(null);
    }

    setSelectedHydroshedId(null);
    setForestHistoryHydroshed(null);

    if (mapState === "default") {
      setSelectedBiome(null);
      setShowProperties(true);
      setShowRestorations(false);
    } else {
      setSelectedTerritory(null);
      setShowProperties(false);
      setShowRestorations(false);

      if (mapState === "hydro") {
        setTimeout(() => {
          triggerHydroshedsReload();
        }, 300);
      }
    }
  };

  return (
    <div className="h-[42.77px] flex items-end gap-[17px] w-fit">
      {mapMenuItems.map((item) => {
        return (
          <button
            key={item.id}
            className={cn(
              "text-[0.875rem] flex gap-1 items-center py-3 h-[42.77px] relative",
              "after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5",
              "after:transition-all after:duration-300 after:ease-in-out",
              {
                "font-semibold after:bg-[#FFB524] after:opacity-100 after:scale-x-100":
                  currentMapType === item.mapState,
                "opacity-50 hover:opacity-100 hover:text-[#F5DC84] after:scale-x-0 after:opacity-0":
                  currentMapType !== item.mapState,
              },
            )}
            onClick={() => handleMapChange(item.mapState)}
          >
            <IbiIcon icon={item.icon} className={item.iconSize} />
            {item.label}
          </button>
        );
      })}
    </div>
  );
};

export default MapMenu;
