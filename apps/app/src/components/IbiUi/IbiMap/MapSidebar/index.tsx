// import { <PERSON><PERSON> } from "@/components/ui/button";
// import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";

// import IbiIcon from "../../IbiIcon";
// import Example from "./PieChart";
// import AnimatedRadial<PERSON>hart from "./RadialBar";

// const MapSidebar = ({
//   propertyId,
//   propertyName,
//   onOpenChange,
// }: {
//   propertyId: number | null;
//   propertyName: string | null;
//   onOpenChange: (open: boolean) => void;
// }) => {
//   return (
//     <Sheet open={propertyId !== null} onOpenChange={onOpenChange}>
//       <SheetContent className="!max-w-[460px] flex flex-col border-[#ffffff32] bg-[#00000083]">
//         <SheetHeader>
//           <SheetTitle className="flex items-center gap-1 border-b border-gray-600 leading-8 text-gray-200">
//             <IbiIcon icon="majesticons:globe-earth-line" />
//             <span className="font-semibold">{propertyName}</span>
//           </SheetTitle>
//           {/* <SheetDescription className="font-semibold flex items-center justify-between gap-1 text-gray-400">
//             <span className="text-gray-400">PropertyPlaceName</span>
//             <IbiIcon icon="material-symbols:location-on" className="text-sm text-white" />
//           </SheetDescription> */}
//         </SheetHeader>
//         <div className="my-4 overflow-y-auto flex-1">
//           <h2 className="text-xs text-gray-400 font-bold">RESUMO</h2>
//           <div className="flex w-full h-[70px] mt-2">
//             <div className="w-1/2 h-full">
//               <IbiIcon icon="material-symbols:diamond-outline" className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]" />
//               <p className="text-sm mt-1.5 font-extrabold">27.000</p>
//               <h2 className="text-xs text-gray-600">UNIDADES FUNDIÁRIAS</h2>
//             </div>
//             <div className="w-1/2 h-full">
//               <IbiIcon
//                 icon="material-symbols:pulse-alert-outline"
//                 className="text-2xl p-0.5 bg-red-950 rounded-[3rem]"
//               />
//               <p className="text-sm mt-1.5 font-extrabold">90</p>
//               <h2 className="text-xs text-gray-600">ALERTAS</h2>
//             </div>
//           </div>
//           <div className="mt-7">
//             <Example />
//             <hr className=" border-gray-700 my-7" />
//             <AnimatedRadialChart />
//           </div>
//         </div>
//         <SheetFooter>
//           <SheetClose asChild>
//             <Button className="w-full gap-2" type="submit">
//               <IbiIcon icon="material-symbols:token-outline" />
//               Buy token
//             </Button>
//           </SheetClose>
//         </SheetFooter>
//       </SheetContent>
//     </Sheet>
//   );
// };

// export default MapSidebar;
