import { BiodiversityTerritory } from "@/types";
import React from "react";

const AnimalSpeciesIcons: React.FC<{ data: BiodiversityTerritory["counts"] }> = ({ data }) => {
  const speciesData = [
    { icon: "🐸", label: "Amphibians", count: data.amphibians, color: "bg-red-500" },
    { icon: "🐦", label: "Birds", count: data.birds, color: "bg-blue-500" },
    { icon: "🐎", label: "Mammals", count: data.mammals, color: "bg-purple-500" },
    { icon: "🌳", label: "Plants", count: data.plants, color: "bg-green-500" },
    { icon: "🐍", label: "Reptiles", count: data.reptiles, color: "bg-yellow-500" },
  ];

  return (
    <div className="mt-4">
      <div className="flex justify-between w-full">
        {speciesData.map((species, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="text-2xl">{species.icon}</div>
            <p className="text-sm font-extrabold">{species.count}</p>
            <h3 className="text-xs text-gray-600">{species.label}</h3>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnimalSpeciesIcons;
