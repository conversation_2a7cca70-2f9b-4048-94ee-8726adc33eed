import ClaimForm from "@/components/ClaimForm";
import { Button } from "@/components/ui/button";
import { useForestHistoryProperties } from "@/hooks/useForestHistoryProperties";
import { useHydroshedsManagementStore } from "@/hooks/useLayerControl/useHydrosheds/store";
import { usePropertyManagementStore } from "@/hooks/useLayerControl/useProperties/store";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import { formatNumber } from "@/utils";
import { usePrivy } from "@privy-io/react-auth";
import { lazy, useCallback, useEffect } from "react";

import IbiIcon from "../../IbiIcon";
import AnimalSpeciesIcons from "./AnimalSpeciesIcons";
import HydroshedSkeleton from "./HydroshedSkeleton";
import PropertySkeleton from "./PropertySkeleton";

const AreaChart = lazy(() => import("./PieChart"));
const ForestAreaChart = lazy(() => import("./RadialBar"));
const RadarChart = lazy(() => import("./RadarChart"));

const MapSideContent = ({ currentMapType }: { currentMapType: "default" | "biome" | "hydro" }) => {
  const { user } = usePrivy();
  const selectedTerritory = useMapSelector.use.selectedTerritory();
  const forestHistory = useMapSelector.use.forestHistory();
  const forestHistoryHydroshed = useMapSelector.use.forestHistoryHydroshed();
  const isLoadingForestHistory = useMapSelector.use.isLoadingForestHistory();
  const selectedHydroshedId = useMapSelector.use.selectedHydroshedId();
  const isLoadingForestHistoryHydroshed = useMapSelector.use.isLoadingForestHistoryHydroshed();

  const { selectedHydroshedsMulti, isMultiSelectMode } = useHydroshedsManagementStore();

  const { selectedPropertyDetails, isLoadingProperty, resetPropertyData } = usePropertyManagementStore();
  const selectedItem = useMapSelector.use.selectedItem();
  const { fetchPropertyForestHistory } = useForestHistoryProperties();
  const biodiversityTerritories = useMapSelector.use.biodiversityTerritories();
  const isLoadingBiodiversityTerritories = useMapSelector.use.isLoadingBiodiversityTerritories();

  useEffect(() => {
    if (currentMapType !== "default") {
      resetPropertyData();
    }
  }, [currentMapType, resetPropertyData]);

  useEffect(() => {
    return () => {
      resetPropertyData();
    };
  }, [resetPropertyData]);

  useEffect(() => {
    if (selectedPropertyDetails?.propertyId) {
      fetchPropertyForestHistory(selectedPropertyDetails.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPropertyDetails?.id]);

  const renderCorrectName = useCallback(() => {
    if (currentMapType === "biome") {
      if (selectedItem?.type === "voronoi" && selectedItem?.properties) {
        return `Grid ${selectedItem.properties.GRID_ID || "N/A"}`;
      } else if (selectedItem?.type === "ecoregions" && selectedItem?.properties) {
        return selectedItem.properties.name || `Ecoregion ${selectedItem.id}`;
      } else if (selectedItem?.type === "biomes" && selectedItem?.properties) {
        return selectedItem.properties.name || `Biome ${selectedItem.id}`;
      } else {
        return "World";
      }
    } else if (currentMapType === "hydro") {
      if (isMultiSelectMode && selectedHydroshedsMulti.length > 0) {
        return `${selectedHydroshedsMulti.length} Hydrosheds Selected`;
      } else if (selectedHydroshedId) {
        return selectedHydroshedId.toString();
      } else {
        return "World";
      }
    } else {
      if (selectedPropertyDetails) {
        return selectedPropertyDetails?.name || "N/A";
      } else if (selectedTerritory?.name) {
        return selectedTerritory.name || "N/A";
      } else {
        return "World";
      }
    }
  }, [
    selectedItem,
    selectedPropertyDetails,
    selectedTerritory,
    currentMapType,
    selectedHydroshedId,
    isMultiSelectMode,
    selectedHydroshedsMulti,
  ]);

  const getClaimButtonState = useCallback(() => {
    if (!selectedPropertyDetails?.claims) {
      return {
        text: "Claim Property",
        disabled: false,
        showForm: true,
        isClaimed: false,
        claimId: null,
      };
    }

    const verifiedClaim = selectedPropertyDetails.claims.find((claim) => claim.status === "APPROVED");

    if (verifiedClaim) {
      if (verifiedClaim.user.authId === user?.id) {
        return {
          text: "Property Claimed",
          disabled: true,
          showForm: false,
          isClaimed: false,
          claimId: null,
        };
      }

      return {
        text: "Contest Claim",
        disabled: false,
        showForm: true,
        isClaimed: true,
        claimId: verifiedClaim.id,
      };
    }

    return {
      text: "Claim Property",
      disabled: false,
      showForm: true,
      isClaimed: false,
      claimId: null,
    };
  }, [selectedPropertyDetails?.claims, user?.id]);

  const renderOverview = () => {
    if (selectedItem?.type === "voronoi") {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-1/2 h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">{selectedItem.properties?.GRID_ID || "N/A"}</p>
            <h2 className="text-xs text-gray-600">GRID ID</h2>
          </div>
          <div className="w-1/2 h-full">
            <IbiIcon icon="ic:twotone-shield-moon" className="text-2xl p-0.5 bg-red-950 rounded-[3rem]" />
            <p className="text-sm mt-1.5 font-extrabold">{selectedItem.properties?.ECO_ID || "N/A"}</p>
            <h2 className="text-xs text-gray-600">ECOREGION ID</h2>
          </div>
        </div>
      );
    } else if (selectedItem?.type === "ecoregions") {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-1/2 h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">{selectedItem.properties?.ecoId || "N/A"}</p>
            <h2 className="text-xs text-gray-600">ECOREGION ID</h2>
          </div>
          <div className="w-1/2 h-full">
            <IbiIcon icon="ic:twotone-shield-moon" className="text-2xl p-0.5 bg-red-950 rounded-[3rem]" />
            <p className="text-sm mt-1.5 font-extrabold">{selectedItem.properties?.biomeId || "N/A"}</p>
            <h2 className="text-xs text-gray-600">BIOME ID</h2>
          </div>
        </div>
      );
    } else if (selectedItem?.type === "biomes") {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-full h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">{selectedItem.properties?.id || "N/A"}</p>
            <h2 className="text-xs text-gray-600">BIOME ID</h2>
          </div>
        </div>
      );
    } else if (currentMapType === "biome") {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-1/2 h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">-</p>
            <h2 className="text-xs text-gray-600">SELECT A BIOME</h2>
          </div>
          <div className="w-1/2 h-full">
            <IbiIcon icon="ic:twotone-shield-moon" className="text-2xl p-0.5 bg-red-950 rounded-[3rem]" />
            <p className="text-sm mt-1.5 font-extrabold">-</p>
            <h2 className="text-xs text-gray-600">SELECT ECOREGION</h2>
          </div>
        </div>
      );
    } else if (currentMapType === "hydro") {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-1/2 h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">27.000</p>
            <h2 className="text-xs text-gray-600">TOTAL HYDROSHEDS</h2>
          </div>
          <div className="w-1/2 h-full">
            <IbiIcon icon="ic:twotone-shield-moon" className="text-2xl p-0.5 bg-red-950 rounded-[3rem]" />
            <p className="text-sm mt-1.5 font-extrabold">90</p>
            <h2 className="text-xs text-gray-600">TOTAL CONSERVATION AREAS</h2>
          </div>
        </div>
      );
    } else if (selectedTerritory) {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-1/2 h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">27.000</p>
            <h2 className="text-xs text-gray-600">TOTAL PROPERTIES</h2>
          </div>
          <div className="w-1/2 h-full">
            <IbiIcon icon="ic:twotone-shield-moon" className="text-2xl p-0.5 bg-red-950 rounded-[3rem]" />
            <p className="text-sm mt-1.5 font-extrabold">90</p>
            <h2 className="text-xs text-gray-600">TOTAL CONSERVATION AREAS</h2>
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex w-full h-[70px] mt-2">
          <div className="w-1/2 h-full">
            <IbiIcon
              icon="material-symbols:shield-with-house-rounded"
              className="text-2xl p-0.5 bg-slate-950 rounded-[3rem]"
            />
            <p className="text-sm mt-1.5 font-extrabold">27.000</p>
            <h2 className="text-xs text-gray-600">TOTAL PROPERTIES</h2>
          </div>
          <div className="w-1/2 h-full">
            <IbiIcon icon="ic:twotone-shield-moon" className="text-2xl p-0.5 bg-red-950 rounded-[3rem]" />
            <p className="text-sm mt-1.5 font-extrabold">90</p>
            <h2 className="text-xs text-gray-600">TOTAL CONSERVATION AREAS</h2>
          </div>
        </div>
      );
    }
  };

  const renderRadarChart = () => {
    if (!selectedItem) return null;

    if (currentMapType === "biome") {
      if (selectedItem?.type === "biomes") {
        return <RadarChart title="BIOME BIODIVERSITY" type="biome" useSampleData={true} />;
      } else if (selectedItem?.type === "ecoregions") {
        return <RadarChart title="ECOREGION BIODIVERSITY" type="ecoregion" useSampleData={true} />;
      } else if (selectedItem?.type === "voronoi") {
        return <RadarChart title="CELL BIODIVERSITY" type="voronoi" useSampleData={true} />;
      }
    } else if (currentMapType === "hydro") {
      if (selectedHydroshedId) {
        return <RadarChart title="HYDROSHED BIODIVERSITY" type="hydro" useSampleData={true} />;
      } else {
        return null;
      }
    } else if (currentMapType === "default") {
      if (selectedPropertyDetails) {
        return <RadarChart title="PROPERTY BIODIVERSITY" type="property" useSampleData={true} />;
      } else if (selectedTerritory?.type === "state") {
        return (
          <RadarChart
            title="STATE BIODIVERSITY"
            type="state"
            data={biodiversityTerritories || undefined}
            isLoading={isLoadingBiodiversityTerritories}
          />
        );
      } else if (selectedTerritory?.type === "country") {
        return (
          <RadarChart
            title="COUNTRY BIODIVERSITY"
            type="country"
            data={biodiversityTerritories || undefined}
            isLoading={isLoadingBiodiversityTerritories}
          />
        );
      }
    }

    return null;
  };

  return (
    <div className="fixed top-4 left-4 w-96 h-[calc(100vh-120px)] text-xs rounded-xl bg-black/85 backdrop-blur-xl border-2 border-gray-700 shadow-lg p-4 pr-0 z-10">
      <div className="flex items-center justify-between pr-4">
        <h1 className="text-[14px] font-bold">{renderCorrectName()}</h1>
        <div className="flex flex-col items-center bg-green-900/50 text-green-400 rounded-lg px-2 py-0.5">
          <span>Ranking</span>
          <div className="flex items-center gap-1">
            <span>
              {Math.abs(
                renderCorrectName()
                  .split("")
                  .reduce((acc, char) => acc + char.charCodeAt(0), 0) % 100,
              ) + 1}
              °
            </span>
            <span>↑</span>
          </div>
        </div>
      </div>
      <h2 className="text-xs text-gray-400 font-bold mt-3">OVERVIEW</h2>
      {renderOverview()}

      <div className="h-[calc(100%-150px)] overflow-y-auto pr-4">
        {currentMapType === "hydro" && (
          <>
            {isMultiSelectMode && selectedHydroshedsMulti.length > 0 ? (
              <div className="mt-4">
                <h2 className="text-xs text-gray-400 font-bold uppercase">MULTIPLE HYDROSHEDS SELECTED</h2>
                <div className="mt-4 space-y-4">
                  <p className="text-sm">
                    You{"'"}ve selected {selectedHydroshedsMulti.length} hydrosheds.
                  </p>
                  <p className="text-xs text-gray-400">Hold CTRL (CMD) and click to select more hydrosheds.</p>

                  <div className="mt-4">
                    <h3 className="text-xs text-gray-400 font-bold uppercase mb-2">Selected Hydroshed IDs:</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedHydroshedsMulti.map((id) => (
                        <div
                          key={id}
                          className="px-3 py-1 rounded-full bg-blue-900/30 border border-blue-700/50 text-white"
                        >
                          {id}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : isLoadingForestHistoryHydroshed ? (
              <HydroshedSkeleton />
            ) : (
              selectedHydroshedId &&
              forestHistoryHydroshed && (
                <>
                  <div className="mt-4">
                    <h2 className="text-xs text-gray-400 font-bold uppercase">HYDROSHED DETAILS</h2>
                    <div className="mt-4 space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <div className="flex items-center gap-1">
                            <h3 className="text-lg text-gray-400">ID</h3>
                          </div>
                          <div className="flex items-center gap-2">
                            <p className="text-[14px] font-bold">{forestHistoryHydroshed?.hybasId}</p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-400">Total Hydroshed Area</span>
                          <span className="text-[14px] font-bold">
                            {formatNumber(Number(forestHistoryHydroshed?.areaHa))} ha
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-400">Total Forest Area</span>
                          <span className="text-[14px] font-bold">
                            {formatNumber(
                              Number(
                                forestHistoryHydroshed?.ecoregions.reduce((acc, item) => acc + item.forestArea, 0),
                              ) * 10,
                            )}{" "}
                            ha
                          </span>
                        </div>
                      </div>

                      {selectedHydroshedId && forestHistoryHydroshed && (
                        <>
                          <hr className="border-gray-700 my-7" />
                          <div className="mb-10">
                            <AreaChart data={forestHistoryHydroshed} isLoading={isLoadingForestHistoryHydroshed} />
                          </div>
                          <ForestAreaChart
                            data={forestHistoryHydroshed}
                            isLoading={isLoadingForestHistoryHydroshed}
                            territoryName={`Hydroshed ${forestHistoryHydroshed?.hybasId || selectedHydroshedId}`}
                          />
                        </>
                      )}
                    </div>
                  </div>
                </>
              )
            )}
          </>
        )}

        {currentMapType === "default" && (
          <>
            {isLoadingProperty === true ? (
              <PropertySkeleton />
            ) : (
              selectedPropertyDetails && (
                <div className="mt-3">
                  <h2 className="text-xs text-gray-400 font-bold uppercase">PROPERTY DETAILS</h2>
                  <div className="mt-4 space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <div className="flex items-center gap-1">
                          <h3 className="text-lg text-gray-400">IBI Code</h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-[14px] font-bold">{selectedPropertyDetails.ibiCode}</p>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center gap-1">
                          <h3 className="text-lg text-gray-400">ADM1</h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-[14px] font-bold">
                            {selectedPropertyDetails?.state ? selectedPropertyDetails?.state : "-"}
                          </p>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center gap-1">
                          <h3 className="text-lg text-gray-400">ADM0</h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-[14px] font-bold">
                            {selectedPropertyDetails?.country ? selectedPropertyDetails?.country : "-"}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400">Total Property Area</span>
                        <span className="text-[14px] font-bold">
                          {formatNumber(Number(selectedPropertyDetails.areaHa))} ha
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400">Total Forest Area</span>
                        <span className="text-[14px] font-bold">
                          {formatNumber(Number(selectedPropertyDetails.totalForestArea))} ha
                        </span>
                      </div>
                    </div>

                    {selectedPropertyDetails.propertyEcoregions &&
                      selectedPropertyDetails.propertyEcoregions.length > 0 && (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h3 className="text-sm text-gray-400 font-bold">ECOREGIONS</h3>
                            <span className="text-xs text-gray-400">
                              {selectedPropertyDetails.propertyEcoregions.length} region
                              {selectedPropertyDetails.propertyEcoregions.length !== 1 ? "s" : ""}
                            </span>
                          </div>
                          <div className="space-y-4">
                            {Object.entries(
                              selectedPropertyDetails.propertyEcoregions.reduce(
                                (acc, item) => {
                                  const biomeName = item.ecoregion.biomeName;
                                  if (!acc[biomeName]) {
                                    acc[biomeName] = [];
                                  }
                                  acc[biomeName].push(item);
                                  return acc;
                                },
                                {} as Record<string, typeof selectedPropertyDetails.propertyEcoregions>,
                              ),
                            ).map(([biomeName, ecoregions]) => (
                              <div
                                key={biomeName}
                                className="space-y-2 p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                              >
                                <div className="flex justify-between items-center">
                                  <h3 className="text-sm font-semibold">Biome: {biomeName}</h3>
                                </div>
                                <div className="grid gap-2">
                                  {ecoregions.map((item) => (
                                    <div
                                      key={item.ecoregion.code}
                                      className="text-xs py-3 flex justify-between items-center border-b border-gray-700/50 last:border-b-0"
                                    >
                                      <div>
                                        <h4 className="font-medium text-sm">{item.ecoregion.name}</h4>
                                      </div>
                                      <div className="text-right">
                                        <span className="text-xs text-gray-400">{item.forestArea.toFixed(2)} ha</span>
                                        <div className="text-xs text-gray-500">
                                          {((item.forestArea / selectedPropertyDetails.totalForestArea) * 100).toFixed(
                                            1,
                                          )}
                                          %
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    <AreaChart
                      data={{
                        id: selectedPropertyDetails?.propertyId?.toString() || "0",
                        externalId: selectedPropertyDetails?.ibiCode || "",
                        name: selectedPropertyDetails?.name || "Property",
                        type: "property",
                        adm0Id: "BR",
                        adm1Id: null,
                        parentId: null,
                        areaHa: Number(selectedPropertyDetails?.areaHa || 0),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                        ecoregions:
                          (selectedPropertyDetails as any)?.propertyEcoregions?.map((eco) => ({
                            id: eco.ecoregion.code,
                            name: eco.ecoregion.name,
                            forestArea: eco.forestArea,
                          })) || [],
                        forestHistory: [],
                      }}
                      isLoading={isLoadingProperty}
                    />
                    {getClaimButtonState().showForm ? (
                      <ClaimForm
                        propertyId={selectedPropertyDetails?.id?.toString() as string}
                        isClaimed={getClaimButtonState().isClaimed}
                        claimId={getClaimButtonState().claimId}
                      >
                        <Button className="w-full mt-4" disabled={getClaimButtonState().disabled}>
                          {getClaimButtonState().text}
                        </Button>
                      </ClaimForm>
                    ) : (
                      <Button
                        className={cn("w-full mt-4", getClaimButtonState().disabled && "bg-green-600")}
                        disabled={getClaimButtonState().disabled}
                      >
                        {getClaimButtonState().text}
                      </Button>
                    )}
                  </div>
                </div>
              )
            )}

            <hr className="border-gray-700 my-5" />
            {renderRadarChart()}
            {selectedTerritory && biodiversityTerritories && !isLoadingBiodiversityTerritories && (
              <AnimalSpeciesIcons data={biodiversityTerritories.counts} />
            )}
            {selectedTerritory && isLoadingBiodiversityTerritories && (
              <div className="mt-4 py-4 flex items-center justify-center">
                <p className="text-gray-500 text-sm">Loading animal species data...</p>
              </div>
            )}

            <div className="mt-4">
              {selectedTerritory && !selectedPropertyDetails && (
                <>
                  <hr className="border-gray-700 my-7" />
                  <div className="mb-10">
                    <AreaChart data={forestHistory} isLoading={isLoadingForestHistory} />
                  </div>
                  <ForestAreaChart
                    data={forestHistory}
                    isLoading={isLoadingForestHistory}
                    territoryName={selectedTerritory.name}
                  />
                </>
              )}

              {selectedPropertyDetails && (
                <>
                  <hr className="border-gray-700 my-7" />
                  <ForestAreaChart
                    data={forestHistory}
                    isLoading={isLoadingForestHistory}
                    territoryName={selectedPropertyDetails.propertyId?.toString() || ""}
                  />
                </>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MapSideContent;
