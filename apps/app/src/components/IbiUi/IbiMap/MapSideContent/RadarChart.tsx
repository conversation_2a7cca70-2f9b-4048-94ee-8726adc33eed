import { BiodiversityTerritory } from "@/types";
import { useMemo } from "react";
import {
  PolarAngleAxis,
  PolarGrid,
  Radar,
  RadarChart as RechartsRadar<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
} from "recharts";

interface RadarChartProps {
  data?: any[] | BiodiversityTerritory;
  nameKey?: string;
  dataKey?: string;
  title: string;
  type: "biome" | "ecoregion" | "voronoi" | "country" | "state" | "property" | "hydro";
  useSampleData?: boolean;
  isLoading?: boolean;
}

const RadarChart = ({
  data,
  nameKey = "name",
  dataKey = "value",
  title,
  type,
  useSampleData = false,
  isLoading = false,
}: RadarChartProps) => {
  const sampleData = useMemo(() => {
    return [
      { name: "🐸", value: Math.floor(Math.random() * 100) },
      { name: "🐦", value: Math.floor(Math.random() * 100) },
      { name: "🐎", value: Math.floor(Math.random() * 100) },
      { name: "🌳", value: Math.floor(Math.random() * 100) },
      { name: "🐍", value: Math.floor(Math.random() * 100) },
    ];
  }, []);

  const processedData = useMemo(() => {
    if (useSampleData) return sampleData;
    if (isLoading) return [];

    if (data && "counts" in data && "maxValues" in data) {
      const biodiversityData = data as BiodiversityTerritory;
      return [
        {
          name: "🐸",
          value: Math.round((biodiversityData.counts.amphibians / biodiversityData.maxValues.amphibians) * 100),
          count: biodiversityData.counts.amphibians,
          max: biodiversityData.maxValues.amphibians,
        },
        {
          name: "🌳",
          value: Math.round((biodiversityData.counts.plants / biodiversityData.maxValues.plants) * 100),
          count: biodiversityData.counts.plants,
          max: biodiversityData.maxValues.plants,
        },
        {
          name: "🐎",
          value: Math.round((biodiversityData.counts.mammals / biodiversityData.maxValues.mammals) * 100),
          count: biodiversityData.counts.mammals,
          max: biodiversityData.maxValues.mammals,
        },
        {
          name: "🐦",
          value: Math.round((biodiversityData.counts.birds / biodiversityData.maxValues.birds) * 100),
          count: biodiversityData.counts.birds,
          max: biodiversityData.maxValues.birds,
        },
        {
          name: "🐍",
          value: Math.round((biodiversityData.counts.reptiles / biodiversityData.maxValues.reptiles) * 100),
          count: biodiversityData.counts.reptiles,
          max: biodiversityData.maxValues.reptiles,
        },
      ];
    }

    return data as any[];
  }, [data, useSampleData, isLoading, sampleData]);

  if (isLoading) {
    return (
      <div className="w-full py-8 flex items-center justify-center">
        <p className="text-gray-500 text-sm">Loading biodiversity data...</p>
      </div>
    );
  }

  if (!processedData || processedData.length === 0) {
    return (
      <div className="w-full py-8 flex items-center justify-center">
        <p className="text-gray-500 text-sm">No data available</p>
      </div>
    );
  }

  const getRadarFillColor = () => {
    switch (type) {
      case "biome":
        return "rgba(16, 185, 129, 0.6)";
      case "ecoregion":
        return "rgba(59, 130, 246, 0.6)";
      case "voronoi":
        return "rgba(236, 72, 153, 0.6)";
      case "country":
        return "rgba(249, 115, 22, 0.6)";
      case "state":
        return "rgba(139, 92, 246, 0.6)";
      case "property":
        return "rgba(234, 179, 8, 0.6)";
      case "hydro":
        return "rgba(16, 185, 129, 0.6)";
      default:
        return "rgba(16, 185, 129, 0.6)";
    }
  };

  const getRadarStrokeColor = () => {
    switch (type) {
      case "biome":
        return "#10B981";
      case "ecoregion":
        return "#3B82F6";
      case "voronoi":
        return "#EC4899";
      case "country":
        return "#F97316";
      case "state":
        return "#8B5CF6";
      case "property":
        return "#EAB308";
      case "hydro":
        return "#10B981";
      default:
        return "#10B981";
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-black/90 border border-gray-700 p-2 rounded-md text-xs">
          <p className="mb-1">{data.name}</p>
          <p className="text-[10px] text-gray-400">
            {data.count !== undefined ? `Count: ${data.count.toLocaleString()}` : ""}
          </p>
          <p className="text-[10px] text-gray-400">
            {data.max !== undefined ? `Max: ${data.max.toLocaleString()}` : ""}
          </p>
          <p className="font-bold">{data.value}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="mt-6">
      <h3 className="text-sm text-gray-400 font-bold mb-2">{title}</h3>
      <div className="w-full h-[250px] rounded-lg p-2">
        <ResponsiveContainer width="100%" height="100%">
          <RechartsRadarChart cx="50%" cy="50%" outerRadius="80%" data={processedData}>
            <PolarGrid stroke="#444" />
            <PolarAngleAxis dataKey={nameKey} tick={{ fill: "#ccc", fontSize: 11 }} />
            <Tooltip content={<CustomTooltip />} />
            <Radar
              name={title}
              dataKey={dataKey}
              stroke={getRadarStrokeColor()}
              fill={getRadarFillColor()}
              fillOpacity={0.5}
            />
          </RechartsRadarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default RadarChart;
