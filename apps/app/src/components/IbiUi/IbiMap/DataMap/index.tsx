// import useMap from "@/hooks/useMap";
// import { CurrentMap } from "@/types";
// import {
//   GEODATIN_STYLE_URL,
//   MAPBOX_TOKEN,
//   NASA_FIRMS_API_KEY,
//   STATIC_WIND_LAYER,
//   TOMORROW_API_KEY,
// } from "@/utils/constants";
// import mapboxgl from "mapbox-gl";
// import "mapbox-gl/dist/mapbox-gl.css";
// import React, { RefObject, useEffect, useRef } from "react";

// const DataMap = () => {
//   const mapContainerRef = useRef<HTMLDivElement>(null);
//   const mapRef = useRef<mapboxgl.Map | null>(null);
//   const { currentMap } = useMap();

//   useEffect(() => {
//     if (!mapContainerRef.current) return;

//     if (!mapRef.current) {
//       mapboxgl.accessToken = MAPBOX_TOKEN;
//       mapRef.current = new mapboxgl.Map({
//         container: mapContainerRef.current,
//         projection: "globe",
//         style: GEODATIN_STYLE_URL,
//         center: [-28, 47],
//         zoom: 2,
//         minZoom: 2,
//         maxZoom: 20,
//         scrollZoom: true,
//       });

//       mapRef.current.on("load", () => {
//         handleCurrentLayer(mapRef, currentMap);
//       });
//     }
//   }, []);

//   useEffect(() => {
//     const map = mapRef.current;
//     if (map && map.isStyleLoaded()) {
//       updateLayers(mapRef, currentMap);
//     } else if (map) {
//       map.once("styledata", () => {
//         updateLayers(mapRef, currentMap);
//       });
//     }
//   }, [currentMap]);

//   const removeExistingLayers = (map: mapboxgl.Map) => {
//     const layers = ["wind-layer", "fire-layer", "radar-tiles"];
//     layers.forEach((layer) => {
//       if (map.getLayer(layer)) {
//         map.removeLayer(layer);
//       }
//       if (
//         map.getSource(
//           layer === "wind-layer" ? "raster-array-source" : layer === "fire-layer" ? "fire-data" : "tomorrow-io-api",
//         )
//       ) {
//         map.removeSource(
//           layer === "wind-layer" ? "raster-array-source" : layer === "fire-layer" ? "fire-data" : "tomorrow-io-api",
//         );
//       }
//     });
//   };

//   const updateLayers = (mapRef: RefObject<mapboxgl.Map | null>, currentMap: CurrentMap) => {
//     const map = mapRef.current;
//     if (!map) return;

//     removeExistingLayers(map);
//     handleCurrentLayer(mapRef, currentMap);
//   };

//   const handleCurrentLayer = (mapRef: RefObject<mapboxgl.Map | null>, currentMap: CurrentMap) => {
//     type TomorrowMapTypes = Exclude<CurrentMap, "default" | "wind" | "nasaFireLayer">;
//     const tomorrowMapTypes: Record<TomorrowMapTypes, string> = {
//       "global-precipitation": "precipitationIntensity",
//       temperature: "temperature",
//       fire: "fireIndex",
//       windSpeed: "windSpeed",
//       ozoneSurfaceConcentration: "pollutantO3",
//     };

//     const ISO_DATETIME = new Date().toISOString();

//     const map = mapRef.current;
//     if (!map) return;

//     if (currentMap === "wind") {
//       map.addSource("raster-array-source", {
//         type: "raster-array",
//         url: "mapbox://rasterarrayexamples.gfs-winds",
//         tileSize: 512,
//       });
//       map.addLayer(STATIC_WIND_LAYER);
//     } else if (currentMap === "nasaFireLayer") {
//       const fetchFireData = async () => {
//         try {
//           const response = await fetch(
//             `https://firms.modaps.eosdis.nasa.gov/api/area/csv/${NASA_FIRMS_API_KEY}/VIIRS_SNPP_NRT/world/5/${new Date().toISOString().slice(0, 10)}`,
//           );
//           const text = await response.text();
//           const rows = text.split("\n").slice(1);
//           const features = rows
//             .map((row) => {
//               const [latitude, longitude] = row
//                 .split(",")
//                 .map((value, index) => (index === 0 || index === 1 ? parseFloat(value) : value));

//               if (!isNaN(Number(latitude)) && !isNaN(Number(longitude))) {
//                 return {
//                   type: "Feature",
//                   geometry: {
//                     type: "Point",
//                     coordinates: [longitude, latitude],
//                   },
//                   properties: {},
//                 };
//               }
//               return null;
//             })
//             .filter(Boolean);

//           const geoJsonData: GeoJSON.FeatureCollection = {
//             type: "FeatureCollection",
//             features: features as GeoJSON.Feature[],
//           };

//           map.addSource("fire-data", {
//             type: "geojson",
//             data: geoJsonData,
//           });

//           map.addLayer({
//             id: "fire-layer",
//             type: "circle",
//             source: "fire-data",
//             paint: {
//               "circle-radius": 5,
//               "circle-color": "#ff0000",
//               "circle-stroke-width": 1,
//               "circle-stroke-color": "#ffffff",
//             },
//           });
//         } catch (error) {
//           console.error("Erro ao buscar dados do FIRMS:", error);
//         }
//       };

//       fetchFireData();
//     } else {
//       map.addSource("tomorrow-io-api", {
//         type: "raster",
//         tiles: [
//           `https://api.tomorrow.io/v4/map/tile/{z}/{x}/{y}/${tomorrowMapTypes[currentMap]}/${ISO_DATETIME}.png?apikey=${TOMORROW_API_KEY}`,
//         ],
//         tileSize: 256,
//         attribution: '&copy; <a href="https://www.tomorrow.io/weather-api">Powered by Tomorrow.io</a>',
//       });

//       map.addLayer({
//         id: "radar-tiles",
//         type: "raster",
//         source: "tomorrow-io-api",
//         minzoom: 1,
//         maxzoom: 12,
//       });
//     }
//   };

//   return <div id="map" ref={mapContainerRef} className="h-full"></div>;
// };

// export default DataMap;
