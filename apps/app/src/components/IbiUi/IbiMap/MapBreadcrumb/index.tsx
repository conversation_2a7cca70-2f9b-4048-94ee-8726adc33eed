import { useForestHistory } from "@/hooks/useForestHistory";
import { useProperties } from "@/hooks/useLayerControl/useProperties";
import { usePropertyManagementStore } from "@/hooks/useLayerControl/useProperties/store";
import useWorldBoundaries from "@/hooks/useLayerControl/useWorldBoundaries";
import useMap from "@/hooks/useMap";
import { useMapSelector } from "@/stores/map.store";
import { BreadcrumbItem } from "@/types";
import { useCallback, useEffect, useRef } from "react";

import IbiIcon from "../../IbiIcon";

const MapBreadcrumb = () => {
  const { fetchForestHistory } = useForestHistory();

  const selectedCountryRef = useRef<string | null>(null);
  const selectedStateRef = useRef<string | null>(null);

  const { mapRef, setShowProperties } = useMap();
  const { clearSelection: clearPropertySelection } = useProperties();
  const { selectedPropertyDetails: propertyDetails } = usePropertyManagementStore();
  const selectedTerritory = useMapSelector.use.selectedTerritory();
  const selectedPropertyDetails = useMapSelector.use.selectedPropertyDetails();
  const setSelectedPropertyDetails = useMapSelector.use.setSelectedPropertyDetails();
  const setSelectedTerritory = useMapSelector.use.setSelectedTerritory();
  const breadcrumbs = useMapSelector.use.breadcrumbs();
  const setBreadcrumbs = useMapSelector.use.setBreadcrumbs();

  const { updateSelection, zoomToFeature, clearSelections } = useWorldBoundaries();

  const hoveredCountryIdRef = useRef<number | null>(null);

  const clearPropertyLayers = useCallback((map: mapboxgl.Map) => {
    ["selected-property-outline", "selected-property-fill", "mask"].forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    if (map.getSource("selected-feature")) {
      map.removeSource("selected-feature");
    }

    map.triggerRepaint();
  }, []);

  const cleanupSelections = useCallback(
    (map: mapboxgl.Map) => {
      clearSelections(map);

      clearPropertySelection();
      setSelectedPropertyDetails(null);

      clearPropertyLayers(map);

      selectedCountryRef.current = null;
      selectedStateRef.current = null;

      map.getCanvas().style.cursor = "";
    },
    [clearSelections, clearPropertySelection, setSelectedPropertyDetails, clearPropertyLayers],
  );

  const handleClick = useCallback(
    async (item: BreadcrumbItem, index: number) => {
      if (!mapRef?.current) return;
      const map = mapRef.current as mapboxgl.Map;

      setShowProperties(true);

      cleanupSelections(map);

      if (item.type === "property") {
        if (selectedPropertyDetails) {
          const propertyFeatures = map.querySourceFeatures("properties-source", {
            sourceLayer: "properties",
            filter: ["==", ["get", "id"], selectedPropertyDetails.propertyId],
          });
          if (propertyFeatures.length > 0) {
            zoomToFeature(propertyFeatures[0], map);
          }
        }
      } else {
        const feature = map.querySourceFeatures(item.type === "country" ? "countries" : "states", {
          filter:
            item.type === "country"
              ? ["==", ["get", "country"], item.name]
              : ["all", ["==", ["get", "country"], item.country || ""], ["==", ["get", "state"], item.name]],
        })[0];

        if (feature) {
          updateSelection(feature, item.type === "country" ? "countries" : "states", map);
          zoomToFeature(feature, map, item.type);

          setSelectedTerritory({
            id: item.id,
            name: item.name || "Unknown",
            type: item.type,
            country: item.country || null,
          });

          setBreadcrumbs((prev) => prev.slice(0, index + 1));

          if (item.type === "state") {
            fetchForestHistory(item.id);
          } else if (item.type === "country") {
            fetchForestHistory(item.id);
          }
        }
      }
    },
    [
      cleanupSelections,
      mapRef,
      selectedPropertyDetails,
      setBreadcrumbs,
      setSelectedTerritory,
      setShowProperties,
      updateSelection,
      zoomToFeature,
      fetchForestHistory,
    ],
  );

  const handleCountryHover = useCallback(
    (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      if (!mapRef?.current) return;
      const map = mapRef.current as mapboxgl.Map;

      if (!e.features?.length || e.features[0].id === undefined) return;

      const feature = e.features[0];

      if (hoveredCountryIdRef.current !== null && hoveredCountryIdRef.current !== feature.id) {
        map.setFeatureState({ source: "countries", id: hoveredCountryIdRef.current }, { hover: false });
        hoveredCountryIdRef.current = null;
      }

      map.getCanvas().style.cursor = "pointer";
      hoveredCountryIdRef.current = feature.id as number;
      map.setFeatureState({ source: "countries", id: hoveredCountryIdRef.current }, { hover: true });
    },
    [mapRef],
  );

  const handleLeave = useCallback(() => {
    if (!mapRef?.current) return;
    const map = mapRef.current as mapboxgl.Map;

    if (hoveredCountryIdRef.current !== null) {
      map.setFeatureState({ source: "countries", id: hoveredCountryIdRef.current }, { hover: false });
      hoveredCountryIdRef.current = null;
    }
    map.getCanvas().style.cursor = "";
  }, [mapRef]);

  const clearBreadcrumbSelection = useCallback(() => {
    if (!mapRef?.current) return;
    const map = mapRef.current as mapboxgl.Map;

    cleanupSelections(map);

    map.on("mousemove", "country-fills", handleCountryHover);
    map.on("mouseleave", "country-fills", handleLeave);

    map.easeTo({
      zoom: 3,
      center: [-51.419, -14.72],
      duration: 300,
      essential: true,
    });
  }, [mapRef, cleanupSelections, handleCountryHover, handleLeave]);

  const handleBackNavigation = useCallback(() => {
    if (!mapRef?.current || breadcrumbs.length === 0) return;
    const map = mapRef.current as mapboxgl.Map;
    const currentLevel = breadcrumbs[breadcrumbs.length - 1];

    cleanupSelections(map);

    if (currentLevel?.type === "property") {
      const stateItem = breadcrumbs[breadcrumbs.length - 2];
      if (stateItem && stateItem.type === "state") {
        const stateFeature = map.querySourceFeatures("states", {
          filter: [
            "all",
            ["==", ["get", "country"], stateItem.country || ""],
            ["==", ["get", "state"], stateItem.name],
          ],
        })[0];

        if (stateFeature) {
          const territory = {
            id: stateItem.id,
            name: stateItem.name || "Unknown State",
            type: "state" as const,
            country: stateItem.country || null,
          };

          setSelectedTerritory(territory);

          updateSelection(stateFeature, "states", map);
          zoomToFeature(stateFeature, map, "state");
          setBreadcrumbs((prev) => prev.slice(0, -1));

          fetchForestHistory(stateItem.id);
        }
      }
    } else if (currentLevel?.type === "state") {
      const countryItem = breadcrumbs[0];
      if (countryItem && countryItem.type === "country") {
        const countryFeature = map.querySourceFeatures("countries", {
          filter: ["==", ["get", "country"], countryItem.name],
        })[0];

        if (countryFeature) {
          const territory = {
            id: countryFeature?.properties?.id,
            name: countryFeature?.properties?.country || "Unknown Country",
            type: "country" as const,
            country: null,
          };

          setSelectedTerritory(territory);

          updateSelection(countryFeature, "countries", map);
          zoomToFeature(countryFeature, map, "country");
          setBreadcrumbs((prev) => [prev[0]]);

          fetchForestHistory(countryFeature?.properties?.id);
        }
      }
    } else {
      clearBreadcrumbSelection();
      setSelectedTerritory(null);
      setBreadcrumbs([]);

      const { setForestHistory } = useMapSelector.getState();
      setForestHistory(null);

      map.easeTo({
        zoom: 3,
        duration: 300,
        essential: true,
      });
    }
  }, [
    breadcrumbs,
    clearBreadcrumbSelection,
    cleanupSelections,
    mapRef,
    setBreadcrumbs,
    setSelectedTerritory,
    updateSelection,
    zoomToFeature,
    fetchForestHistory,
  ]);

  useEffect(() => {
    if (!selectedTerritory) return;

    const newBreadcrumbs: BreadcrumbItem[] = [];

    if (selectedTerritory.type === "country") {
      newBreadcrumbs.push({
        id: selectedTerritory.id,
        name: selectedTerritory.name || "Unknown Country",
        type: "country",
        country: null,
      });
    } else if (selectedTerritory.type === "state") {
      newBreadcrumbs.push({
        id: selectedTerritory.country!,
        name: selectedTerritory.country || "Unknown Country",
        type: "country",
        country: null,
      });
      newBreadcrumbs.push({
        id: selectedTerritory.id,
        name: selectedTerritory.name || "Unknown State",
        type: "state",
        country: selectedTerritory.country,
      });
    }

    if (propertyDetails && selectedTerritory.type === "state") {
      newBreadcrumbs.push({
        id: propertyDetails.propertyId?.toString() || "",
        name: propertyDetails?.ibiCode || "Unknown Property",
        type: "property",
        country: selectedTerritory.country,
      });
    }

    setBreadcrumbs(newBreadcrumbs);
  }, [selectedTerritory, propertyDetails, setBreadcrumbs]);

  return (
    <div className="flex items-center">
      {breadcrumbs?.length === 0 ? (
        <IbiIcon icon="mdi:world-box" className="text-lg mr-2" />
      ) : (
        <div onClick={handleBackNavigation} className="flex items-center hover:cursor-pointer hover:opacity-80">
          <IbiIcon icon="mdi:arrow-left" className="text-lg mr-2" />
        </div>
      )}

      {breadcrumbs?.length !== 0 ? (
        breadcrumbs?.map((item, index) => (
          <div key={item?.id} className="flex items-center">
            <button
              onClick={() => handleClick(item, index)}
              className="hover:text-opacity-80 text-white transition-colors font-bold"
            >
              {item?.name || "Unknown"}
            </button>
            {index < breadcrumbs.length - 1 && <span className="mx-2 text-gray-400">|</span>}
          </div>
        ))
      ) : (
        <div className="flex items-center">
          <span>World</span>
        </div>
      )}
    </div>
  );
};

export default MapBreadcrumb;
