import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { IbiConfirmDialogProps } from "@/types";

import { Button } from "../../ui/button";

const IbiConfirmDialog = ({
  title,
  description,
  open,
  onOpenChange,
  onCancel,
  onConfirm,
  ...props
}: IbiConfirmDialogProps) => {
  return (
    <Dialog {...props} open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-[#01050dD9]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="default-clipped" className="rounded-none" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="default-clipped" className="rounded-none" onClick={onConfirm}>
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default IbiConfirmDialog;
