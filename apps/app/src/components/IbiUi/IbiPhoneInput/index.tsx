import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { CountryCode, parsePhoneNumberFromString } from "libphonenumber-js";
import React, { useEffect, useRef, useState } from "react";

import IbiIcon from "../IbiIcon";

interface PhoneCountry {
  code: string;
  countryCode: string;
  flag: string;
  name: string;
}

const PHONE_COUNTRIES: PhoneCountry[] = [
  { code: "+1", countryCode: "US", flag: "circle-flags:us", name: "United States" },
  { code: "+55", countryCode: "BR", flag: "circle-flags:br", name: "Brazil" },
  { code: "+44", countryCode: "GB", flag: "circle-flags:gb", name: "United Kingdom" },
  { code: "+49", countryCode: "DE", flag: "circle-flags:de", name: "Germany" },
  { code: "+33", countryCode: "FR", flag: "circle-flags:fr", name: "France" },
  { code: "+34", countryCode: "ES", flag: "circle-flags:es", name: "Spain" },
  { code: "+39", countryCode: "IT", flag: "circle-flags:it", name: "Italy" },
  { code: "+351", countryCode: "PT", flag: "circle-flags:pt", name: "Portugal" },
  { code: "+31", countryCode: "NL", flag: "circle-flags:nl", name: "Netherlands" },
  { code: "+32", countryCode: "BE", flag: "circle-flags:be", name: "Belgium" },
  { code: "+41", countryCode: "CH", flag: "circle-flags:ch", name: "Switzerland" },
  { code: "+46", countryCode: "SE", flag: "circle-flags:se", name: "Sweden" },
  { code: "+47", countryCode: "NO", flag: "circle-flags:no", name: "Norway" },
  { code: "+45", countryCode: "DK", flag: "circle-flags:dk", name: "Denmark" },
  { code: "+358", countryCode: "FI", flag: "circle-flags:fi", name: "Finland" },
  { code: "+48", countryCode: "PL", flag: "circle-flags:pl", name: "Poland" },
  { code: "+43", countryCode: "AT", flag: "circle-flags:at", name: "Austria" },
  { code: "+420", countryCode: "CZ", flag: "circle-flags:cz", name: "Czech Republic" },
  { code: "+36", countryCode: "HU", flag: "circle-flags:hu", name: "Hungary" },
  { code: "+30", countryCode: "GR", flag: "circle-flags:gr", name: "Greece" },
  { code: "+7", countryCode: "RU", flag: "circle-flags:ru", name: "Russia" },
  { code: "+81", countryCode: "JP", flag: "circle-flags:jp", name: "Japan" },
  { code: "+86", countryCode: "CN", flag: "circle-flags:cn", name: "China" },
  { code: "+82", countryCode: "KR", flag: "circle-flags:kr", name: "South Korea" },
  { code: "+91", countryCode: "IN", flag: "circle-flags:in", name: "India" },
  { code: "+61", countryCode: "AU", flag: "circle-flags:au", name: "Australia" },
  { code: "+64", countryCode: "NZ", flag: "circle-flags:nz", name: "New Zealand" },
  { code: "+52", countryCode: "MX", flag: "circle-flags:mx", name: "Mexico" },
  { code: "+54", countryCode: "AR", flag: "circle-flags:ar", name: "Argentina" },
  { code: "+56", countryCode: "CL", flag: "circle-flags:cl", name: "Chile" },
  { code: "+57", countryCode: "CO", flag: "circle-flags:co", name: "Colombia" },
  { code: "+51", countryCode: "PE", flag: "circle-flags:pe", name: "Peru" },
  { code: "+58", countryCode: "VE", flag: "circle-flags:ve", name: "Venezuela" },
  { code: "+27", countryCode: "ZA", flag: "circle-flags:za", name: "South Africa" },
  { code: "+20", countryCode: "EG", flag: "circle-flags:eg", name: "Egypt" },
  { code: "+971", countryCode: "AE", flag: "circle-flags:ae", name: "UAE" },
  { code: "+966", countryCode: "SA", flag: "circle-flags:sa", name: "Saudi Arabia" },
  { code: "+972", countryCode: "IL", flag: "circle-flags:il", name: "Israel" },
  { code: "+65", countryCode: "SG", flag: "circle-flags:sg", name: "Singapore" },
  { code: "+60", countryCode: "MY", flag: "circle-flags:my", name: "Malaysia" },
  { code: "+90", countryCode: "TR", flag: "circle-flags:tr", name: "Turkey" },
  { code: "+63", countryCode: "PH", flag: "circle-flags:ph", name: "Philippines" },
  { code: "+84", countryCode: "VN", flag: "circle-flags:vn", name: "Vietnam" },
];

interface IbiPhoneInputProps {
  value: string;
  countryCode: string;
  onChange: (value: string, countryCode: string) => void;
  error?: string;
  required?: boolean;
}

const IbiPhoneInput: React.FC<IbiPhoneInputProps> = ({ value, countryCode, onChange, error, required }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const [cursorPosition, setCursorPosition] = useState(0);

  const selectedCountry = PHONE_COUNTRIES.find((c) => c.code === countryCode) || PHONE_COUNTRIES[0];

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
    }
  }, [value, cursorPosition]);

  const handleCountryChange = (newCountryCode: string) => {
    const newCountry = PHONE_COUNTRIES.find((c) => c.code === newCountryCode);
    if (!newCountry) return;

    const rawValue = value.replace(/\D/g, "");
    const phoneNumber = parsePhoneNumberFromString(rawValue, newCountry.countryCode as CountryCode);
    const formatted = phoneNumber ? phoneNumber.formatNational() : "";
    onChange(formatted, newCountry.code);
  };

  const formatPhoneNumber = (value: string, country: PhoneCountry) => {
    const rawValue = value.replace(/\D/g, "");
    const phoneNumber = parsePhoneNumberFromString(rawValue, country.countryCode as CountryCode);
    return phoneNumber ? phoneNumber.formatNational() : value;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    const newValue = input.value;
    setCursorPosition(input.selectionStart || 0);

    // If we have enough digits (more than 6), format the number
    if (newValue.replace(/\D/g, "").length >= 6) {
      const formatted = formatPhoneNumber(newValue, selectedCountry);
      onChange(formatted, countryCode);
    } else {
      onChange(newValue, countryCode);
    }
  };

  const handleBlur = () => {
    const formatted = formatPhoneNumber(value, selectedCountry);
    onChange(formatted, countryCode);
  };

  const filteredCountries = PHONE_COUNTRIES.filter(
    (country) =>
      country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      country.code.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const getMaxLength = (country: PhoneCountry) => {
    const lengths: { [key: string]: number } = {
      US: 14,
      BR: 15,
      GB: 12,
      DE: 15,
      FR: 14,
      ES: 12,
      IT: 13,
      PT: 11,
      NL: 11,
      BE: 12,
      CH: 14,
      SE: 13,
      NO: 11,
      DK: 10,
      FI: 12,
      PL: 11,
      AT: 12,
      CZ: 12,
      HU: 11,
      GR: 14,
      RU: 16,
      JP: 14,
      CN: 13,
      KR: 13,
      IN: 11,
      AU: 13,
      NZ: 12,
      MX: 15,
      AR: 15,
      CL: 12,
      CO: 12,
      PE: 11,
      VE: 15,
      ZA: 12,
      EG: 12,
      AE: 12,
      SA: 11,
      IL: 11,
      SG: 10,
      MY: 12,
      TR: 15,
      PH: 14,
      VN: 13,
    };
    return lengths[country.countryCode] || 15;
  };

  const getPlaceholder = (country: PhoneCountry) => {
    const placeholders: { [key: string]: string } = {
      US: "(*************",
      BR: "(00) 00000-0000",
      GB: "00000 000000",
      DE: "(*************",
      FR: "00 00 00 00 00",
      ES: "000 000 000",
      IT: "************",
      PT: "000 000 000",
      NL: "00 00000000",
      BE: "000 00 00 00",
      CH: "000 000 00 00",
      SE: "000-000 00 00",
      NO: "000 00 000",
      DK: "0000 0000",
      FI: "************",
      PL: "000 000 000",
      AT: "0000 0000000",
      CZ: "000 000 000",
      HU: "00 000 0000",
      GR: "000 0000 0000",
      RU: "(000) 000-00-00",
      JP: "000 0000 0000",
      CN: "000 0000 0000",
      KR: "000 0000 0000",
      IN: "00000 00000",
      AU: "0000 000 000",
      NZ: "************",
      MX: "(*************",
      AR: "(00) 0000-0000",
      CL: "0 0000 0000",
      CO: "************",
      PE: "000 000 000",
      VE: "(*************",
      ZA: "************",
      EG: "00 0000 0000",
      AE: "00 000 0000",
      SA: "00 000 0000",
      IL: "00 000 0000",
      SG: "0000 0000",
      MY: "00 0000 0000",
      TR: "(*************",
      PH: "(*************",
      VN: "************",
    };
    return placeholders[country.countryCode] || "************";
  };

  return (
    <div className="space-y-2">
      <Label>Phone Number {required && <span className="text-red-500">*</span>}</Label>
      <div className="flex gap-2">
        <Select value={countryCode} onValueChange={handleCountryChange}>
          <SelectTrigger className="w-[160px]">
            <SelectValue>
              <div className="flex items-center gap-2">
                <IbiIcon icon={selectedCountry.flag} className="w-5 h-5" />
                <span>{selectedCountry.code}</span>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <div className="p-1 space-y-1">
              <Input
                placeholder="Search country..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="mb-2"
                autoFocus
              />
              <div className="max-h-[300px] overflow-y-auto">
                {filteredCountries.map((country) => (
                  <SelectItem key={country.code} value={country.code}>
                    <div className="flex items-center gap-3">
                      <IbiIcon icon={country.flag} className="w-5 h-5 flex-shrink-0" />
                      <div className="flex-1">
                        <span className="block text-sm font-medium">{country.name}</span>
                        <span className="block text-xs text-muted-foreground">{country.code}</span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </div>
            </div>
          </SelectContent>
        </Select>
        <Input
          ref={inputRef}
          value={value}
          onChange={handlePhoneChange}
          onBlur={handleBlur}
          maxLength={getMaxLength(selectedCountry)}
          className={cn(error && "border-red-500")}
          placeholder={getPlaceholder(selectedCountry)}
        />
      </div>
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default IbiPhoneInput;
