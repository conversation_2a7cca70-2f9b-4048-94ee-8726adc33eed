import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import * as SwitchPrimitives from "@radix-ui/react-switch";
import React from "react";

interface IbiSwitchProps extends SwitchPrimitives.SwitchProps {
  id: string;
  label?: string;
}

const IbiSwitch = ({ id, label, ...props }: IbiSwitchProps) => {
  return (
    <div className="flex items-center space-x-2">
      <Switch id={id} {...props} />
      <Label htmlFor={id}>{label}</Label>
    </div>
  );
};

export default IbiSwitch;
