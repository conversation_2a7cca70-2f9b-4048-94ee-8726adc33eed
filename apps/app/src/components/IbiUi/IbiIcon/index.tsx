"use client";

import { cn } from "@/lib/utils";
import type { IconProps } from "@iconify/react";
import dynamic from "next/dynamic";
import React, { Suspense } from "react";

const IconWrapper = React.forwardRef<HTMLSpanElement, React.HTMLAttributes<HTMLSpanElement>>(
  ({ children, ...props }, ref) => (
    <span ref={ref} {...props}>
      {children}
    </span>
  ),
);
IconWrapper.displayName = "IconWrapper";

const LazyIcon = dynamic(() => import("@iconify/react").then((mod) => mod.Icon), {
  ssr: false,
  loading: () => <span className="text-[0.4rem]">...</span>,
});

const IbiIcon = React.forwardRef<
  HTMLSpanElement,
  IconProps & {
    className?: string;
    icon: string;
    btn?: boolean;
    onClick?: () => void;
    role?: string;
  }
>(({ className, icon, btn, onClick, role, ...props }, ref) => {
  return (
    <IconWrapper ref={ref} className={cn(`inline-flex items-center justify-center`, className)}>
      <Suspense fallback={<div className="text-xs font-extrabold text-primary-2">• • •</div>}>
        <LazyIcon
          role={role}
          icon={icon}
          className={cn(`text-xl ${btn ? "p-[1px] text-2xl hover:bg-[#3637386e] transition cursor-pointer" : ""}`)}
          {...props}
          onClick={onClick}
        />
      </Suspense>
    </IconWrapper>
  );
});

IbiIcon.displayName = "IbiIcon";

export default IbiIcon;
