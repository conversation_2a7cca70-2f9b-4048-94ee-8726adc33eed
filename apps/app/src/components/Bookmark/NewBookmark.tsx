import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/useToast";
import { cn } from "@/lib/utils";
import { useCollections } from "@/queries/hooks/useCollections";
import { useCollectionsMutations } from "@/queries/mutations/collections.mutations";
import { collectionService } from "@/services/collection.service";
import { useState } from "react";

import IbiIcon from "../IbiUi/IbiIcon";
import IbiInput from "../IbiUi/IbiInput";
import { Button } from "../ui/button";

type NewCollectionFormProps = {
  newCollection: {
    name: string;
    description: string;
    isPrivate: boolean;
  };
  setNewCollection: React.Dispatch<
    React.SetStateAction<{
      name: string;
      description: string;
      isPrivate: boolean;
    }>
  >;
  onCancel: () => void;
  onSubmit: (e: React.FormEvent) => void;
  isPending: boolean;
};

const NewCollectionForm = ({
  newCollection,
  setNewCollection,
  onCancel,
  onSubmit,
  isPending,
}: NewCollectionFormProps) => {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <IbiInput
          label="Collection Name"
          className="bg-black/40 p-1"
          id="name"
          value={newCollection.name}
          onChange={(e) => setNewCollection({ ...newCollection, name: e.target.value })}
          required
        />
      </div>
      <div className="space-y-2">
        <IbiInput
          label="Description"
          className="bg-black/40 p-1"
          id="description"
          value={newCollection.description}
          onChange={(e) => setNewCollection({ ...newCollection, description: e.target.value })}
        />
      </div>
      <div className="flex justify-end items-end gap-2">
        <Button type="button" variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isPending}>
          <IbiIcon icon="ph:plus" className="text-xl mr-2" />
          {isPending ? "Creating..." : "Create Collection"}
        </Button>
      </div>
    </form>
  );
};

type NewBookmarkProps = {
  propertyId: string;
};

const NewBookmark = ({ propertyId }: NewBookmarkProps) => {
  const [selectedBookmark, setSelectedBookmark] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [newCollection, setNewCollection] = useState({
    name: "",
    description: "",
    isPrivate: false,
  });
  const [manuallySetCollection, setManuallySetCollection] = useState(false);

  const { collections } = useCollections();
  const { createCollection, bookmarkProperty, unbookmarkProperty } = useCollectionsMutations();

  const handleCreateCollection = (e: React.FormEvent) => {
    e.preventDefault();
    createCollection.mutate(newCollection, {
      onSuccess: () => {
        setIsCreatingCollection(false);
        setNewCollection({ name: "", description: "", isPrivate: false });
      },
    });
  };

  const handleBookmarkClick = async () => {
    try {
      const generalCollection = await collectionService.findOrCreateGeneralCollection();

      bookmarkProperty.mutate(
        { id: generalCollection.id, propertyId },
        {
          onSuccess: () => {
            toast({
              title: "Bookmark Saved",
              description: "Your bookmark has been saved.",
              position: "bottom",
              margin: {
                right: 430,
                bottom: 0,
                left: 0,
              },
              exitDirection: "bottom",
            });
          },
        },
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save bookmark.",
        variant: "destructive",
      });
    }
  };

  const handleMoveToCollection = () => {
    if (!selectedBookmark) return;

    collectionService.findOrCreateGeneralCollection().then((generalCollection) => {
      if (selectedBookmark === generalCollection.id) return;

      bookmarkProperty.mutate(
        { id: selectedBookmark, propertyId },
        {
          onSuccess: () => {
            unbookmarkProperty.mutate(
              { id: generalCollection.id, propertyId },
              {
                onSuccess: () => {
                  setSelectedBookmark(null);
                  toast({
                    title: "Bookmark Moved",
                    description: "Your bookmark has been moved to the selected collection.",
                    position: "bottom",
                    margin: {
                      right: 430,
                      bottom: 0,
                      left: 0,
                    },
                    exitDirection: "bottom",
                  });
                  setIsDialogOpen(false);
                },
              },
            );
          },
        },
      );
    });
  };

  const handleBookmarkSelection = (id: string) => {
    if (selectedBookmark === id) {
      setSelectedBookmark(null);
    } else {
      setSelectedBookmark(id);
    }
  };

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(open) => {
        setIsDialogOpen(open);
        if (!open) {
          handleBookmarkClick();
        }
      }}
    >
      <DialogTrigger asChild>
        <IbiIcon btn icon="ph:bookmark-fill" className="text-3xl text-gray-300 bg-gray-900" />
      </DialogTrigger>
      <DialogContent className="bg-[#01050dD9]">
        <DialogHeader>
          <DialogTitle>
            {!manuallySetCollection
              ? "This landunit has been Bookmarked!"
              : isCreatingCollection
                ? "Create New Collection"
                : "Select a Bookmark Collection"}
          </DialogTitle>
          <DialogDescription>
            {!manuallySetCollection
              ? "You can close this window now. Or add your bookmark to a different collection by clicking the button below."
              : isCreatingCollection
                ? "Fill in the details below to create a new collection."
                : "Choose a collection from the list below to save your bookmark."}
          </DialogDescription>
        </DialogHeader>
        {!manuallySetCollection ? (
          <div className="flex items-center justify-center">
            <Button onClick={() => setManuallySetCollection(true)}>
              <IbiIcon icon="mdi:folder-add-outline" className="text-xl mr-2" />
              New collection
            </Button>
          </div>
        ) : (
          <>
            {isCreatingCollection ? (
              <NewCollectionForm
                newCollection={newCollection}
                setNewCollection={setNewCollection}
                onCancel={() => setIsCreatingCollection(false)}
                onSubmit={handleCreateCollection}
                isPending={createCollection.isPending}
              />
            ) : (
              <div className="flex flex-wrap gap-2 items-center">
                {collections.data?.data.length === 0 ? (
                  <div className="flex items-center justify-center flex-col gap-2 w-full">
                    <p className="text-gray-400">No collections available.</p>
                    <Button onClick={() => setIsCreatingCollection(true)}>
                      <IbiIcon icon="ph:plus" className="text-xl mr-2" />
                      New Collection
                    </Button>
                  </div>
                ) : (
                  <div className="w-full">
                    <div className="flex items-center gap-2 flex-wrap">
                      {collections.data?.data
                        .filter((c) => c.name !== "General")
                        .map((collection) => (
                          <button
                            key={collection.id}
                            className={cn(
                              "flex items-center gap-1.5 bg-gray-600/10 py-2 px-3 w-fit opacity-60 hover:opacity-100",
                              {
                                "bg-gray-600/50 opacity-100": selectedBookmark === collection.id,
                              },
                            )}
                            onClick={() => handleBookmarkSelection(collection.id)}
                          >
                            <IbiIcon icon="line-md:folder" className="text-2xl text-gray-300" />
                            <h1>{collection.name}</h1>
                          </button>
                        ))}
                    </div>
                    <div className="mt-5 flex justify-end items-end gap-2">
                      <Button onClick={() => setIsCreatingCollection(true)}>
                        <IbiIcon icon="ph:plus" className="text-xl mr-2" />
                        New Collection
                      </Button>
                      <Button
                        onClick={handleMoveToCollection}
                        disabled={!selectedBookmark || bookmarkProperty.isPending}
                        className="bg-green-600/50 hover:bg-green-600/80"
                      >
                        <IbiIcon icon="ph:bookmark-fill" className="text-xl mr-2" />
                        {bookmarkProperty.isPending ? "Moving..." : "Move to Collection"}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default NewBookmark;
