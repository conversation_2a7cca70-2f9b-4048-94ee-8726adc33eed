import { Dialog, DialogContent } from "@/components/ui/dialog";
import { toast } from "@/hooks/useToast";
import { useCollectionsMutations } from "@/queries/mutations/collections.mutations";
import { territoriesService } from "@/services/territories.service";
import { handleStorage } from "@/utils/storage";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import IbiIcon from "../IbiUi/IbiIcon";
import { Button } from "../ui/button";
import { Skeleton } from "../ui/skeleton";

interface PropertyDetailsModalProps {
  propertyId: string | null;
  collectionId?: string | null;
  onClose: () => void;
}

const PropertyDetailsModal = ({ propertyId, onClose, collectionId }: PropertyDetailsModalProps) => {
  const [polygonImageUrl, setPolygonImageUrl] = useState<string>("");
  const [imageError, setImageError] = useState(false);
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const { data, isLoading, error } = useQuery({
    queryKey: ["property-details", propertyId],

    // TODO: passed 0 to prevent build fail, fix it later.
    // build test
    queryFn: () => territoriesService.getPropertyDetails(propertyId as string, 0, 0),
    enabled: !!propertyId,
  });
  const router = useRouter();
  const { unbookmarkProperty } = useCollectionsMutations();

  const handleMapNavigation = async () => {
    if (data?.ibiCode) {
      handleStorage("session", "ibiCode", "create", data.ibiCode);
      router.push("/claim");
    }
  };

  const handleSaveBookmark = () => {
    unbookmarkProperty.mutate(
      { id: collectionId as string, propertyId: propertyId as string },
      {
        onSuccess: () => {
          toast({
            title: "Bookmark Removed",
            description: "Your bookmark has been removed from the selected collection.",
          });
          onClose();
        },
        onError: () => {
          toast({
            title: "Error",
            description: "An error occurred while removing the bookmark.",
          });
        },
      },
    );
  };

  const loadPolygonImage = async (ibicode: string) => {
    if (!ibicode) return;

    setIsLoadingImage(true);
    try {
      const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000";
      const response = await fetch(`${baseUrl}/api/map-info/${ibicode}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const encodedGeojson = encodeURIComponent(JSON.stringify(data.geojson));
      const mapboxUrl = `https://api.mapbox.com/styles/v1/geodatin/clawpmxqa000014mrgrn39mtd/static/geojson(${encodedGeojson})/auto/600x400?access_token=${process.env.MAPBOX_TOKEN}`;

      setImageError(false);
      setPolygonImageUrl(mapboxUrl);
    } catch (error) {
      console.error("Error loading polygon image:", error);
      setPolygonImageUrl("");
      setImageError(true);
    } finally {
      setIsLoadingImage(false);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const resetState = () => {
    setPolygonImageUrl("");
    setImageError(false);
    setIsLoadingImage(false);
    onClose();
  };

  useEffect(() => {
    if (data?.ibiCode) {
      loadPolygonImage(data.ibiCode);
    }
  }, [data?.ibiCode]);

  return (
    <Dialog open={!!propertyId} onOpenChange={() => resetState()}>
      <DialogContent className="bg-[#010303]/95 border-gray-800 text-white max-w-xl">
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/2 bg-gray-800" />
            <Skeleton className="h-24 w-full bg-gray-800" />
            <Skeleton className="h-40 w-full bg-gray-800" />
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <IbiIcon icon="ph:warning-circle" className="text-4xl text-red-400 mb-2" />
            <p className="text-gray-400">Failed to load property details</p>
          </div>
        ) : (
          <>
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-1">
                <h2 className="text-xl font-bold">#{data?.ibiCode}</h2>
                <span className="text-sm text-gray-400">ID: {data?.id}</span>
              </div>
              <div className="flex gap-4 text-sm text-gray-400">
                <div>
                  <span className="font-medium">Total Area:</span> {Number(data?.areaHa).toFixed(2)} ha
                </div>
                <div>
                  <span className="font-medium">Forest Area:</span> {data?.totalForestArea.toFixed(2)} ha
                </div>
              </div>
            </div>

            {isLoadingImage ? (
              <div className="w-full h-[300px] bg-gray-800 rounded-lg animate-pulse" />
            ) : (
              <img
                src={
                  imageError ? "https://cdn.rohde-schwarz.com/pws/_tech/images/map-placeholder.png" : polygonImageUrl
                }
                alt="Property polygon"
                className="w-full h-fit object-cover rounded-lg"
                onError={handleImageError}
              />
            )}

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-lg">Ecoregions</h3>
                <span className="text-sm text-gray-400">
                  {data?.propertyEcoregions.length} region{data?.propertyEcoregions.length !== 1 ? "s" : ""}
                </span>
              </div>
              <div className="grid gap-3">
                {data?.propertyEcoregions.map((item) => (
                  <div key={item.ecoregion.code} className="p-4 rounded-lg bg-[#0a0e15] border border-gray-800/30">
                    <div className="space-y-2">
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium">{item.ecoregion.name}</h4>
                        <span className="text-sm text-gray-400">{item.forestArea.toFixed(2)} ha</span>
                      </div>
                      <p className="text-sm text-gray-400">
                        <span className="font-medium">Biome:</span> {item.ecoregion.biomeName}
                      </p>
                      <p className="text-xs text-gray-500">Code: {item.ecoregion.code}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end items-end gap-2">
              {collectionId && (
                <Button onClick={handleSaveBookmark}>
                  <IbiIcon icon="majesticons:bookmark-minus-line" className="mr-2" /> Remove Bookmark
                </Button>
              )}
              <Button onClick={handleMapNavigation}>
                <IbiIcon icon="mingcute:world-2-line" className="mr-2" /> Navigate to property
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PropertyDetailsModal;
