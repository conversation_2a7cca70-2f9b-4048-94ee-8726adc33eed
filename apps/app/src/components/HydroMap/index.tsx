import IbiIcon from "@/components/IbiUi/IbiIcon";
import MapLayers from "@/components/IbiUi/IbiMap/MapLayers";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useBaseMap } from "@/hooks/useBaseMap";
import { useDetectLocation } from "@/hooks/useDetectLocation";
import { useLayerControl } from "@/hooks/useLayerControl";
import { useHydrosheds } from "@/hooks/useLayerControl/useHydrosheds";
import { useHydroshedsManagementStore } from "@/hooks/useLayerControl/useHydrosheds/store";
import useMap from "@/hooks/useMap";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import { CurrentBaseMap } from "@/types";
import { MAPBOX_TOKEN, MAP_PROJECTION, TERRAIN_EXAGGERATION } from "@/utils/constants";
import "mapbox-gl/dist/mapbox-gl.css";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { Map } from "react-map-gl";

import MapFooterToolbar from "../MapFooterToolbar";

// import MapControllerHydroMap from "./MapControllerHydroMap";

const HydroMap = () => {
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const { currentBaseMap, setCurrentBaseMap } = useMap();
  const { mapStyle } = useBaseMap();
  const padding = useMapSelector.use.padding();
  const { latitude, longitude } = useDetectLocation();
  const triggerHydroshedsReload = useMapSelector.use.triggerHydroshedsReload();
  const { setCurrentHydroshedLevel, setSelectedHydroshedLevel } = useHydroshedsManagementStore();

  const [mapLoaded, setLocalMapLoaded] = useState(false);
  const [mapInitialized, setMapInitialized] = useState(false);
  const [hydroshedsInitialized, setHydroshedsInitialized] = useState(false);

  const { handleMapLoad: layerControlMapLoad } = useLayerControl();

  const { clearSelection, setupHydroshedsLayer } = useHydrosheds(mapRef, mapLoaded);

  const defaultViewState = {
    latitude: -14.72,
    longitude: -51.419,
    zoom: 8,
  };

  const [viewState, setViewState] = useState(defaultViewState);

  const clickHandlerRef = useRef<((e: any) => void) | null>(null);

  const isBondPage = usePathname().includes("bond");

  const baseMapOptions = [
    {
      id: 1,
      label: "Gray Scale",
      mapState: "dark" as CurrentBaseMap,
    },
    {
      id: 2,
      label: "Streets",
      mapState: "streets" as CurrentBaseMap,
    },
    {
      id: 3,
      label: "Outdoors",
      mapState: "outdoors" as CurrentBaseMap,
    },
    {
      id: 4,
      label: "Light",
      mapState: "light" as CurrentBaseMap,
    },
    {
      id: 5,
      label: "Navigation Day",
      mapState: "navigation-day" as CurrentBaseMap,
    },
    {
      id: 6,
      label: "Navigation Night",
      mapState: "navigation-night" as CurrentBaseMap,
    },
  ];

  useEffect(() => {
    setSelectedHydroshedLevel({
      id: "hybas_12",
      label: "Hybas 12",
    });
    setCurrentHydroshedLevel("Hybas 12 (default)");
  }, []);

  useEffect(() => {
    if (mapRef.current && mapLoaded) {
      (window as any).__hydroMapInstance = mapRef.current;
    }
  }, [mapRef, mapLoaded]);

  useEffect(() => {
    if (mapLoaded && mapRef.current && !hydroshedsInitialized) {
      clearSelection();

      setupHydroshedsLayer();

      setTimeout(() => {
        triggerHydroshedsReload();
      }, 500);

      setHydroshedsInitialized(true);
    }
  }, [mapLoaded, setupHydroshedsLayer, triggerHydroshedsReload, clearSelection, hydroshedsInitialized]);

  useEffect(() => {
    if (mapLoaded && mapRef.current && hydroshedsInitialized) {
      setTimeout(() => {
        setupHydroshedsLayer();
        triggerHydroshedsReload();
      }, 300);
    }
  }, [currentBaseMap, mapLoaded, hydroshedsInitialized, setupHydroshedsLayer, triggerHydroshedsReload]);

  useEffect(() => {
    if (latitude && longitude && !mapInitialized) {
      setViewState({
        latitude,
        longitude,
        zoom: 8,
      });
      setMapInitialized(true);

      if (mapRef.current && mapLoaded) {
        mapRef.current.flyTo({
          center: [longitude, latitude],
          zoom: 8,
          essential: true,
          duration: 1000,
        });
      }
    }
  }, [latitude, longitude, mapInitialized, mapLoaded]);

  useEffect(() => {
    return () => {
      if (mapRef.current && clickHandlerRef.current) {
        mapRef.current.off("click", clickHandlerRef.current);
      }
    };
  }, []);

  const handleMapLoad = useCallback(
    (event: any) => {
      mapRef.current = event.target;
      setLocalMapLoaded(true);

      layerControlMapLoad(event);

      if (latitude && longitude && mapRef.current) {
        mapRef.current.flyTo({
          center: [longitude, latitude],
          zoom: 8,
          essential: true,
          duration: 1000,
        });
      }

      const map = event.target;
      if (!map.getSource("mapbox-dem")) {
        map.addSource("mapbox-dem", {
          type: "raster-dem",
          url: "mapbox://mapbox.mapbox-terrain-dem-v1",
          tileSize: 512,
          maxzoom: 22,
        });
      }

      const clickHandler = (e: any) => {
        const hydroshedsFeatures = map.queryRenderedFeatures(e.point, {
          layers: ["hydrosheds_fill"],
        });

        if (hydroshedsFeatures.length === 0) {
          clearSelection();
        }
      };

      clickHandlerRef.current = clickHandler;

      map.on("click", clickHandler);
    },
    [latitude, longitude, clearSelection, layerControlMapLoad],
  );

  return (
    <div className="w-full h-full flex flex-col relative">
      <Map
        ref={mapRef as any}
        mapStyle={mapStyle}
        initialViewState={viewState}
        padding={padding}
        mapboxAccessToken={MAPBOX_TOKEN}
        projection={MAP_PROJECTION}
        onLoad={handleMapLoad}
        interactiveLayerIds={["hydrosheds"]}
        style={{ width: "100%", height: "100%" }}
        renderWorldCopies={false}
        attributionControl={false}
        preserveDrawingBuffer={false}
        antialias={false}
        terrain={{ source: "mapbox-dem", exaggeration: TERRAIN_EXAGGERATION }}
        pitchWithRotate={true}
      />

      {/* Map Layers Component */}
      <div
        className={cn(
          "absolute top-[30px] sm:top-[10px] left-0 mt-[70px] ml-3 sm:ml-[75px] flex flex-col gap-2",
          isBondPage && "top-[50px] mt-[100px]",
        )}
      >
        <MapLayers />
      </div>

      {/* Base Map Component */}
      <div
        className={cn(
          "absolute top-[30px] left-0 mt-[100px] ml-3 sm:ml-[75px] flex flex-col gap-2",
          isBondPage && "top-[70px] mt-[90px]",
        )}
      >
        <Popover>
          <PopoverTrigger asChild>
            <button className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-black/80 backdrop-blur-md flex items-center justify-center px-2 gap-2">
              <IbiIcon icon="mi:map" className="text-xl" />
              Base Map
            </button>
          </PopoverTrigger>
          <PopoverContent
            sideOffset={12}
            side="right"
            align="start"
            className="w-[298px] rounded-none border-none p-[1px] bg-[#ffffff2f] layers-box-clip-path relative text-white"
          >
            <div className="layers-box-clip-path bg-[#0A0E15] size-full px-4 pb-6 pt-5">
              <h1 className="text-gray-400 text-[12px]">Base Map</h1>
              <div className="mt-3 flex flex-col gap-2">
                <div
                  className={cn(
                    "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                    currentBaseMap === "default" && "bg-[#ffffff2f]",
                  )}
                  onClick={() => {
                    setCurrentBaseMap("default");
                    setTimeout(() => {
                      triggerHydroshedsReload();
                    }, 500);
                  }}
                >
                  <h2 className="text-gray-300 text-[14px]">Default</h2>
                </div>

                {baseMapOptions.map((option) => (
                  <div
                    key={option.id}
                    className={cn(
                      "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                      currentBaseMap === option.mapState && "bg-[#ffffff2f]",
                    )}
                    onClick={() => {
                      setCurrentBaseMap(option.mapState);
                      setTimeout(() => {
                        triggerHydroshedsReload();
                      }, 500);
                    }}
                  >
                    <h2 className="text-gray-300 text-[14px]">{option.label}</h2>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* <MapControllerHydroMap /> */}
      <div
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.1) 15%, rgba(0,0,0,0.1) 85%, rgba(0,0,0,0.9) 100%)",
          zIndex: 10,
        }}
      />
      <MapFooterToolbar className="absolute bottom-0 left-0 right-0 z-20" />
    </div>
  );
};

export default HydroMap;
