"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React from "react";

import { useDashboard } from "./dashboard.store";

export const DashboardHeader = () => {
  const searchQuery = useDashboard.use.searchQuery();
  const setSearchQuery = useDashboard.use.setSearchQuery();
  const currency = useDashboard.use.currency();
  const setCurrency = useDashboard.use.setCurrency();
  const currencyData = useDashboard.use.currencyData();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <header
      id="dashboard-header"
      className="h-[108px] px-6 max-[380px]:px-3 bg-[#01050dD9] backdrop-blur-md py-2 sticky top-0 z-10 max-[750px]:pt-12 max-[750px]:h-fit"
    >
      <div className="h-full max-w-7xl mx-auto flex justify-between flex-col">
        <div className="flex items-center gap-2 justify-between">
          <Link href="/dashboard" className="flex items-center gap-2">
            <IbiIcon icon="tdesign:dashboard-1-filled" className="text-xl max-[380px]:text-sm" />
            <h1 className="text-2xl font-bold max-[380px]:text-sm">Explore</h1>
          </Link>
          <div className="flex items-center gap-1">
            <button
              onClick={() => router.push("/explore/users")}
              className={cn(
                "bg-gray-800 rounded-lg px-2 py-1 text-gray-400 hover:bg-gray-700 transition-colors duration-300 text-sm max-[380px]:text-xs",
                pathname === "/explore/users" && "bg-gray-700",
              )}
            >
              Tokens
            </button>
            <button
              onClick={() => router.push("/explore/landunits")}
              className={cn(
                "bg-gray-800 rounded-lg px-2 py-1 text-gray-400 hover:bg-gray-700 transition-colors duration-300 text-sm max-[380px]:text-xs",
                pathname === "/explore/landunits" && "bg-gray-700",
              )}
            >
              Land units
            </button>
            <button
              onClick={() => router.push("/explore/quests")}
              className={cn(
                "bg-gray-800 rounded-lg px-2 py-1 text-gray-400 hover:bg-gray-700 transition-colors duration-300 text-sm max-[380px]:text-xs",
                pathname === "/explore/quests" && "bg-gray-700",
              )}
            >
              Quests
            </button>
          </div>
        </div>
        <div className="w-full mx-auto flex justify-between items-end">
          <Input
            type="text"
            placeholder="Asset, wallet, domain or identity"
            className="w-full max-w-[440px] bg-gray-800 rounded-lg px-4 py-2 text-gray-300"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div className="flex items-center gap-1">
            <Popover>
              <PopoverTrigger className="flex gap-0.5 items-center hover:bg-gray-700 rounded-lg p-2 transition-colors duration-300">
                <IbiIcon icon="fa-solid:gas-pump" className="text-sm" />
                <IbiIcon icon="ic:baseline-keyboard-arrow-down" className="text-sm" />
              </PopoverTrigger>
              <PopoverContent className="border border-gray-600 outline-none bg-primary-dark text-gray-100 p-4 w-[250px]">
                <div className="flex flex-col gap-3">
                  <div className="flex items-center justify-between text-sm">
                    <h3 className="font-medium">Ethereum gas prices</h3>
                    <IbiIcon icon="mingcute:question-line" className="text-sm opacity-50" />
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <IbiIcon icon="ph:rocket-duotone" className="text-sm" />
                      <span>Fast</span>
                    </div>
                    <span>8 GWEI</span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <IbiIcon icon="ph:car-duotone" className="text-sm" />
                      <span>Average</span>
                    </div>
                    <span>8 GWEI</span>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger className="flex gap-0.5 items-center hover:bg-gray-700 rounded-lg p-2 transition-colors duration-300">
                {currency}
                <IbiIcon icon="ic:baseline-keyboard-arrow-down" className="text-sm" />
              </PopoverTrigger>
              <PopoverContent className="border border-gray-600 outline-none bg-primary-dark text-gray-100 p-2 w-[280px]">
                <div className="flex flex-col">
                  {currencyData.map(({ code, name, icon }) => (
                    <button
                      key={code}
                      className="flex items-center gap-3 p-2 hover:bg-gray-800 rounded-lg transition-colors"
                      onClick={() => setCurrency(code)}
                    >
                      <div className="w-5 h-5 rounded-full bg-gray-700 flex items-center justify-center">
                        <IbiIcon icon={icon} className="text-sm" />
                      </div>
                      <span>{code}</span>
                      <span className="text-gray-400 text-sm">{name}</span>
                      {currency === code && <IbiIcon icon="lucide:check" className="text-blue-400 ml-auto" />}
                    </button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;
