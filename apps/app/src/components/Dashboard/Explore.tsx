"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import Link from "next/link";
import React from "react";

import { useDashboard } from "./dashboard.store";

export const ExploreDashboard = () => {
  const formatPrice = useDashboard.use.formatPrice();
  const filteredMemeCoins = useDashboard.use.filteredMemeCoins();
  const filteredStakingTokens = useDashboard.use.filteredStakingTokens();
  const filteredCryptoIndexes = useDashboard.use.filteredCryptoIndexes();
  const filteredDefiTokens = useDashboard.use.filteredDefiTokens();
  const filteredNFTs = useDashboard.use.filteredNFTs();

  return (
    <div className="max-w-6xl mx-auto mt-7 px-4 sm:px-6 lg:px-8">
      <h1 className="text-4xl font-bold mb-8">Explore</h1>
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-12">
        <div className="bg-green-400 bg-opacity-20 p-4 rounded-xl flex items-center gap-2">
          <span>Top Gainers</span>
          <IbiIcon icon="ant-design:rise-outlined" className="text-sm" />
        </div>
        <div className="bg-red-400 bg-opacity-20 p-4 rounded-xl flex items-center gap-2">
          <span>Top Losers</span>
          <IbiIcon icon="ant-design:fall-outlined" className="text-sm" />
        </div>
        <div className="bg-teal-400 bg-opacity-20 p-4 rounded-xl flex items-center gap-2">
          <span>Market</span>
          <IbiIcon icon="octicon:graph-16" className="text-sm" />
        </div>
        <div className="bg-orange-400 bg-opacity-20 p-4 rounded-xl flex items-center gap-2">
          <span>Pools</span>
          <IbiIcon icon="clarity:resource-pool-outline-badged" className="text-sm" />
        </div>
      </div>

      {filteredMemeCoins.length > 0 && (
        <div className="mb-12">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-2xl font-bold">Meme Coins</h2>
              <p className="text-gray-500">
                Meme coins are digital currencies that take inspiration from internet memes and narratives
              </p>
            </div>
            <a href="#" className="text-blue-400">
              See all
            </a>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {filteredMemeCoins.map((coin, i) => (
              <Link href="/explore/tokens" key={i} className="bg-gray-800 rounded-xl p-4 sm:p-6">
                <div className="flex items-center gap-3 mb-4">
                  <img src={coin.image} alt={coin.name} className="w-8 h-8 rounded-full" />
                  <span>{coin.name}</span>
                </div>
                <div>
                  <div className="text-gray-500 text-sm">Price</div>
                  <div className="flex items-center gap-2">
                    <span className="text-xl">{formatPrice(coin.price, "8")}</span>
                    <span className="text-green-400">+{coin.change}%</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {filteredStakingTokens.length > 0 && (
        <div className="mb-12">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-2xl font-bold">ETH: Staking and Restaking</h2>
              <p className="text-gray-500">Put your ETH to work with staking protocols</p>
            </div>
            <a href="#" className="text-blue-400">
              See all
            </a>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {filteredStakingTokens.map((token, i) => (
              <Link href="/explore/tokens" key={i} className="bg-gray-800 rounded-xl p-4 sm:p-6">
                <div className="flex items-center gap-3 mb-4">
                  <img src={token.image} alt={token.name} className="w-8 h-8 rounded-full" />
                  <span className="truncate flex-1 text-sm sm:text-base">{token.name}</span>
                </div>
                <div>
                  <div className="text-gray-500 text-sm">Price</div>
                  <div className="flex items-center gap-2">
                    <span className="text-xl">{formatPrice(token.price, "2")}</span>
                    <span className="text-green-400">+{token.change}%</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {filteredCryptoIndexes.length > 0 && (
        <div className="mb-12">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <div>
              <h2 className="text-2xl font-bold">Crypto Indexes</h2>
              <p className="text-gray-500">Tokenized baskets of high quality crypto projects</p>
            </div>
            <a href="#" className="text-blue-400">
              See all
            </a>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {filteredCryptoIndexes.map((index, i) => (
              <Link href="/explore/tokens" key={i} className="bg-gray-800 rounded-xl p-4 sm:p-6">
                <div className="flex items-center gap-3 mb-4">
                  <img src={index.image} alt={index.name} className="w-8 h-8 rounded-full" />
                  <span className="flex items-center gap-1">
                    {index.name}
                    {index.verified && <IbiIcon icon="lucide:verified" className="text-blue-400 text-sm" />}
                  </span>
                </div>
                <div>
                  <div className="text-gray-500 text-sm">Price</div>
                  <div className="flex items-center gap-2">
                    <span className="text-xl">{formatPrice(index.price, "2")}</span>
                    <span className={`${index.change >= 0 ? "text-green-400" : "text-red-400"}`}>
                      {index.change >= 0 ? "+" : ""}
                      {index.change}%
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {filteredDefiTokens.length > 0 && (
        <div className="mb-12">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-2xl font-bold">DeFi Blue Chips</h2>
              <p className="text-gray-500">Top DeFi tokens by Market Cap</p>
            </div>
            <a href="#" className="text-blue-400">
              See all
            </a>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {filteredDefiTokens.map((token, i) => (
              <Link href="/explore/tokens" key={i} className="bg-gray-800 rounded-xl p-4 sm:p-6">
                <div className="flex items-center gap-3 mb-4">
                  <img src={token.image} alt={token.name} className="w-8 h-8 rounded-full" />
                  <span className="flex items-center gap-1">
                    {token.name}
                    {token.verified && <IbiIcon icon="lucide:verified" className="text-blue-400 text-sm" />}
                  </span>
                </div>
                <div>
                  <div className="text-gray-500 text-sm">Price</div>
                  <div className="flex items-center gap-2">
                    <span className="text-xl">{formatPrice(token.price, "2")}</span>
                    <span className={`${token.change >= 0 ? "text-green-400" : "text-red-400"}`}>
                      {token.change >= 0 ? "+" : ""}
                      {token.change}%
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {filteredNFTs.length > 0 && (
        <div className="mb-12">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-2xl font-bold">Trending NFTs for 24h</h2>
              <p className="text-gray-500">Most popular NFT collections by volume</p>
            </div>
            <a href="#" className="text-blue-400">
              See all
            </a>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {filteredNFTs.map((nft, i) => (
              <Link href="/explore/tokens" key={i} className="bg-gray-800 rounded-xl p-4 sm:p-6">
                <div className="flex items-center gap-3 mb-4">
                  <img src={nft.image} alt={nft.name} className="w-8 h-8 rounded-full" />
                  <span>{nft.name}</span>
                </div>
                <div className="flex flex-col gap-2">
                  <div>
                    <div className="text-gray-500 text-sm">Floor Price</div>
                    <div className="flex items-center gap-2">
                      <span className="text-xl">Ξ{nft.price}</span>
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500 text-sm">Volume</div>
                    <div className="flex items-center gap-2">
                      <span>Ξ{nft.volume}</span>
                      {nft.volumeChange > 0 && <span className="text-green-400">+{nft.volumeChange}%</span>}
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ExploreDashboard;
