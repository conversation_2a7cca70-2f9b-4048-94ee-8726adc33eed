import { useMapSelector } from "@/stores/map.store";
import { ForestHistoryHydroshed } from "@/types";
import { useCallback } from "react";

import usePrivyAuth from "./usePrivyAuth";

export const useForestHistoryHydrosheds = () => {
  const { setForestHistoryHydroshed, setIsLoadingForestHistoryHydroshed, setForestHistoryHydroshedError } =
    useMapSelector((state) => ({
      setForestHistoryHydroshed: state.setForestHistoryHydroshed,
      setIsLoadingForestHistoryHydroshed: state.setIsLoadingForestHistoryHydroshed,
      setForestHistoryHydroshedError: state.setForestHistoryHydroshedError,
    }));

  const { getJWT } = usePrivyAuth();

  const fetchForestHistoryHydroshed = useCallback(
    async (id: string) => {
      if (!id) return;

      try {
        setIsLoadingForestHistoryHydroshed(true);
        setForestHistoryHydroshedError(null);

        const jwt = await getJWT();
        const response = await fetch(`https://api.yby.energy/api/hydrosheds/forest-history?hybasId=${id}`, {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch property forest history");
        }

        const propertyData = await response.json();

        const transformedData: ForestHistoryHydroshed = {
          id: propertyData.id,
          hybasId: propertyData.hybasId,
          forestHistory: propertyData.forestHistory,
          areaHa: propertyData.areaHa,
          createdAt: propertyData.createdAt,
          updatedAt: propertyData.updatedAt,
          ecoregions: propertyData.ecoregions,
        };

        setForestHistoryHydroshed(transformedData);
      } catch (err) {
        console.error("Error fetching property forest history:", err);
        setForestHistoryHydroshedError(err instanceof Error ? err.message : "Failed to fetch property forest history");
        setForestHistoryHydroshed(null);
      } finally {
        setIsLoadingForestHistoryHydroshed(false);
      }
    },
    [getJWT, setForestHistoryHydroshed, setForestHistoryHydroshedError, setIsLoadingForestHistoryHydroshed],
  );

  return {
    fetchForestHistoryHydroshed,
  };
};
