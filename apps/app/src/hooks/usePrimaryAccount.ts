import { useMemo } from "react";

import usePrivyAuth from "./usePrivyAuth";

interface PrimaryAccountInfo {
  displayName: string;
  username: string | null;
  avatarLetter: string;
}

export const usePrimaryAccount = (): PrimaryAccountInfo => {
  const { user } = usePrivyAuth();

  return useMemo(() => {
    if (!user?.linkedAccounts?.length) {
      return {
        displayName: "Anonymous User",
        username: null,
        avatarLetter: "A",
      };
    }

    const priorityTypes = [
      "github_oauth",
      "google_oauth",
      "twitter_oauth",
      "discord_oauth",
      "linkedin_oauth",
      "instagram_oauth",
      "email",
      "wallet",
    ];

    let primaryAccount: any = null;
    for (const type of priorityTypes) {
      primaryAccount = user.linkedAccounts.find((account) => account.type === type);
      if (primaryAccount) break;
    }

    if (!primaryAccount) {
      primaryAccount = user.linkedAccounts[0];
    }

    let displayName = "Anonymous User";
    let username: string | null = null;
    let avatarLetter = "A";

    switch (primaryAccount.type) {
      case "github_oauth":
        displayName = primaryAccount.name || primaryAccount.username;
        username = primaryAccount.username;
        avatarLetter = (primaryAccount.name?.[0] || primaryAccount.username?.[0] || "A").toUpperCase();
        break;
      case "google_oauth":
        displayName = primaryAccount.name || primaryAccount.email;
        username = primaryAccount.email;
        avatarLetter = (primaryAccount.name?.[0] || primaryAccount.email?.[0] || "A").toUpperCase();
        break;
      case "twitter_oauth":
        displayName = primaryAccount.name || primaryAccount.username;
        username = primaryAccount.username;
        avatarLetter = (primaryAccount.name?.[0] || primaryAccount.username?.[0] || "A").toUpperCase();
        break;
      case "discord_oauth":
        displayName = primaryAccount.username;
        username = primaryAccount.username;
        avatarLetter = (primaryAccount.username?.[0] || "A").toUpperCase();
        break;
      case "linkedin_oauth":
        displayName = primaryAccount.name || "LinkedIn User";
        username = primaryAccount.email;
        avatarLetter = (primaryAccount.name?.[0] || "L").toUpperCase();
        break;
      case "instagram_oauth":
        displayName = primaryAccount.username;
        username = primaryAccount.username;
        avatarLetter = (primaryAccount.username?.[0] || "I").toUpperCase();
        break;
      case "email":
        displayName = primaryAccount.address;
        username = primaryAccount.address;
        avatarLetter = (primaryAccount.address?.[0] || "E").toUpperCase();
        break;
      case "wallet":
      case "smart_wallet":
        displayName = `${primaryAccount.address.slice(0, 6)}...${primaryAccount.address.slice(-4)}`;
        username = displayName;
        avatarLetter = "W";
        break;
      default:
        displayName = "Connected User";
        username = null;
        avatarLetter = "C";
    }

    return { displayName, username, avatarLetter };
  }, [user]);
};
