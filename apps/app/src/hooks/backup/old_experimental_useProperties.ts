import { useClaimedProperties } from "@/queries/hooks/useTerritories";
import { PROPERTIES_MAX_ZOOM, PROPERTIES_MIN_ZOOM } from "@/utils/constants";
import { useCallback, useEffect, useRef } from "react";

export function useProperties(mapRef: React.RefObject<mapboxgl.Map | null>, propertyId: string, mapLoaded: boolean) {
  const currentHoveredId = useRef<number | null>(null);
  const currentSelectedId = useRef<number | null>(null);

  const { data: claimedProperties } = useClaimedProperties();

  const onMouseMove = useCallback(
    (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef.current;
      if (!map || !e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as number;

      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "properties-source", sourceLayer: "properties", id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "properties-source", sourceLayer: "properties", id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    },
    [mapRef],
  );

  const cleanupLayers = useCallback(() => {
    const map = mapRef.current;
    if (!map) return;

    // Remove event listeners
    map.off("mousemove", "properties", onMouseMove); // Updated to use onMouseMove
    map.off("mouseleave", "properties", () => {});
    map.off("click", "properties", () => {});

    // Remove layers in reverse order
    ["properties-border", "properties-glow-3", "properties-glow-2", "properties-glow-1", "properties"].forEach(
      (layerId) => {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
      },
    );

    // Remove source
    if (map.getSource("properties-source")) {
      map.removeSource("properties-source");
    }

    // Reset refs
    currentHoveredId.current = null;
    currentSelectedId.current = null;
  }, [mapRef, onMouseMove]);

  const clearSelection = useCallback(() => {
    const map = mapRef.current;
    if (!map) return;

    if (currentSelectedId.current !== null) {
      map.setFeatureState(
        { source: "properties-source", sourceLayer: "properties", id: currentSelectedId.current },
        { selected: false },
      );
      currentSelectedId.current = null;
    }
  }, [mapRef]);

  const setupPropertyLayer = useCallback(() => {
    const map = mapRef.current;
    if (!map || !propertyId || !mapLoaded) return;

    cleanupLayers();

    try {
      map.addSource("properties-source", {
        type: "vector",
        tiles: [`https://tiles.yby.energy/dev/properties-${propertyId}/{z}/{x}/{y}`],
        minzoom: PROPERTIES_MIN_ZOOM,
        maxzoom: PROPERTIES_MAX_ZOOM,
        promoteId: "IBI_COD",
      });

      map.addLayer({
        id: "properties",
        type: "fill",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "fill-color": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            [
              "match",
              ["get", "IBI_COD"],
              (claimedProperties || []).map((prop) => prop.ibiCode),
              "rgba(255, 215, 0, 0.2)",
              "#ffffff",
            ],
            [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              [
                "match",
                ["get", "IBI_COD"],
                (claimedProperties || []).map((prop) => prop.ibiCode),
                "#CFB53B", // New hover color for claimed properties
                "#768FFF", // Original hover color for unclaimed properties
              ],
              [
                "match",
                ["get", "IBI_COD"],
                (claimedProperties || []).map((prop) => prop.ibiCode),
                "#FFD700",
                "#5C79FA",
              ],
            ],
          ],
          "fill-opacity": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            0.2,
            [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              0.8,
              ["match", ["get", "IBI_COD"], (claimedProperties || []).map((prop) => prop.ibiCode), 0.6, 0.8],
            ],
          ],
          "fill-outline-color": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            "#FFBF00",
            "#ffffff",
          ],
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      });

      map.addLayer({
        id: "properties-glow-1",
        type: "line",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "line-color": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            "#FFBF00",
            ["case", ["boolean", ["feature-state", "selected"], false], "#768FFF", "rgba(0, 0, 0, 0)"],
          ],
          "line-width": 5,
          "line-opacity": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            0.7,
            ["case", ["boolean", ["feature-state", "selected"], false], 1, 0],
          ],
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      });

      map.addLayer({
        id: "properties-glow-2",
        type: "line",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "line-color": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            "rgba(255, 191, 0, 0.7)",
            ["case", ["boolean", ["feature-state", "selected"], false], "#768FFF", "rgba(0, 0, 0, 0)"],
          ],
          "line-width": 12,
          "line-opacity": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            0.5,
            ["case", ["boolean", ["feature-state", "selected"], false], 0.7, 0],
          ],
          "line-blur": 5,
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      });

      map.addLayer({
        id: "properties-glow-3",
        type: "line",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "line-color": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            "rgba(255, 191, 0, 0.5)",
            ["case", ["boolean", ["feature-state", "selected"], false], "#768FFF", "rgba(0, 0, 0, 0)"],
          ],
          "line-width": 24,
          "line-opacity": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            0.35,
            ["case", ["boolean", ["feature-state", "selected"], false], 0.4, 0],
          ],
          "line-blur": 13,
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      });

      map.addLayer({
        id: "properties-border",
        type: "line",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "line-color": [
            "match",
            ["get", "IBI_COD"],
            (claimedProperties || []).map((prop) => prop.ibiCode),
            "#FFBF00",
            ["case", ["boolean", ["feature-state", "selected"], false], "#ffffff", "#ffffff"],
          ],
          "line-width": [
            "interpolate",
            ["linear"],
            ["zoom"],
            2,
            0.2, // Thinner at zoom level 2
            4,
            0.5, // Thinner at zoom level 4
            6,
            0.8, // Thinner at zoom level 6
            8,
            1, // Thinner at zoom level 8
            10,
            1.5, // Thinner at zoom level 10
          ],
          "line-opacity": 1,
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      });

      map.on("mousemove", "properties", onMouseMove);

      map.on("mouseleave", "properties", () => {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "properties-source", sourceLayer: "properties", id: currentHoveredId.current },
            { hover: false },
          );
          currentHoveredId.current = null;
        }

        map.getCanvas().style.cursor = "";
      });

      map.on("click", "properties", (e) => {
        if (!e.features?.length || e.features[0].id === undefined) return;

        const featureId = e.features[0].id as number;

        if (currentSelectedId.current !== null) {
          map.setFeatureState(
            { source: "properties-source", sourceLayer: "properties", id: currentSelectedId.current },
            { selected: false },
          );
        }

        map.setFeatureState(
          { source: "properties-source", sourceLayer: "properties", id: featureId },
          { selected: true },
        );

        currentSelectedId.current = featureId;
      });

      // Add click handler for the entire map
      map.on("click", (e) => {
        // Check if the click was on a property
        const features = map.queryRenderedFeatures(e.point, { layers: ["properties"] });

        // If no properties were clicked, clear selection
        if (!features.length) {
          clearSelection();
        }
      });

      console.log("Property layer setup complete");
    } catch (error) {
      console.error("Error setting up property layer:", error);
    }
  }, [propertyId, mapLoaded, cleanupLayers, claimedProperties, clearSelection, onMouseMove]);

  useEffect(() => {
    if (mapLoaded) {
      setupPropertyLayer();
    }
  }, [propertyId, mapLoaded, setupPropertyLayer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearSelection();
      cleanupLayers();
    };
  }, [clearSelection, cleanupLayers]);

  return {
    setupPropertyLayer,
    cleanupLayers,
    onMouseMove,
    clearSelection,
  };
}
