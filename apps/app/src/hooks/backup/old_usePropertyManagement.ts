import useMap from "@/hooks/useMap";
import { useToast } from "@/hooks/useToast";
import { useClaimedProperties } from "@/queries/hooks/useTerritories";
import { useMapSelector } from "@/stores/map.store";
import { PropertyDetails } from "@/types";
import { PROPERTIES_MIN_ZOOM } from "@/utils/constants";
import { makeRequest } from "@/utils/make-request";
import debounce from "lodash/debounce";
import throttle from "lodash/throttle";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { FillLayer, MapLayerMouseEvent } from "react-map-gl";
import { create } from "zustand";

import usePrivyAuth from "../usePrivyAuth";

interface PopupInfo {
  lngLat: { lng: number; lat: number };
  propertyDetails: PropertyDetails | null;
  isLoading: boolean;
  error?: string;
  propertyId: number | null;
}

interface PropertyManagementState {
  isStateSelected: boolean;
  selectedStateId: string | null;
}

interface PropertyManagementStoreState {
  selectedPropertyDetails: PropertyDetails | null;
  setSelectedPropertyDetails: (details: PropertyDetails | null) => void;
  isLoadingProperty: boolean;
  setIsLoadingProperty: (isLoading: boolean) => void;
}

interface SpatialIndex {
  index: Map<string, number[]>;
  resolution: number;
  bounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  add: (id: number, minX: number, minY: number, maxX: number, maxY: number) => void;
  query: (x: number, y: number) => number[];
  clear: () => void;
}

function createSpatialIndex(resolution = 0.5): SpatialIndex {
  const index = new Map<string, number[]>();

  const bounds = {
    minX: -180,
    minY: -90,
    maxX: 180,
    maxY: 90,
  };

  const getCellKey = (x: number, y: number): string => {
    const cellX = Math.floor((x - bounds.minX) / resolution);
    const cellY = Math.floor((y - bounds.minY) / resolution);
    return `${cellX}:${cellY}`;
  };

  const add = (id: number, minX: number, minY: number, maxX: number, maxY: number): void => {
    const minCellX = Math.floor((minX - bounds.minX) / resolution);
    const minCellY = Math.floor((minY - bounds.minY) / resolution);
    const maxCellX = Math.floor((maxX - bounds.minX) / resolution);
    const maxCellY = Math.floor((maxY - bounds.minY) / resolution);

    for (let x = minCellX; x <= maxCellX; x++) {
      for (let y = minCellY; y <= maxCellY; y++) {
        const key = `${x}:${y}`;
        if (!index.has(key)) {
          index.set(key, []);
        }
        index.get(key)!.push(id);
      }
    }
  };

  const query = (x: number, y: number): number[] => {
    const key = getCellKey(x, y);
    if (!index.has(key)) return [];

    const uniqueIds = Array.from(new Set(index.get(key)!));
    return uniqueIds;
  };

  const clear = (): void => {
    index.clear();
  };

  return { index, resolution, bounds, add, query, clear };
}

const usePropertyManagementStore = create<PropertyManagementStoreState>((set) => ({
  selectedPropertyDetails: null,
  setSelectedPropertyDetails: (details) => set({ selectedPropertyDetails: details }),
  isLoadingProperty: false,
  setIsLoadingProperty: (isLoading) => set({ isLoadingProperty: isLoading }),
}));

export const usePropertyManagement = () => {
  const [popupInfo, setPopupInfo] = useState<PopupInfo | null>(null);
  const currentPropertyIdRef = useRef<string | null>(null);
  const hoveredStateIdRef = useRef<string | null>(null);
  const selectedPropertyIdRef = useRef<string | null>(null);
  const [hoveredFeatureId, setHoveredFeatureId] = useState<string | null>(null);
  const [clickedFeatureId, setClickedFeatureId] = useState<string | null>(null);
  const [propertyManagementState, setPropertyManagementState] = useState<PropertyManagementState>({
    isStateSelected: false,
    selectedStateId: null,
  });

  const featureStateCache = useRef(new Map<string, { hover: boolean; selected: boolean }>());
  const lastMousePos = useRef({ x: 0, y: 0 });
  const propertyDetailsCache = useRef(new Map<string, PropertyDetails>());

  const { mapRef } = useMap();
  const { getJWT } = usePrivyAuth();
  const { toast } = useToast();

  const { data: claimedPropertiesData, error: claimedPropertiesError } = useClaimedProperties();

  useEffect(() => {
    if (claimedPropertiesError) {
      console.error("Failed to load claimed properties:", claimedPropertiesError);
      toast({
        title: "Error",
        description: "Failed to load claimed properties",
        variant: "destructive",
      });
    }
  }, [claimedPropertiesError, toast]);

  const { selectedPropertyDetails, setSelectedPropertyDetails, isLoadingProperty, setIsLoadingProperty } =
    usePropertyManagementStore();

  const isTransitioning = useMapSelector.use.isTransitioning();

  const debouncedSetPopupInfo = useRef(
    debounce((info: PopupInfo | null) => {
      setPopupInfo(info);
    }, 150),
  ).current;

  const claimedIbiCodes = useMemo(() => {
    if (!claimedPropertiesData) return [];
    return claimedPropertiesData
      .filter((prop) => prop.claims.some((claim) => claim.status === "APPROVED"))
      .map((prop) => prop.ibiCode);
  }, [claimedPropertiesData]);

  const propertyLayer: FillLayer = useMemo(
    () => ({
      id: "properties",
      type: "fill",
      source: "properties-source",
      "source-layer": "properties",
      paint: {
        "fill-color": [
          "case",
          ["==", ["get", "IBI_COD"], hoveredFeatureId],
          ["case", ["in", ["get", "IBI_COD"], ["literal", claimedIbiCodes]], "#82E0AA", "#13D4EC"],
          ["in", ["get", "IBI_COD"], ["literal", claimedIbiCodes]],
          "#27AE60",
          "#34B4B8",
        ],
        "fill-opacity": ["case", ["==", ["get", "IBI_COD"], clickedFeatureId], 0, 0.5],
        "fill-outline-color": "#fff",
      },
      minzoom: propertyManagementState.isStateSelected ? 1 : PROPERTIES_MIN_ZOOM,
    }),
    [claimedIbiCodes, hoveredFeatureId, clickedFeatureId, propertyManagementState.isStateSelected],
  );

  const spatialIndex = useRef<SpatialIndex>(createSpatialIndex(0.5));

  const updateFeatureState = useCallback(
    (featureId: string, state: "hover" | "selected", value: boolean) => {
      if (!mapRef?.current) return;

      const mapboxMap = (mapRef?.current as any).getMap();

      try {
        const features = mapboxMap.querySourceFeatures("properties-source", {
          sourceLayer: "properties",
          filter: ["==", ["get", "IBI_COD"], featureId],
        });

        features.forEach((feature) => {
          if (feature.id) {
            mapboxMap.setFeatureState(
              {
                source: "properties-source",
                sourceLayer: "properties",
                id: feature.id,
              },
              { [state]: value },
            );
          }
        });
      } catch (error) {
        console.error("Error updating feature state:", error);
      }
    },
    [mapRef],
  );

  const updateStateSelection = useCallback(
    (stateId: string | null) => {
      setPropertyManagementState((prev) => ({
        ...prev,
        isStateSelected: !!stateId,
        selectedStateId: stateId,
      }));

      if (mapRef?.current) {
        const mapboxMap = (mapRef.current as any).getMap();
        if (mapboxMap.getLayer("properties")) {
          mapboxMap.setLayerZoomRange("properties", stateId ? 1 : PROPERTIES_MIN_ZOOM, 24);
        }
      }
    },
    [mapRef],
  );

  const clearSelection = useCallback(() => {
    if (!mapRef?.current) return;

    setSelectedPropertyDetails(null);

    const mapboxMap = (mapRef?.current as any).getMap();

    if (selectedPropertyIdRef.current !== null) {
      updateFeatureState(selectedPropertyIdRef.current, "selected", false);

      const layersToRemove = [
        "selected-property-outline-glow-3",
        "selected-property-outline-glow-2",
        "selected-property-outline-glow-1",
        "selected-property-outline",
        "selected-property-fill",
        "mask",
      ];

      layersToRemove.forEach((layerId) => {
        if (mapboxMap.getLayer(layerId)) {
          mapboxMap.removeLayer(layerId);
        }
      });

      const sourcesToRemove = ["selected-feature", "selected-outline"];
      sourcesToRemove.forEach((sourceId) => {
        if (mapboxMap.getSource(sourceId)) {
          mapboxMap.removeSource(sourceId);
        }
      });

      selectedPropertyIdRef.current = null;
      setClickedFeatureId(null);
      setSelectedPropertyDetails(null);
    }
  }, [mapRef, setSelectedPropertyDetails, updateFeatureState]);

  const fetchPropertyDetails = useCallback(
    async (id: string | number): Promise<PropertyDetails> => {
      const jwt = await getJWT();
      const { result, error } = await makeRequest<PropertyDetails>(
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        },
        `/api/properties/${id}/details`,
      );

      if (error) {
        throw new Error("Failed to fetch property details");
      }

      return result as PropertyDetails;
    },
    [getJWT],
  );

  const updatePopupPosition = useCallback(
    (e: MapLayerMouseEvent) => {
      if (!mapRef?.current || !popupInfo) return;

      const popupOffset = 150;
      const point = mapRef.current.project([e.lngLat.lng, e.lngLat.lat]);
      const popupPoint = mapRef.current.unproject([point.x, point.y - popupOffset]);

      setPopupInfo((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          lngLat: {
            lng: popupPoint.lng,
            lat: popupPoint.lat,
          },
        };
      });
    },
    [mapRef, popupInfo],
  );

  const onMouseMove = useCallback(
    throttle(
      (e: MapLayerMouseEvent) => {
        const mapboxMap = (mapRef?.current as any)?.getMap();
        if (!mapRef?.current || isTransitioning || !mapboxMap?.loaded()) return;

        const currentPos = { x: e.point.x, y: e.point.y };
        const lastPos = lastMousePos.current;
        const distance = Math.sqrt(Math.pow(currentPos.x - lastPos.x, 2) + Math.pow(currentPos.y - lastPos.y, 2));

        if (distance < 5) return;
        lastMousePos.current = currentPos;

        const features = mapboxMap.queryRenderedFeatures(e.point, {
          layers: ["properties"],
        });

        if (!features.length) {
          if (hoveredStateIdRef.current !== null) {
            updateFeatureState(hoveredStateIdRef.current, "hover", false);
            hoveredStateIdRef.current = null;
            setHoveredFeatureId(null);
          }
          debouncedSetPopupInfo(null);
          return;
        }

        const feature = features[0];
        const featureIbiCode = feature.properties?.IBI_COD;

        if (!featureIbiCode || featureIbiCode === hoveredStateIdRef.current) return;

        if (hoveredStateIdRef.current !== null) {
          updateFeatureState(hoveredStateIdRef.current, "hover", false);
        }

        hoveredStateIdRef.current = featureIbiCode;
        updateFeatureState(featureIbiCode, "hover", true);
        setHoveredFeatureId(featureIbiCode);

        if (featureIbiCode !== currentPropertyIdRef.current) {
          currentPropertyIdRef.current = featureIbiCode;

          const propertyId = feature.properties?.id || feature.properties?.IBI_COD;

          const point = mapboxMap.project([e.lngLat.lng, e.lngLat.lat]);
          const popupPoint = mapboxMap.unproject([point.x, point.y - 150]);

          debouncedSetPopupInfo({
            lngLat: {
              lng: popupPoint.lng,
              lat: popupPoint.lat,
            },
            propertyDetails: null,
            isLoading: true,
            error: undefined,
            propertyId,
          });
        }

        mapboxMap.getCanvas().style.cursor = "pointer";
      },
      100,
      { leading: true, trailing: true },
    ),
    [mapRef, isTransitioning, updateFeatureState, debouncedSetPopupInfo],
  );

  const onClick = useCallback(
    async (e: MapLayerMouseEvent) => {
      const mapboxMap = (mapRef?.current as any).getMap();
      mapboxMap.getCanvas().style.cursor = "not-allowed";

      if (!e.features?.length || !mapRef?.current || isTransitioning) return;

      const map = (mapRef.current as any).getMap();
      const feature = e.features[0];

      const featureIbiCode = feature.properties?.IBI_COD;
      if (!featureIbiCode) {
        console.error("Feature has no IBI_COD property:", feature);
        return;
      }

      try {
        setIsLoadingProperty(true);

        if (selectedPropertyIdRef.current !== null) {
          updateFeatureState(selectedPropertyIdRef.current, "selected", false);

          const layersToRemove = [
            "selected-property-outline-glow-3",
            "selected-property-outline-glow-2",
            "selected-property-outline-glow-1",
            "selected-property-outline",
            "selected-property-fill",
            "mask",
          ];

          layersToRemove.forEach((layerId) => {
            if (map.getLayer(layerId)) {
              map.removeLayer(layerId);
            }
          });

          if (map.getSource("selected-feature")) {
            map.removeSource("selected-feature");
          }

          if (map.getSource("selected-outline")) {
            map.removeSource("selected-outline");
          }
        }

        selectedPropertyIdRef.current = featureIbiCode;
        setClickedFeatureId(featureIbiCode);

        const details = await fetchPropertyDetails(featureIbiCode);
        setSelectedPropertyDetails({
          ...details,
          propertyId: featureIbiCode,
        });

        updateFeatureState(featureIbiCode, "selected", true);

        const maskPolygon = {
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [-180, 90],
                [180, 90],
                [180, -90],
                [-180, -90],
                [-180, 90],
              ],
              ...(feature.geometry.type === "MultiPolygon"
                ? (feature.geometry as GeoJSON.MultiPolygon).coordinates[0].map((coords) => [...coords].reverse())
                : feature.geometry.type === "Polygon"
                  ? [[(feature.geometry as GeoJSON.Polygon).coordinates[0]].reverse()]
                  : []),
            ],
          },
          properties: {},
        };

        const featurePolygon = {
          type: "Feature",
          geometry: feature.geometry,
          properties: feature.properties,
        };

        map.addSource("selected-feature", {
          type: "geojson",
          data: maskPolygon,
        });

        map.addSource("selected-outline", {
          type: "geojson",
          data: featurePolygon,
        });

        map.addLayer({
          id: "mask",
          type: "fill",
          source: "selected-feature",
          paint: {
            "fill-color": "#000000",
            "fill-opacity": 0.45,
          },
        });

        map.addLayer({
          id: "selected-property-outline-glow-3",
          type: "line",
          source: "selected-outline",
          paint: {
            "line-color": "#13D4EC",
            "line-width": 8,
            "line-opacity": 0.3,
            "line-blur": 8,
          },
        });

        map.addLayer({
          id: "selected-property-outline-glow-2",
          type: "line",
          source: "selected-outline",
          paint: {
            "line-color": "#13D4EC",
            "line-width": 6,
            "line-opacity": 0.5,
            "line-blur": 4,
          },
        });

        map.addLayer({
          id: "selected-property-outline-glow-1",
          type: "line",
          source: "selected-outline",
          paint: {
            "line-color": "#13D4EC",
            "line-width": 4,
            "line-opacity": 0.7,
            "line-blur": 2,
          },
        });

        map.addLayer({
          id: "selected-property-outline",
          type: "line",
          source: "selected-outline",
          paint: {
            "line-color": "#768FFF",
            "line-width": 2,
            "line-opacity": 1,
          },
        });

        setTimeout(() => {
          map.triggerRepaint();
        }, 100);
      } catch (error) {
        console.error("Error handling property click:", error);
        if (selectedPropertyIdRef.current !== null) {
          updateFeatureState(selectedPropertyIdRef.current, "selected", false);
        }
        selectedPropertyIdRef.current = null;
        setClickedFeatureId(null);
        setSelectedPropertyDetails(null);
      } finally {
        setIsLoadingProperty(false);
      }
    },
    [
      fetchPropertyDetails,
      isTransitioning,
      mapRef,
      setSelectedPropertyDetails,
      updateFeatureState,
      setIsLoadingProperty,
    ],
  );

  const onMouseLeave = useCallback(() => {
    if (hoveredStateIdRef.current !== null) {
      updateFeatureState(hoveredStateIdRef.current, "hover", false);
    }
    hoveredStateIdRef.current = null;
    currentPropertyIdRef.current = null;
    setHoveredFeatureId(null);

    setPopupInfo(null);
  }, [updateFeatureState]);

  const updateSpatialIndex = useCallback((features: GeoJSON.Feature[]) => {
    spatialIndex.current.clear();

    features.forEach((feature) => {
      if (!feature.properties?.IBI_COD) return;

      let minX = 180,
        minY = 90,
        maxX = -180,
        maxY = -90;

      if (feature.geometry.type === "Polygon") {
        const coords = (feature.geometry as GeoJSON.Polygon).coordinates[0];
        coords.forEach(([x, y]) => {
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x);
          maxY = Math.max(maxY, y);
        });
      } else if (feature.geometry.type === "MultiPolygon") {
        const polys = (feature.geometry as GeoJSON.MultiPolygon).coordinates;
        polys.forEach((poly) => {
          poly[0].forEach(([x, y]) => {
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
          });
        });
      }

      const numericId = parseInt(feature.properties.IBI_COD.replace(/\D/g, ""), 10);
      if (!isNaN(numericId)) {
        spatialIndex.current.add(numericId, minX, minY, maxX, maxY);
      }
    });
  }, []);

  useEffect(() => {
    if (!mapRef?.current) return;

    const map = (mapRef.current as any).getMap();
    if (!map?.loaded()) return;

    const handleSourceData = () => {
      if (!map.getSource("properties-source")) return;

      const features = map.querySourceFeatures("properties-source", {
        sourceLayer: "properties",
      });

      if (features.length > 0) {
        updateSpatialIndex(features);
      }
    };

    map.on("sourcedata", handleSourceData);

    return () => {
      map.off("sourcedata", handleSourceData);
    };
  }, [mapRef, updateSpatialIndex]);

  const MAX_PROPERTY_CACHE_SIZE = 100;
  const MAX_FEATURE_STATE_CACHE_SIZE = 200;

  const limitCacheSize = useCallback((cache: Map<any, any>, maxSize: number) => {
    if (cache.size <= maxSize) return;

    const removeCount = Math.ceil(maxSize * 0.2);
    const keysToRemove = Array.from(cache.keys()).slice(0, removeCount);
    keysToRemove.forEach((key) => cache.delete(key));
  }, []);

  useEffect(() => {
    const cacheCheckInterval = setInterval(() => {
      limitCacheSize(propertyDetailsCache.current, MAX_PROPERTY_CACHE_SIZE);
      limitCacheSize(featureStateCache.current, MAX_FEATURE_STATE_CACHE_SIZE);
    }, 30000);

    return () => {
      clearInterval(cacheCheckInterval);
    };
  }, [limitCacheSize]);

  useEffect(() => {
    return () => {
      propertyDetailsCache.current.clear();
      featureStateCache.current.clear();
      debouncedSetPopupInfo.cancel();
      spatialIndex.current.clear();
    };
  }, [debouncedSetPopupInfo]);

  const ensureLayerOrder = useCallback(() => {
    if (!mapRef?.current) return;

    const map = (mapRef.current as any).getMap();
    if (!map.loaded()) return;

    try {
      const layerOrder = [
        "selected-property-outline-glow-3",
        "selected-property-outline-glow-2",
        "selected-property-outline-glow-1",
        "selected-property-outline",
      ];

      for (let i = 0; i < layerOrder.length; i++) {
        const layerId = layerOrder[i];
        if (map.getLayer(layerId)) {
          map.moveLayer(layerId);
        }
      }
    } catch (error) {
      console.error("Error ensuring layer order:", error);
    }
  }, [mapRef]);

  useEffect(() => {
    if (selectedPropertyDetails) {
      setTimeout(ensureLayerOrder, 100);
    }
  }, [selectedPropertyDetails, ensureLayerOrder]);

  return {
    popupInfo,
    setPopupInfo,
    onMouseMove,
    onMouseLeave,
    onClick,
    updatePopupPosition,
    clearSelection,
    updateStateSelection,
    selectedPropertyDetails,
    isLoadingProperty,
    propertyLayer,
    ensureLayerOrder,
  };
};
