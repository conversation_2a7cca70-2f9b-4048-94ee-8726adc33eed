import { MapContext } from "@/contexts/MapContext";
import {
  DARK_STYLE_URL,
  GEODATIN_STYLE_URL,
  LIGHT_STYLE_URL,
  NAVIGATION_DAY_STYLE_URL,
  NAVIGATION_NIGHT_STYLE_URL,
  OUTDOORS_STYLE_URL,
  STREETS_STYLE_URL,
} from "@/utils/constants";
import { useEffect, useState } from "react";
import { useContextSelector } from "use-context-selector";

export const useBaseMap = () => {
  const [mapStyle, setMapStyle] = useState<string>(GEODATIN_STYLE_URL);

  const currentBaseMap = useContextSelector(MapContext, (state) => state.currentBaseMap);

  useEffect(() => {
    switch (currentBaseMap) {
      case "dark":
        setMapStyle(DARK_STYLE_URL);
        break;
      case "streets":
        setMapStyle(STREETS_STYLE_URL);
        break;
      case "outdoors":
        setMapStyle(OUTDOORS_STYLE_URL);
        break;
      case "light":
        setMapStyle(LIGHT_STYLE_URL);
        break;
      case "navigation-day":
        setMapStyle(NAVIGATION_DAY_STYLE_URL);
        break;
      case "navigation-night":
        setMapStyle(NAVIGATION_NIGHT_STYLE_URL);
        break;
      default:
        setMapStyle(GEODATIN_STYLE_URL);
        break;
    }
  }, [currentBaseMap]);

  return {
    mapStyle,
  };
};
