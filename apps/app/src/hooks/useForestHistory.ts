import { useMapSelector } from "@/stores/map.store";
import { ForestHistoryData } from "@/types";

import usePrivyAuth from "./usePrivyAuth";

export const useForestHistory = () => {
  const { setForestHistory, setIsLoadingForestHistory, setForestHistoryError, setSelectedTerritory } = useMapSelector(
    (state) => ({
      setForestHistory: state.setForestHistory,
      setIsLoadingForestHistory: state.setIsLoadingForestHistory,
      setForestHistoryError: state.setForestHistoryError,
      setSelectedTerritory: state.setSelectedTerritory,
    }),
  );

  const { getJWT } = usePrivyAuth();

  const fetchForestHistory = async (id: string) => {
    if (!id) return;

    try {
      setIsLoadingForestHistory(true);
      setForestHistoryError(null);

      const jwt = await getJWT();
      const response = await fetch(`https://api.yby.energy/api/territories/v2/forest-history?id=${id}`, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch forest history");
      }

      const data: ForestHistoryData = await response.json();
      setForestHistory(data);
    } catch (err) {
      console.error("Error fetching forest history:", err);
      setForestHistoryError(err instanceof Error ? err.message : "Failed to fetch forest history");
      setForestHistory(null);
    } finally {
      setIsLoadingForestHistory(false);
    }
  };

  return {
    fetchForestHistory,
    setSelectedTerritory,
  };
};
