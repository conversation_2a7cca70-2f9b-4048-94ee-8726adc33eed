import { useMapSelector } from "@/stores/map.store";
import mapboxgl from "mapbox-gl";
import { useCallback, useRef, useState } from "react";
import type { RefObject } from "react";
import type { MapLayerMouseEvent } from "react-map-gl";

interface UseMapZoomProps {
  mapRef: RefObject<mapboxgl.Map | null> | null;
}

export const useMapZoom = ({ mapRef }: UseMapZoomProps) => {
  const [isPolygonSelected, setIsPolygonSelected] = useState<boolean>(false);
  const [selectedPolygonId, setSelectedPolygonId] = useState<number | null>(null);
  const lastNavigationTime = useRef<number>(0);
  const currentPropertyId = useRef<number | null>(null);

  const setIsTransitioning = useMapSelector.use.setIsTransitioning();
  const isTransitioning = useMapSelector.use.isTransitioning();

  const resetMapView = useCallback(() => {
    if (!mapRef?.current) return;

    const now = Date.now();
    if (now - lastNavigationTime.current < 500) return;
    lastNavigationTime.current = now;

    const map = mapRef.current;

    // Clear property selection state
    if (currentPropertyId.current !== null) {
      map.setFeatureState(
        { source: "properties-source", sourceLayer: "properties", id: currentPropertyId.current },
        { selected: false },
      );
      currentPropertyId.current = null;
    }

    map.easeTo({
      pitch: 0,
      bearing: 0,
      duration: 500,
    });

    setIsPolygonSelected(false);
    setSelectedPolygonId(null);

    setTimeout(() => setIsTransitioning(false), 500);
  }, [mapRef, setIsTransitioning]);

  const handlePropertyClick = useCallback(
    (e: MapLayerMouseEvent) => {
      if (!e.features?.length || !mapRef?.current || isTransitioning) return;

      const map = mapRef.current;
      const feature = e.features[0];
      const featureId = feature.id as number;

      const now = Date.now();
      if (now - lastNavigationTime.current < 500) return;
      lastNavigationTime.current = now;

      // Handle property selection state
      if (currentPropertyId.current !== null) {
        map.setFeatureState(
          { source: "properties-source", sourceLayer: "properties", id: currentPropertyId.current },
          { selected: false },
        );
      }

      map.setFeatureState(
        { source: "properties-source", sourceLayer: "properties", id: featureId },
        { selected: true },
      );

      currentPropertyId.current = featureId;

      setIsTransitioning(true);
      const bounds = new mapboxgl.LngLatBounds();

      if (feature.geometry.type === "Polygon") {
        (feature.geometry.coordinates[0] as [number, number][]).forEach((coord) => {
          bounds.extend(coord as mapboxgl.LngLatLike);
        });
      } else if (feature.geometry.type === "MultiPolygon") {
        feature.geometry.coordinates.forEach((polygon) => {
          polygon[0].forEach((coord) => {
            bounds.extend(coord as mapboxgl.LngLatLike);
          });
        });
      }

      const currentZoom = map.getZoom();
      const defaultPropertyZoom = 8;

      const targetZoom = currentZoom > defaultPropertyZoom ? currentZoom : defaultPropertyZoom;

      map.easeTo({
        center: bounds.getCenter(),
        zoom: targetZoom,
        pitch: 45,
        bearing: 0,
        duration: 1000,
      });

      setTimeout(() => setIsTransitioning(false), 1000);
    },
    [isTransitioning, mapRef, setIsTransitioning],
  );

  const setupPropertyHandlers = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    map.on("click", "properties", handlePropertyClick);

    return () => {
      map.off("click", "properties", handlePropertyClick);
    };
  }, [mapRef, handlePropertyClick]);

  return {
    setupPropertyHandlers,
    resetMapView,
    isPolygonSelected,
    selectedPolygonId,
    isTransitioning,
  };
};
