import { MapContext } from "@/contexts/MapContext";
import { MapContextProps } from "@/types";
import { useContextSelector } from "use-context-selector";

/**
 * Hook to access MapContext.
 *
 * @return {Object} An object containing mapRef.
 */
export default function useMap(): MapContextProps {
  const mapRef = useContextSelector(MapContext, (auth) => auth.mapRef);
  const minimapRef = useContextSelector(MapContext, (auth) => auth.minimapRef);
  const currentLayers = useContextSelector(MapContext, (auth) => auth.currentLayers);
  const currentBaseMap = useContextSelector(MapContext, (auth) => auth.currentBaseMap);
  const mapLoaded = useContextSelector(MapContext, (auth) => auth.mapLoaded);
  const showProperties = useContextSelector(MapContext, (auth) => auth.showProperties);
  const showRestorations = useContextSelector(MapContext, (auth) => auth.showRestorations);

  const setShowProperties = useContextSelector(MapContext, (auth) => auth.setShowProperties);
  const setShowRestorations = useContextSelector(MapContext, (auth) => auth.setShowRestorations);
  const setCurrentLayers = useContextSelector(MapContext, (auth) => auth.setCurrentLayers);
  const setCurrentBaseMap = useContextSelector(MapContext, (auth) => auth.setCurrentBaseMap);
  const setMapLoaded = useContextSelector(MapContext, (auth) => auth.setMapLoaded);

  return {
    mapRef,
    minimapRef,
    currentLayers,
    currentBaseMap,
    mapLoaded,
    showProperties,
    showRestorations,
    setShowProperties,
    setShowRestorations,
    setCurrentLayers,
    setCurrentBaseMap,
    setMapLoaded,
  };
}
