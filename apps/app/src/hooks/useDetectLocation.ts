"use client";

import {
  DETECT_CITY_LOCAL_STORAGE_KEY,
  DETECT_COUNTRY_LOCAL_STORAGE_KEY,
  DETECT_LATITUDE_LOCAL_STORAGE_KEY,
  DETECT_LONGITUDE_LOCAL_STORAGE_KEY,
} from "@/utils/constants";
import { useEffect, useState } from "react";

export function useDetectLocation() {
  const [country, setCountry] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem(DETECT_COUNTRY_LOCAL_STORAGE_KEY) || "";
    }
    return "";
  });
  const [city, setCity] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem(DETECT_CITY_LOCAL_STORAGE_KEY) || "";
    }
    return "";
  });
  const [latitude, setLatitude] = useState(() => {
    if (typeof window !== "undefined") {
      const lat = localStorage.getItem(DETECT_LATITUDE_LOCAL_STORAGE_KEY);
      const parsedLat = lat ? parseFloat(lat) : 0;
      return parsedLat;
    }
    return 0;
  });
  const [longitude, setLongitude] = useState(() => {
    if (typeof window !== "undefined") {
      const lng = localStorage.getItem(DETECT_LONGITUDE_LOCAL_STORAGE_KEY);
      const parsedLng = lng ? parseFloat(lng) : 0;
      return parsedLng;
    }
    return 0;
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function detectLocation() {
      try {
        const cachedLatitude = localStorage.getItem(DETECT_LATITUDE_LOCAL_STORAGE_KEY);
        const cachedLongitude = localStorage.getItem(DETECT_LONGITUDE_LOCAL_STORAGE_KEY);

        if (
          cachedLatitude &&
          cachedLongitude &&
          parseFloat(cachedLatitude) !== 0 &&
          parseFloat(cachedLongitude) !== 0
        ) {
          setLatitude(parseFloat(cachedLatitude));
          setLongitude(parseFloat(cachedLongitude));
          setLoading(false);
          return;
        }

        const response = await fetch("https://ipapi.co/json/");
        const data = await response.json();

        localStorage.setItem(DETECT_COUNTRY_LOCAL_STORAGE_KEY, data.country_name);
        localStorage.setItem(DETECT_CITY_LOCAL_STORAGE_KEY, data.city);
        localStorage.setItem(DETECT_LATITUDE_LOCAL_STORAGE_KEY, String(data.latitude));
        localStorage.setItem(DETECT_LONGITUDE_LOCAL_STORAGE_KEY, String(data.longitude));

        setCountry(data.country_name);
        setCity(data.city);
        setLatitude(data.latitude);
        setLongitude(data.longitude);
      } catch (error) {
        console.error("Error detecting location:", error);
        setError(error as any);
      } finally {
        setLoading(false);
      }
    }

    detectLocation();
  }, []);

  return { country, city, latitude, longitude, loading, error };
}
