import { useBaseMap } from "@/hooks/useBaseMap";
import { useLayerControl } from "@/hooks/useLayerControl";
import useMap from "@/hooks/useMap";
import { territoriesService } from "@/services/territories.service";
import { handleStorage } from "@/utils/storage";
import { useCallback } from "react";

export const useIbiMap = () => {
  const { mapRef, handleMapLoad } = useLayerControl();
  const { currentLayers } = useMap();

  const simulateClickOnLocation = (map: mapboxgl.Map, coordinates: { lng: number; lat: number }) => {
    const point = map.project(coordinates);
    const event = new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
      clientX: point.x,
      clientY: point.y,
    });
    map.getCanvas().dispatchEvent(event);

    const features = map.queryRenderedFeatures(point, { layers: ["properties"] });
    return features.length > 0;
  };

  const checkLayersLoaded = (map: mapboxgl.Map): Promise<boolean> => {
    return new Promise((resolve) => {
      const checkLayers = () => {
        const propertiesSource = map.getSource("properties-source");
        if (!propertiesSource) {
          setTimeout(checkLayers, 100);
          return;
        }

        const propertiesLayer = map.getLayer("properties");
        if (!propertiesLayer) {
          setTimeout(checkLayers, 100);
          return;
        }

        if ((propertiesSource as mapboxgl.GeoJSONSource).loaded()) {
          resolve(true);
        } else {
          setTimeout(checkLayers, 100);
        }
      };

      checkLayers();
    });
  };

  const handleShareNavigation = useCallback(async () => {
    const ibicode = handleStorage<string>("session", "ibiCode", "get");
    if (!ibicode || !mapRef?.current) return;

    try {
      const map = (mapRef?.current as any).getMap();

      map.dragPan.disable();
      map.scrollZoom.disable();
      map.doubleClickZoom.disable();
      map.touchZoomRotate.disable();
      map.getCanvas().style.cursor = "not-allowed";

      const customCoordinates = await territoriesService.getCoordinates(ibicode);

      map.flyTo({
        center: [customCoordinates.data.coordinates[0], customCoordinates.data.coordinates[1]],
        zoom: 12,
        duration: 600,
      });

      await checkLayersLoaded(map);
      await new Promise((resolve) => setTimeout(resolve, 2000));
      simulateClickOnLocation(map, {
        lng: customCoordinates.data.coordinates[0],
        lat: customCoordinates.data.coordinates[1],
      });

      await new Promise((resolve) => setTimeout(resolve, 2000));
      simulateClickOnLocation(map, {
        lng: customCoordinates.data.coordinates[0],
        lat: customCoordinates.data.coordinates[1],
      });

      map.dragPan.enable();
      map.scrollZoom.enable();
      map.doubleClickZoom.enable();
      map.touchZoomRotate.enable();
      map.getCanvas().style.cursor = "";

      handleStorage("session", "ibiCode", "remove");
      handleStorage("session", "pathHistory", "remove");
    } catch (error) {
      console.error("Error navigating to shared location:", error);

      if (mapRef?.current) {
        const map = (mapRef?.current as any).getMap();
        map.dragPan.enable();
        map.scrollZoom.enable();
        map.doubleClickZoom.enable();
        map.touchZoomRotate.enable();
        map.getCanvas().style.cursor = "";
      }

      handleStorage("session", "ibiCode", "remove");
      handleStorage("session", "pathHistory", "remove");
    }
  }, [mapRef]);

  const { mapStyle } = useBaseMap();

  return {
    mapRef,
    handleMapLoad,
    handleShareNavigation,
    mapStyle,
    currentLayers,
  };
};
