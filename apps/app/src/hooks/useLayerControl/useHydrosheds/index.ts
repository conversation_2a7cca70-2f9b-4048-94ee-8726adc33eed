import { useMapSelector } from "@/stores/map.store";
import throttle from "lodash/throttle";
import { useCallback, useEffect, useRef } from "react";

import { hydroshedsEventHandler } from "./events";
import { useHydroshedsManagementStore } from "./store";

const HYDROSHEDS_MIN_ZOOM = 2;
const HYDROSHEDS_MAX_ZOOM = 12;

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

export function useHydrosheds(mapRef?: React.RefObject<mapboxgl.Map | null>, mapLoaded?: boolean) {
  const currentHoveredId = useRef<number | null>(null);
  const currentSelectedId = useRef<number | null>(null);
  const {
    setSelectedHydroshedLevel,
    setIsLoadingHydroshedLevel,
    currentHydroshedLevel,
    selectedHydroshedLevel,
    setSelectedHydroshedId,
    selectedHydroshedsMulti,
  } = useHydroshedsManagementStore();
  const triggerHydroshedsReload = useMapSelector.use.triggerHydroshedsReload();

  const onMouseMove = useCallback(
    throttle((e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map) return;

      hydroshedsEventHandler.events.onMouseMoveOnHydroshed(e, map, currentHoveredId);
    }, 16),
    [mapRef],
  );

  const clearSelection = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    try {
      hydroshedsEventHandler.events.clearAllHydroshedSelections(map, currentSelectedId);

      setSelectedHydroshedLevel({
        id: "hybas_12",
        label: "Hybas 12",
      });

      const hydroshedStore = useHydroshedsManagementStore.getState();
      hydroshedStore.setCurrentHydroshedLevel("Hybas 12 (default)");
      hydroshedStore.clearSelectedHydrosheds();
      hydroshedStore.setMultiSelectMode(false);
      hydroshedStore.setShowMultiSelectTooltip(false);
    } catch (error) {
      console.error("Error in clearSelection:", error);
    }
  }, [mapRef, setSelectedHydroshedLevel]);

  const forceReloadHydrosheds = useCallback(() => {
    const map = mapRef?.current;
    if (!map || !mapLoaded) return;

    const { selectedHydroshedsMulti, isMultiSelectMode, selectedHydroshedLevel } =
      useHydroshedsManagementStore.getState();

    if (!sourceExists(map, "hydrosheds-source")) {
      const level = currentHydroshedLevel.split(" ")[1];
      const sourceLayer = selectedHydroshedLevel?.id;

      try {
        map.addSource("hydrosheds-source", {
          type: "vector",
          tiles: [`https://tiles.yby.energy/dev/hybas-${level}/{z}/{x}/{y}`],
          minzoom: HYDROSHEDS_MIN_ZOOM,
          maxzoom: HYDROSHEDS_MAX_ZOOM,
          promoteId: "HYBAS_ID",
          "source-layer": sourceLayer,
        });
      } catch (error) {
        console.error("Error creating hydroshed source:", error);
      }
    }

    const sourceLayer = selectedHydroshedLevel?.id;

    if (!layerExists(map, "hydrosheds_glow")) {
      try {
        map.addLayer({
          id: "hydrosheds_glow",
          type: "line",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "line-color": "#03a9f4",
            "line-width": 1,
            "line-blur": 1,
          },
        });
      } catch (error) {
        console.error("Error adding glow layer:", error);
      }
    }

    if (!layerExists(map, "hydrosheds_border")) {
      try {
        map.addLayer({
          id: "hydrosheds_border",
          type: "line",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
            "line-opacity": 1,
          },
        });
      } catch (error) {
        console.error("Error adding border layer:", error);
      }
    }

    if (!layerExists(map, "hydrosheds_fill")) {
      try {
        map.addLayer({
          id: "hydrosheds_fill",
          type: "fill",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "fill-color": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              "transparent",
              ["boolean", ["feature-state", "hover"], false],
              "#768FFF",
              "#0288d1",
            ],
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0.4,
              ["boolean", ["feature-state", "hover"], false],
              0.6,
              0.4,
            ],
          },
        });
      } catch (error) {
        console.error("Error adding fill layer:", error);
      }
    }

    ["hydrosheds_fill", "hydrosheds_border", "hydrosheds_glow"].forEach((layerId) => {
      try {
        map.setLayoutProperty(layerId, "visibility", "visible");
      } catch (e) {
        console.error(`Error setting visibility for ${layerId}:`, e);
      }
    });

    if (sourceExists(map, "hydrosheds-source")) {
      setTimeout(() => {
        try {
          if (isMultiSelectMode && selectedHydroshedsMulti.length > 0) {
            selectedHydroshedsMulti.forEach((id) => {
              try {
                map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id }, { selected: true });
              } catch (error) {
                console.error("Error restoring multi-select state:", error);
              }
            });
          } else if (currentSelectedId.current !== null) {
            try {
              map.setFeatureState(
                { source: "hydrosheds-source", sourceLayer, id: currentSelectedId.current },
                { selected: true },
              );
            } catch (error) {
              console.error("Error restoring single select state:", error);
            }
          }
        } catch (error) {
          console.error("Error restoring selection states:", error);
        }

        if (map.getLayer("hydrosheds_fill")) {
          try {
            map.off("click", "hydrosheds_fill", onHydroshedClick);
            map.off("mousemove", "hydrosheds_fill", onMouseMove);
            map.off("mouseleave", "hydrosheds_fill", () => {
              hydroshedsEventHandler.events.onMouseLeaveHydroshed(map, currentHoveredId);
            });

            map.on("click", "hydrosheds_fill", (e) => {
              if (map && e) {
                hydroshedsEventHandler.events.onHydroshedClick(
                  e,
                  map,
                  currentSelectedId,
                  setIsLoadingHydroshedLevel,
                  setSelectedHydroshedLevel,
                  setSelectedHydroshedId,
                );
              }
            });
            map.on("mousemove", "hydrosheds_fill", onMouseMove);
            map.on("mouseleave", "hydrosheds_fill", () => {
              hydroshedsEventHandler.events.onMouseLeaveHydroshed(map, currentHoveredId);
            });
          } catch (error) {
            console.error("Error re-attaching event handlers:", error);
          }
        }
      }, 50);
    }
  }, [
    mapRef,
    mapLoaded,
    currentHydroshedLevel,
    onMouseMove,
    setIsLoadingHydroshedLevel,
    setSelectedHydroshedLevel,
    setSelectedHydroshedId,
  ]);

  const onHydroshedClick = useCallback(
    async (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map) return;

      hydroshedsEventHandler.events.onHydroshedClick(
        e,
        map,
        currentSelectedId,
        setIsLoadingHydroshedLevel,
        setSelectedHydroshedLevel,
        setSelectedHydroshedId,
        forceReloadHydrosheds,
      );
    },
    [mapRef, setIsLoadingHydroshedLevel, setSelectedHydroshedLevel, forceReloadHydrosheds],
  );

  const cleanupLayers = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    const { isMultiSelectMode, selectedHydroshedsMulti } = useHydroshedsManagementStore.getState();

    const shouldPreserveLayers =
      isMultiSelectMode || selectedHydroshedsMulti.length > 0 || currentSelectedId.current !== null;

    if (layerExists(map, "hydrosheds_fill")) {
      map.off("mousemove", "hydrosheds_fill", onMouseMove);
      map.off("mouseleave", "hydrosheds_fill", () => {
        hydroshedsEventHandler.events.onMouseLeaveHydroshed(map, currentHoveredId);
      });
      map.off("click", "hydrosheds_fill", onHydroshedClick);
    }

    const hydroshedStore = useHydroshedsManagementStore.getState();
    hydroshedStore.clearSelectedHydrosheds();
    hydroshedStore.setMultiSelectMode(false);
    hydroshedStore.setShowMultiSelectTooltip(false);

    if (!shouldPreserveLayers) {
      ["hydrosheds_fill", "hydrosheds_border", "hydrosheds_glow", "hydrosheds_mask"].forEach((layerId) => {
        if (layerExists(map, layerId)) {
          try {
            map.removeLayer(layerId);
          } catch (error) {
            console.error(`Error removing layer ${layerId}:`, error);
          }
        }
      });

      if (sourceExists(map, "hydrosheds-source")) {
        try {
          map.removeSource("hydrosheds-source");
        } catch (error) {
          console.error("Error removing hydrosheds-source:", error);
        }
      }
    }

    currentHoveredId.current = null;

    if (!shouldPreserveLayers) {
      currentSelectedId.current = null;
    } else {
      if (sourceExists(map, "hydrosheds-source")) {
        const layers = map.getStyle()?.layers;
        const sourceLayer =
          layers && (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);

        if (sourceLayer) {
          if (currentSelectedId.current !== null) {
            try {
              map.setFeatureState(
                { source: "hydrosheds-source", sourceLayer, id: currentSelectedId.current },
                { selected: false },
              );
              currentSelectedId.current = null;
            } catch (error) {
              console.error("Error clearing single selection state:", error);
            }
          }

          selectedHydroshedsMulti.forEach((id) => {
            try {
              map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id }, { selected: false });
            } catch (error) {
              console.error(`Error clearing multi-selection state for id ${id}:`, error);
            }
          });
        }
      }
    }
  }, [mapRef, onMouseMove, onHydroshedClick]);

  const setupHydroshedsLayer = useCallback(() => {
    const map = mapRef?.current;
    if (!map || !mapLoaded) return;

    const { selectedHydroshedsMulti, isMultiSelectMode } = useHydroshedsManagementStore.getState();
    const currentlySelectedId = currentSelectedId.current;

    const needsSetup =
      !sourceExists(map, "hydrosheds-source") ||
      !layerExists(map, "hydrosheds_fill") ||
      !layerExists(map, "hydrosheds_border") ||
      !layerExists(map, "hydrosheds_glow");

    if (needsSetup) {
      try {
        if (sourceExists(map, "hydrosheds-source")) {
          try {
            ["hydrosheds_fill", "hydrosheds_border", "hydrosheds_glow", "hydrosheds_mask"].forEach((layerId) => {
              if (layerExists(map, layerId)) {
                map.off("mousemove", layerId, onMouseMove);
                map.off("mouseleave", layerId, () => {});
                map.off("click", layerId, onHydroshedClick);
                map.removeLayer(layerId);
              }
            });
            map.removeSource("hydrosheds-source");
          } catch (error) {
            console.error("Error removing hydrosheds source:", error);
          }
        }

        const level = currentHydroshedLevel.split(" ")[1];
        const sourceLayer = selectedHydroshedLevel?.id;

        map.addSource("hydrosheds-source", {
          type: "vector",
          tiles: [`https://tiles.yby.energy/dev/hybas-${level}/{z}/{x}/{y}`],
          minzoom: HYDROSHEDS_MIN_ZOOM,
          maxzoom: HYDROSHEDS_MAX_ZOOM,
          promoteId: "HYBAS_ID",
          "source-layer": sourceLayer,
        });

        map.addLayer({
          id: "hydrosheds_glow",
          type: "line",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "line-color": "#03a9f4",
            "line-width": 1,
            "line-blur": 1,
          },
        });

        map.addLayer({
          id: "hydrosheds_border",
          type: "line",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
            "line-opacity": 1,
          },
        });

        map.addLayer({
          id: "hydrosheds_fill",
          type: "fill",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "fill-color": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              "transparent",
              ["boolean", ["feature-state", "hover"], false],
              "#768FFF",
              "#0288d1",
            ],
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0.4,
              ["boolean", ["feature-state", "hover"], false],
              0.6,
              0.4,
            ],
          },
        });

        map.on("click", "hydrosheds_fill", onHydroshedClick);
        map.on("mousemove", "hydrosheds_fill", onMouseMove);
        map.on("mouseleave", "hydrosheds_fill", () => {
          hydroshedsEventHandler.events.onMouseLeaveHydroshed(map, currentHoveredId);
        });

        setTimeout(() => {
          if (isMultiSelectMode && selectedHydroshedsMulti.length > 0) {
            selectedHydroshedsMulti.forEach((id) => {
              try {
                map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id }, { selected: true });
              } catch (error) {
                console.error("Error restoring multi-select state:", error);
              }
            });
          } else if (currentlySelectedId !== null) {
            try {
              map.setFeatureState(
                { source: "hydrosheds-source", sourceLayer, id: currentlySelectedId },
                { selected: true },
              );
              currentSelectedId.current = currentlySelectedId;
            } catch (error) {
              console.error("Error restoring single select state:", error);
            }
          }
        }, 100);
      } catch (error) {
        console.error("Error setting up hydrosheds layers:", error);
      }
    } else {
      try {
        ["hydrosheds_fill", "hydrosheds_border", "hydrosheds_glow"].forEach((layerId) => {
          try {
            map.setLayoutProperty(layerId, "visibility", "visible");
          } catch (e) {
            console.error(`Error setting visibility for ${layerId}:`, e);
          }
        });
      } catch (error) {
        console.error("Error ensuring layer visibility:", error);
      }
    }
  }, [mapRef, mapLoaded, onMouseMove, onHydroshedClick, cleanupLayers, currentHydroshedLevel]);

  useEffect(() => {
    if (mapLoaded) {
      setupHydroshedsLayer();
    }
  }, [mapLoaded, setupHydroshedsLayer]);

  useEffect(() => {
    if (!mapRef?.current || !mapLoaded) return;

    setTimeout(() => {
      triggerHydroshedsReload();
    }, 500);
  }, [mapRef, mapLoaded, triggerHydroshedsReload]);

  useEffect(() => {
    if (!mapRef?.current || !mapLoaded) return;

    let isMounted = true;

    const timerId = setTimeout(() => {
      if (isMounted) {
        forceReloadHydrosheds();
      }
    }, 300);

    return () => {
      isMounted = false;
      clearTimeout(timerId);
    };
  }, [mapRef, mapLoaded, forceReloadHydrosheds]);

  useEffect(() => {
    return () => {
      const map = mapRef?.current;
      if (map) {
        hydroshedsEventHandler.events.removeHydroshedLayers(map, currentHoveredId, currentSelectedId, onMouseMove);
      }

      const hydroshedStore = useHydroshedsManagementStore.getState();
      hydroshedStore.clearSelectedHydrosheds();
      hydroshedStore.setMultiSelectMode(false);
      hydroshedStore.setShowMultiSelectTooltip(false);

      clearSelection();
      cleanupLayers();
    };
  }, [mapRef, onMouseMove, clearSelection, cleanupLayers]);

  return {
    setupHydroshedsLayer,
    cleanupLayers,
    clearSelection,
    selectedHydroshedsMulti,
    forceReloadHydrosheds,
  };
}
