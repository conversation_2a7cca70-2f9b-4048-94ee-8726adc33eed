import { HydroshedLevel } from "@/types";
import { create } from "zustand";

interface HydroshedsManagementStoreState {
  selectedHydroshedLevel: HydroshedLevel;
  setSelectedHydroshedLevel: (hydroshedLevel: HydroshedLevel) => void;
  isLoadingHydroshedLevel: boolean;
  setIsLoadingHydroshedLevel: (isLoading: boolean) => void;
  currentHydroshedLevel: string;
  setCurrentHydroshedLevel: (level: string) => void;
  selectedHydroshedId: number | null;
  setSelectedHydroshedId: (id: number) => void;

  selectedHydroshedsMulti: number[];
  addSelectedHydroshed: (id: number) => void;
  removeSelectedHydroshed: (id: number) => void;
  clearSelectedHydrosheds: () => void;
  isMultiSelectMode: boolean;
  setMultiSelectMode: (isActive: boolean) => void;
  showMultiSelectTooltip: boolean;
  setShowMultiSelectTooltip: (show: boolean) => void;
}

export const useHydroshedsManagementStore = create<HydroshedsManagementStoreState>()((set) => ({
  selectedHydroshedLevel: {
    id: "hybas_12",
    label: "Hybas 12",
  },
  setSelectedHydroshedLevel: (hydroshedLevel) => set({ selectedHydroshedLevel: hydroshedLevel }),
  isLoadingHydroshedLevel: false,
  setIsLoadingHydroshedLevel: (isLoading) => set({ isLoadingHydroshedLevel: isLoading }),
  currentHydroshedLevel: "Hybas 12",
  setCurrentHydroshedLevel: (level) => set({ currentHydroshedLevel: level }),
  selectedHydroshedId: null,
  setSelectedHydroshedId: (id) => set({ selectedHydroshedId: id }),

  selectedHydroshedsMulti: [],
  addSelectedHydroshed: (id) =>
    set((state) => {
      if (!state.selectedHydroshedsMulti.includes(id)) {
        return { selectedHydroshedsMulti: [...state.selectedHydroshedsMulti, id] };
      }
      return state;
    }),
  removeSelectedHydroshed: (id) =>
    set((state) => ({
      selectedHydroshedsMulti: state.selectedHydroshedsMulti.filter((hydroId) => hydroId !== id),
    })),
  clearSelectedHydrosheds: () => set({ selectedHydroshedsMulti: [] }),
  isMultiSelectMode: false,
  setMultiSelectMode: (isActive) => set({ isMultiSelectMode: isActive }),
  showMultiSelectTooltip: false,
  setShowMultiSelectTooltip: (show) => set({ showMultiSelectTooltip: show }),
}));
