import useMapStore from "@/stores/map.store";
import { TOOLTIP_MESSAGES, createMultiSelectTooltip } from "@/utils/multiSelectTooltip";
import { FillLayer, LineLayer, MapMouseEvent } from "mapbox-gl";
import type { Map as MapboxMap } from "mapbox-gl";
import mapboxgl from "mapbox-gl";

import { useHydroshedsManagementStore } from "./store";

const ensureHydroshedLayersVisible = (map: MapboxMap, level: string) => {
  const sourceLayer = level;

  const hasLayers =
    map.getLayer("hydrosheds_fill") && map.getLayer("hydrosheds_border") && map.getLayer("hydrosheds_glow");

  if (!hasLayers && map.getSource("hydrosheds-source")) {
    try {
      if (!map.getLayer("hydrosheds_glow")) {
        map.addLayer({
          id: "hydrosheds_glow",
          type: "line",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "line-color": "#03a9f4",
            "line-width": 1,
            "line-blur": 1,
          },
        });
      }

      if (!map.getLayer("hydrosheds_border")) {
        map.addLayer({
          id: "hydrosheds_border",
          type: "line",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
            "line-opacity": 1,
          },
        });
      }

      if (!map.getLayer("hydrosheds_fill")) {
        map.addLayer({
          id: "hydrosheds_fill",
          type: "fill",
          source: "hydrosheds-source",
          "source-layer": sourceLayer,
          paint: {
            "fill-color": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              "transparent",
              ["boolean", ["feature-state", "hover"], false],
              "#768FFF",
              "#0288d1",
            ],
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0.4,
              ["boolean", ["feature-state", "hover"], false],
              0.6,
              0.4,
            ],
          },
        });
      }
    } catch (error) {
      console.error("Error ensuring hydroshed layers:", error);
    }
  }
};

const layerExists = (map: MapboxMap, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: MapboxMap, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: MapboxMap,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

export const hydroshedsEventHandler = {
  events: {
    onHydroshedClick: async (
      e: MapMouseEvent,
      map: MapboxMap,
      currentSelectedId: React.MutableRefObject<number | null>,
      setIsLoadingHydroshedLevel: (isLoading: boolean) => void,
      setSelectedHydroshedLevel: (details: any) => void,
      setSelectedHydroshedId: (id: number) => void,
      forceReloadHydrosheds?: () => void,
    ) => {
      if (!e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as number;

      let sourceLayer: string | undefined = undefined;

      if (e.features[0]["source-layer"]) {
        sourceLayer = e.features[0]["source-layer"] as string;
      } else {
        const layers = map.getStyle()?.layers;
        sourceLayer =
          layers && (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);
      }

      if (!sourceLayer) {
        console.error("Could not determine source layer for hydrosheds feature");
        return;
      }

      const isShortcutKeyPressed = e.originalEvent
        ? (e.originalEvent as MouseEvent).metaKey || (e.originalEvent as MouseEvent).ctrlKey
        : false;

      const hydroshedStore = useHydroshedsManagementStore.getState();
      const {
        addSelectedHydroshed,
        removeSelectedHydroshed,
        setMultiSelectMode,
        selectedHydroshedsMulti,
        clearSelectedHydrosheds,
        selectedHydroshedLevel,
        setShowMultiSelectTooltip,
      } = hydroshedStore;

      if (currentSelectedId.current === null) {
        createMultiSelectTooltip({
          id: "hydroshed-multi-tooltip",
          message: TOOLTIP_MESSAGES.HYDROSHEDS,
        });
        setShowMultiSelectTooltip(true);
      }

      ensureHydroshedLayersVisible(map, selectedHydroshedLevel.id);

      if (isShortcutKeyPressed) {
        setMultiSelectMode(true);

        if (selectedHydroshedsMulti.includes(featureId)) {
          removeSelectedHydroshed(featureId);
          map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id: featureId }, { selected: false });
        } else {
          addSelectedHydroshed(featureId);
          map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id: featureId }, { selected: true });
        }

        if (currentSelectedId.current !== null && selectedHydroshedsMulti.length === 0) {
          addSelectedHydroshed(currentSelectedId.current);
        }

        useMapStore.getState().setSelectedHydroshedId(featureId);

        setTimeout(() => {
          if (forceReloadHydrosheds) {
            forceReloadHydrosheds();
          } else {
            useMapStore.getState().triggerHydroshedsReload();
            setTimeout(() => ensureHydroshedLayersVisible(map, selectedHydroshedLevel.id), 100);
          }
        }, 50);

        return;
      } else {
        setMultiSelectMode(false);

        if (selectedHydroshedsMulti.length > 0) {
          selectedHydroshedsMulti.forEach((id) => {
            if (id !== featureId) {
              map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id }, { selected: false });
            }
          });
          clearSelectedHydrosheds();
        }

        if (currentSelectedId.current !== null && currentSelectedId.current !== featureId) {
          map.setFeatureState(
            { source: "hydrosheds-source", sourceLayer, id: currentSelectedId.current },
            { selected: false },
          );
        }

        map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id: featureId }, { selected: true });
        currentSelectedId.current = featureId;

        if (e.features[0].geometry.type === "Polygon" || e.features[0].geometry.type === "MultiPolygon") {
          const bounds = new mapboxgl.LngLatBounds();

          const coordinates =
            e.features[0].geometry.type === "Polygon"
              ? e.features[0].geometry.coordinates[0]
              : e.features[0].geometry.coordinates.flat(1);

          if (coordinates && coordinates.length) {
            coordinates.forEach((coord: any) => {
              if (Array.isArray(coord) && coord.length >= 2) {
                bounds.extend([coord[0], coord[1]]);
              }
            });
          }

          if (!bounds.isEmpty()) {
            const center = bounds.getCenter();
            map.flyTo({
              center: [center.lng, center.lat],
              zoom: map.getZoom(),
              animate: true,
              duration: 500,
              essential: true,
            });
          }
        } else if (e.lngLat) {
          map.flyTo({
            center: [e.lngLat.lng, e.lngLat.lat],
            zoom: map.getZoom(),
            essential: true,
            animate: true,
            duration: 500,
          });
        }

        try {
          setIsLoadingHydroshedLevel(true);
          setSelectedHydroshedLevel({
            id: featureId,
            ...e.features[0].properties,
          });
          setSelectedHydroshedId(featureId);

          useMapStore.getState().setSelectedHydroshedId(featureId);

          setTimeout(() => {
            if (forceReloadHydrosheds) {
              forceReloadHydrosheds();
            } else {
              useMapStore.getState().triggerHydroshedsReload();
              setTimeout(() => ensureHydroshedLayersVisible(map, selectedHydroshedLevel.id), 100);
            }
          }, 50);
        } catch (error) {
          console.error("Failed to process hydroshed details:", error);
        } finally {
          setIsLoadingHydroshedLevel(false);
        }
      }
    },
    onBaseMapClick: (e: MapMouseEvent, map: MapboxMap, clearSelection: () => void) => {
      const hydroshedsFeatures = safeQueryRenderedFeatures(map, e.point, {
        layers: ["hydrosheds_fill"],
      });

      if (hydroshedsFeatures.length === 0) {
        clearSelection();
      }
    },
    onMouseLeaveHydroshed: (map: MapboxMap, currentHoveredId: React.MutableRefObject<number | null>) => {
      if (currentHoveredId.current !== null) {
        const layers = map.getStyle()?.layers;
        const activeSourceLayer =
          layers && (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);

        if (activeSourceLayer) {
          map.setFeatureState(
            { source: "hydrosheds-source", sourceLayer: activeSourceLayer, id: currentHoveredId.current },
            { hover: false },
          );
        }
        currentHoveredId.current = null;
      }

      map.getCanvas().style.cursor = "";
    },
    onMouseMoveOnHydroshed: (
      e: MapMouseEvent,
      map: MapboxMap,
      currentHoveredId: React.MutableRefObject<number | null>,
    ) => {
      if (!e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as number;

      let sourceLayer: string | undefined = undefined;

      if (e.features[0]["source-layer"]) {
        sourceLayer = e.features[0]["source-layer"] as string;
      } else {
        const layers = map.getStyle()?.layers;
        sourceLayer =
          layers && (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);
      }

      if (!sourceLayer) {
        console.error("Could not determine source layer for hydrosheds feature");
        return;
      }

      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "hydrosheds-source", sourceLayer, id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    },
    clearHydroshedSelection: (map: MapboxMap, currentSelectedId: React.MutableRefObject<number | null>) => {
      try {
        if (currentSelectedId.current !== null) {
          const layers = map.getStyle()?.layers;
          const sourceLayer =
            layers &&
            (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);

          if (sourceLayer) {
            try {
              map.setFeatureState(
                { source: "hydrosheds-source", sourceLayer, id: currentSelectedId.current },
                { selected: false },
              );
            } catch (error) {
              console.error("Error clearing feature state in clearHydroshedSelection:", error);
            }
          }
        }

        currentSelectedId.current = null;

        map.triggerRepaint();
      } catch (error) {
        console.error("Error in clearHydroshedSelection:", error);
      }
    },
    clearAllHydroshedSelections: (map: MapboxMap, currentSelectedId: React.MutableRefObject<number | null>) => {
      try {
        if (currentSelectedId.current !== null) {
          const layers = map.getStyle()?.layers;
          const sourceLayer =
            layers &&
            (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);

          if (sourceLayer) {
            try {
              map.setFeatureState(
                { source: "hydrosheds-source", sourceLayer, id: currentSelectedId.current },
                { selected: false },
              );
            } catch (error) {
              console.error("Error clearing feature state for current selection:", error);
            }
          }
        }

        const { selectedHydroshedsMulti, clearSelectedHydrosheds } = useHydroshedsManagementStore.getState();

        if (selectedHydroshedsMulti.length > 0) {
          const layers = map.getStyle()?.layers;
          const sourceLayer =
            layers &&
            (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);

          if (sourceLayer) {
            selectedHydroshedsMulti.forEach((id) => {
              try {
                map.setFeatureState({ source: "hydrosheds-source", sourceLayer, id }, { selected: false });
              } catch (error) {
                console.error(`Error clearing feature state for id ${id}:`, error);
              }
            });
          }

          clearSelectedHydrosheds();
        }

        currentSelectedId.current = null;
        useMapStore.getState().setSelectedHydroshedId(null);

        map.triggerRepaint();
      } catch (error) {
        console.error("Error in clearAllHydroshedSelections:", error);
      }
    },
    removeHydroshedLayers: (
      map: MapboxMap,
      currentHoveredId: React.MutableRefObject<number | null>,
      currentSelectedId: React.MutableRefObject<number | null>,
      onMouseMove: (e: MapMouseEvent) => void,
    ) => {
      try {
        const { selectedHydroshedsMulti, clearSelectedHydrosheds, setMultiSelectMode, setShowMultiSelectTooltip } =
          useHydroshedsManagementStore.getState();

        const layers = map.getStyle()?.layers;
        const sourceLayer =
          layers && (layers.find((l) => l.id === "hydrosheds_fill" && "source-layer" in l)?.["source-layer"] as string);

        if (sourceLayer && sourceExists(map, "hydrosheds-source")) {
          if (currentSelectedId.current !== null) {
            try {
              map.setFeatureState(
                { source: "hydrosheds-source", sourceLayer, id: currentSelectedId.current },
                { selected: false, hover: false },
              );
            } catch (error) {
              console.error("Error clearing current selection state:", error);
            }
          }

          if (selectedHydroshedsMulti.length > 0) {
            selectedHydroshedsMulti.forEach((id) => {
              try {
                map.setFeatureState(
                  { source: "hydrosheds-source", sourceLayer, id },
                  { selected: false, hover: false },
                );
              } catch (error) {
                console.error(`Error clearing feature state for id ${id}:`, error);
              }
            });
          }
        }

        if (layerExists(map, "hydrosheds_fill")) {
          map.off("mousemove", "hydrosheds_fill", onMouseMove);
          map.off("mouseleave", "hydrosheds_fill", () => {});
          map.off("click", "hydrosheds_fill", () => {});
        }

        ["hydrosheds_fill", "hydrosheds_border", "hydrosheds_glow", "hydrosheds_mask"].forEach((layerId) => {
          if (layerExists(map, layerId)) {
            try {
              map.removeLayer(layerId);
            } catch (error) {
              console.error(`Error removing layer ${layerId}:`, error);
            }
          }
        });

        if (sourceExists(map, "hydrosheds-source")) {
          try {
            map.removeSource("hydrosheds-source");
          } catch (error) {
            console.error("Error removing hydrosheds-source:", error);
          }
        }

        currentHoveredId.current = null;
        currentSelectedId.current = null;

        clearSelectedHydrosheds();
        setMultiSelectMode(false);
        setShowMultiSelectTooltip(false);
        useMapStore.getState().setSelectedHydroshedId(null);

        map.triggerRepaint();
      } catch (error) {
        console.error("Error in removeHydroshedLayers:", error);
      }
    },
  },
  layers: {
    hydroshed_fill_layer: (level: string): FillLayer => {
      return {
        id: "hydrosheds_fill",
        type: "fill",
        source: "hydrosheds-source",
        "source-layer": level,
        paint: {
          "fill-color": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            "transparent",
            ["boolean", ["feature-state", "hover"], false],
            "#768FFF",
            "#0288d1",
          ],
          "fill-opacity": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            0.4,
            ["boolean", ["feature-state", "hover"], false],
            0.6,
            0.4,
          ],
        },
      };
    },
    hydroshed_border_layer: (level: string): LineLayer => {
      return {
        id: "hydrosheds_border",
        type: "line",
        source: "hydrosheds-source",
        "source-layer": level,
        paint: {
          "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
          "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
          "line-opacity": 1,
        },
      };
    },
    hydroshed_glow_layer: (level: string): LineLayer => {
      return {
        id: "hydrosheds_glow",
        type: "line",
        source: "hydrosheds-source",
        "source-layer": level,
        paint: {
          "line-color": "#03a9f4",
          "line-width": 1,
          "line-blur": 1,
        },
      };
    },
  },
};
