import { create } from "zustand";

interface PumpDetails {
  id: string;
  properties?: any;
}

interface PumpsManagementState {
  selectedPumpId: string | null;
  setSelectedPumpId: (id: string | null) => void;

  selectedPumpDetails: PumpDetails | null;
  setSelectedPumpDetails: (details: PumpDetails | null) => void;

  isLoadingPump: boolean;
  setIsLoadingPump: (isLoading: boolean) => void;

  resetPumpData: () => void;

  selectedPumpsMulti: string[];
  addSelectedPump: (id: string) => void;
  removeSelectedPump: (id: string) => void;
  clearSelectedPumps: () => void;

  isMultiSelectMode: boolean;
  setMultiSelectMode: (isActive: boolean) => void;
  showMultiSelectTooltip: boolean;
  setShowMultiSelectTooltip: (show: boolean) => void;
}

export const usePumpsManagementStore = create<PumpsManagementState>((set) => ({
  selectedPumpId: null,
  setSelectedPumpId: (id) => set({ selectedPumpId: id }),

  selectedPumpDetails: null,
  setSelectedPumpDetails: (details) => set({ selectedPumpDetails: details }),

  isLoadingPump: false,
  setIsLoadingPump: (isLoading) => set({ isLoadingPump: isLoading }),

  resetPumpData: () =>
    set({
      selectedPumpDetails: null,
      isLoadingPump: false,
      selectedPumpId: null,
      selectedPumpsMulti: [],
      isMultiSelectMode: false,
      showMultiSelectTooltip: false,
    }),

  selectedPumpsMulti: [],
  addSelectedPump: (id) =>
    set((state) => {
      if (!state.selectedPumpsMulti.includes(id)) {
        return { selectedPumpsMulti: [...state.selectedPumpsMulti, id] };
      }
      return state;
    }),
  removeSelectedPump: (id) =>
    set((state) => ({
      selectedPumpsMulti: state.selectedPumpsMulti.filter((pumpId) => pumpId !== id),
    })),
  clearSelectedPumps: () => set({ selectedPumpsMulti: [] }),

  isMultiSelectMode: false,
  setMultiSelectMode: (isActive) => set({ isMultiSelectMode: isActive }),
  showMultiSelectTooltip: false,
  setShowMultiSelectTooltip: (show) => set({ showMultiSelectTooltip: show }),
}));
