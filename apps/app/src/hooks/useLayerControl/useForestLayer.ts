import { useMapData } from "@/queries/hooks/useTerritories";
import { Map } from "mapbox-gl";
import { useEffect, useRef } from "react";

const getMapInstance = (map: Map | null): mapboxgl.Map | null => {
  if (!map) return null;

  try {
    return (map as any).getMap();
  } catch (error) {
    return map;
  }
};

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

interface UseForestLayerProps {
  map: Map | null;
  enabled: boolean;
}

export const useForestLayer = ({ map, enabled }: UseForestLayerProps) => {
  const { forestData } = useMapData({
    enabled: {
      forest: enabled,
      alerts: false,
    },
  });

  const previousUrl = useRef<string | undefined>();
  const forestUrl = forestData?.data?.data?.url;

  useEffect(() => {
    if (!map) return;

    const mapInstance = getMapInstance(map);
    if (!mapInstance) return;

    const hasForestSource = sourceExists(mapInstance, "forest-source");

    if (previousUrl.current === forestUrl && hasForestSource) {
      return;
    }

    previousUrl.current = forestUrl;

    if (enabled && forestUrl) {
      if (!hasForestSource) {
        try {
          mapInstance.addSource("forest-source", {
            type: "raster",
            tiles: [forestUrl],
            tileSize: 256,
          });
        } catch (error) {
          console.error("Error adding forest source:", error);
          return;
        }
      } else {
        try {
          (mapInstance.getSource("forest-source") as mapboxgl.RasterTileSource).setTiles([forestUrl]);
        } catch (error) {
          console.error("Error setting forest tiles:", error);
          return;
        }
      }

      const hasForestLayer = layerExists(mapInstance, "forest-layer");

      if (!hasForestLayer) {
        try {
          mapInstance.addLayer({
            id: "forest-layer",
            type: "raster",
            source: "forest-source",
            paint: {
              "raster-opacity": 0.7,
            },
          });
        } catch (error) {
          console.error("Error adding forest layer:", error);
        }
      }
    } else {
      if (layerExists(mapInstance, "forest-layer")) {
        try {
          mapInstance.removeLayer("forest-layer");
        } catch (error) {
          console.error("Error removing forest layer:", error);
        }
      }

      if (sourceExists(mapInstance, "forest-source")) {
        try {
          mapInstance.removeSource("forest-source");
        } catch (error) {
          console.error("Error removing forest source:", error);
        }
      }
    }

    return () => {
      if (map) {
        const mapInstance = getMapInstance(map);
        if (!mapInstance) return;

        if (layerExists(mapInstance, "forest-layer")) {
          try {
            mapInstance.removeLayer("forest-layer");
          } catch (error) {
            console.error("Error removing forest layer in cleanup:", error);
          }
        }

        if (sourceExists(mapInstance, "forest-source")) {
          try {
            mapInstance.removeSource("forest-source");
          } catch (error) {
            console.error("Error removing forest source in cleanup:", error);
          }
        }
      }
    };
  }, [map, enabled, forestUrl]);

  return {
    isLoading: forestData.isLoading,
    error: forestData.error,
  };
};
