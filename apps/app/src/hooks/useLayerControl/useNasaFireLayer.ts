import { NASA_FIRMS_API_KEY } from "@/utils/constants";
import { Map } from "mapbox-gl";
import { useCallback, useEffect, useState } from "react";

const getMapInstance = (map: Map | null): mapboxgl.Map | null => {
  if (!map) return null;

  try {
    return (map as any).getMap();
  } catch (error) {
    return map;
  }
};

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

interface UseNasaFireLayerProps {
  map: Map | null;
  enabled: boolean;
}

interface FireDataRow {
  latitude: number;
  longitude: number;
  bright_ti4: number;
  confidence: string;
  daynight: string;
}

export const useNasaFireLayer = ({ map, enabled }: UseNasaFireLayerProps) => {
  const [fireData, setFireData] = useState<GeoJSON.FeatureCollection | null>(null);
  const sourceId = "nasa-firms-source";
  const layerId = "nasa-firms-layer";

  const parseCSVRow = (headers: string[], row: string): FireDataRow | null => {
    const values = row.split(",");
    if (values.length !== headers.length) return null;

    const rowData: any = {};
    headers.forEach((header, index) => {
      const value = values[index];
      if (["latitude", "longitude", "bright_ti4"].includes(header)) {
        rowData[header] = parseFloat(value);
      } else {
        rowData[header] = value;
      }
    });

    return rowData as FireDataRow;
  };

  const handleNasaFirmsData = useCallback(async (): Promise<GeoJSON.FeatureCollection | null> => {
    try {
      const response = await fetch(
        `https://firms.modaps.eosdis.nasa.gov/api/area/csv/${NASA_FIRMS_API_KEY}/VIIRS_SNPP_NRT/world/5/${new Date().toISOString().slice(0, 10)}`,
      );
      const text = await response.text();
      const lines = text.split("\n").filter((line) => line.trim());
      const headers = lines[0].split(",");

      const features = lines
        .slice(1)
        .map((line) => {
          const rowData = parseCSVRow(headers, line);
          if (!rowData) return null;

          const { latitude, longitude, bright_ti4, confidence, daynight } = rowData;

          if (!isNaN(latitude) && !isNaN(longitude)) {
            return {
              type: "Feature" as const,
              geometry: {
                type: "Point" as const,
                coordinates: [longitude, latitude],
              },
              properties: {
                brightness: bright_ti4,
                confidence,
                daynight,
              },
            };
          }
          return null;
        })
        .filter(Boolean);

      return {
        type: "FeatureCollection" as const,
        features: features as GeoJSON.Feature[],
      };
    } catch (error) {
      console.error("Error loading NASA FIRMS data:", error);
      return null;
    }
  }, []);

  useEffect(() => {
    if (!map) return;

    const mapInstance = getMapInstance(map);
    if (!mapInstance) return;

    const loadAndSetupLayer = async () => {
      if (enabled) {
        const data = await handleNasaFirmsData();
        setFireData(data);

        if (data) {
          const hasSource = sourceExists(mapInstance, sourceId);

          if (!hasSource) {
            try {
              mapInstance.addSource(sourceId, {
                type: "geojson",
                data: data,
              });
            } catch (error) {
              console.error("Error adding NASA fire source:", error);
              return;
            }
          } else {
            try {
              (mapInstance.getSource(sourceId) as mapboxgl.GeoJSONSource).setData(data);
            } catch (error) {
              console.error("Error updating NASA fire source data:", error);
              return;
            }
          }

          const hasLayer = layerExists(mapInstance, layerId);

          if (!hasLayer) {
            try {
              mapInstance.addLayer({
                id: layerId,
                type: "circle",
                source: sourceId,
                paint: {
                  "circle-radius": ["interpolate", ["linear"], ["get", "brightness"], 290, 3, 367, 8],
                  "circle-color": [
                    "match",
                    ["get", "daynight"],
                    "D",
                    "#FF4136", // Day fires - red
                    "N",
                    "#FFDC00", // Night fires - yellow
                    "#FF4136", // Default - red
                  ],
                  "circle-opacity": 0.8,
                  "circle-blur": 0.4,
                },
              });
            } catch (error) {
              console.error("Error adding NASA fire layer:", error);
            }
          }
        }
      } else {
        if (layerExists(mapInstance, layerId)) {
          try {
            mapInstance.removeLayer(layerId);
          } catch (error) {
            console.error("Error removing NASA fire layer:", error);
          }
        }

        if (sourceExists(mapInstance, sourceId)) {
          try {
            mapInstance.removeSource(sourceId);
          } catch (error) {
            console.error("Error removing NASA fire source:", error);
          }
        }

        setFireData(null);
      }
    };

    loadAndSetupLayer();

    return () => {
      if (map) {
        const mapInstance = getMapInstance(map);
        if (!mapInstance) return;

        if (layerExists(mapInstance, layerId)) {
          try {
            mapInstance.removeLayer(layerId);
          } catch (error) {
            console.error("Error removing NASA fire layer in cleanup:", error);
          }
        }

        if (sourceExists(mapInstance, sourceId)) {
          try {
            mapInstance.removeSource(sourceId);
          } catch (error) {
            console.error("Error removing NASA fire source in cleanup:", error);
          }
        }
      }
    };
  }, [map, enabled, handleNasaFirmsData]);

  return {
    fireData,
    isLoading: enabled && !fireData,
    error: null,
  };
};
