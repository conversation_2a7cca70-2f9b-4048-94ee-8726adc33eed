import throttle from "lodash/throttle";
import type { LayerSpecification } from "mapbox-gl";
import mapboxgl from "mapbox-gl";
import { useCallback, useEffect, useRef } from "react";

import { useAnimalsManagementStore } from "./store";

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: mapboxgl.Map,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

export function useAnimals(mapRef?: React.RefObject<mapboxgl.Map | null>, mapLoaded?: boolean) {
  const currentHoveredId = useRef<string | null>(null);
  const currentSelectedId = useRef<string | null>(null);
  const {
    setSelectedAnimalId,
    clearSelectedAnimals,
    setMultiSelectMode,
    setIsLoadingAnimal,
    setSelectedAnimalDetails,
    resetAnimalData,
    selectedSpeciesFilter,
    filterBy,
  } = useAnimalsManagementStore();

  const ANIMALS_MIN_ZOOM = 0;
  const ANIMALS_MAX_ZOOM = 22;

  const onMouseMove = useCallback(
    throttle((e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map) return;

      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as string;

      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "animal-tiles", sourceLayer: "ibi_animals", id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "animal-tiles", sourceLayer: "ibi_animals", id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    }, 16),
    [mapRef],
  );

  const clearSelection = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (currentSelectedId.current !== null) {
      map.setFeatureState(
        { source: "animal-tiles", sourceLayer: "ibi_animals", id: currentSelectedId.current },
        { selected: false },
      );
      currentSelectedId.current = null;
    }

    const { selectedAnimalsMulti } = useAnimalsManagementStore.getState();
    selectedAnimalsMulti.forEach((id) => {
      map.setFeatureState({ source: "animal-tiles", sourceLayer: "ibi_animals", id }, { selected: false });
    });

    clearSelectedAnimals();
    setSelectedAnimalId(null);
    setMultiSelectMode(false);
    resetAnimalData();
  }, [mapRef, clearSelectedAnimals, setSelectedAnimalId, setMultiSelectMode, resetAnimalData]);

  const onAnimalClick = useCallback(
    async (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map || !e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as string;
      const isCtrlPressed = e.originalEvent ? (e.originalEvent as MouseEvent).ctrlKey : false;
      const animalsStore = useAnimalsManagementStore.getState();
      const {
        addSelectedAnimal,
        removeSelectedAnimal,
        setMultiSelectMode,
        selectedAnimalsMulti,
        clearSelectedAnimals,
        setSelectedAnimalId,
      } = animalsStore;

      if (isCtrlPressed) {
        setMultiSelectMode(true);
        if (selectedAnimalsMulti.includes(featureId)) {
          removeSelectedAnimal(featureId);
          map.setFeatureState(
            { source: "animal-tiles", sourceLayer: "ibi_animals", id: featureId },
            { selected: false },
          );
        } else {
          addSelectedAnimal(featureId);
          map.setFeatureState(
            { source: "animal-tiles", sourceLayer: "ibi_animals", id: featureId },
            { selected: true },
          );
        }
        setSelectedAnimalId(featureId);
        return;
      } else {
        setMultiSelectMode(false);
        if (selectedAnimalsMulti.length > 0) {
          selectedAnimalsMulti.forEach((id) => {
            if (id !== featureId) {
              map.setFeatureState({ source: "animal-tiles", sourceLayer: "ibi_animals", id }, { selected: false });
            }
          });
          clearSelectedAnimals();
        }
        if (currentSelectedId.current && currentSelectedId.current !== featureId) {
          map.setFeatureState(
            { source: "animal-tiles", sourceLayer: "ibi_animals", id: currentSelectedId.current },
            { selected: false },
          );
        }
        map.setFeatureState({ source: "animal-tiles", sourceLayer: "ibi_animals", id: featureId }, { selected: true });
        currentSelectedId.current = featureId;
        setSelectedAnimalId(featureId);

        if (e.features[0].geometry.type === "Polygon" || e.features[0].geometry.type === "MultiPolygon") {
          const bounds = new mapboxgl.LngLatBounds();

          const coordinates =
            e.features[0].geometry.type === "Polygon"
              ? e.features[0].geometry.coordinates[0]
              : e.features[0].geometry.coordinates.flat(1);

          if (coordinates && coordinates.length) {
            coordinates.forEach((coord: any) => {
              if (Array.isArray(coord) && coord.length >= 2) {
                bounds.extend([coord[0], coord[1]]);
              }
            });
          }

          if (!bounds.isEmpty()) {
            const center = bounds.getCenter();
            map.flyTo({
              center: [center.lng, center.lat],
              zoom: map.getZoom(),
              animate: true,
              duration: 500,
              essential: true,
            });
          }
        } else if (e.lngLat) {
          map.flyTo({
            center: [e.lngLat.lng, e.lngLat.lat],
            zoom: map.getZoom(),
            essential: true,
            animate: true,
            duration: 500,
          });
        }

        try {
          setIsLoadingAnimal(true);
          setSelectedAnimalDetails({
            id: featureId,
            properties: e.features[0].properties,
          });
        } catch (error) {
          console.error("Failed to fetch animal details:", error);
        } finally {
          setIsLoadingAnimal(false);
        }
      }
    },
    [mapRef, setIsLoadingAnimal, setSelectedAnimalDetails],
  );

  const cleanupLayers = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (layerExists(map, "animal-tiles-layer")) {
      map.off("mousemove", "animal-tiles-layer", onMouseMove);
      map.off("mouseleave", "animal-tiles-layer", () => {});
      map.off("click", "animal-tiles-layer", onAnimalClick);
    }

    ["animal-tiles-layer", "animal-tiles-border", "animal-tiles-glow"].forEach((layerId) => {
      if (layerExists(map, layerId)) {
        try {
          map.removeLayer(layerId);
        } catch (error) {
          console.error(`Error removing layer ${layerId}:`, error);
        }
      }
    });

    if (sourceExists(map, "animal-tiles")) {
      try {
        map.removeSource("animal-tiles");
      } catch (error) {
        console.error("Error removing animal-tiles source:", error);
      }
    }

    currentHoveredId.current = null;
    currentSelectedId.current = null;
  }, [mapRef, onMouseMove, onAnimalClick]);

  const setupAnimalsLayer = useCallback(() => {
    const map = mapRef?.current;
    if (!map || !mapLoaded) return;

    cleanupLayers();

    try {
      if (!sourceExists(map, "animal-tiles")) {
        map.addSource("animal-tiles", {
          type: "vector",
          tiles: ["https://tiles.yby.energy/dev/animals/{z}/{x}/{y}"],
          minzoom: ANIMALS_MIN_ZOOM,
          maxzoom: ANIMALS_MAX_ZOOM,
        });
      }

      const layersToAdd: LayerSpecification[] = [];

      const getFilteredColor = (): any => {
        if (!selectedSpeciesFilter) {
          return [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            "#606060",
            ["boolean", ["feature-state", "hover"], false],
            "#808080",
            "#505050",
          ];
        }

        return [
          "case",
          ["boolean", ["feature-state", "selected"], false],
          ["case", ["==", ["get", filterBy], selectedSpeciesFilter], "#FFD700", "#606060"],
          ["boolean", ["feature-state", "hover"], false],
          ["case", ["==", ["get", filterBy], selectedSpeciesFilter], "#FFE55C", "#808080"],
          ["case", ["==", ["get", filterBy], selectedSpeciesFilter], "#FFC107", "#505050"],
        ];
      };

      const getFilteredStrokeColor = (): any => {
        if (!selectedSpeciesFilter) {
          return ["case", ["boolean", ["feature-state", "selected"], false], "#707070", "#606060"];
        }

        return [
          "case",
          ["boolean", ["feature-state", "selected"], false],
          ["case", ["==", ["get", filterBy], selectedSpeciesFilter], "#FFB300", "#707070"],
          ["case", ["==", ["get", filterBy], selectedSpeciesFilter], "#FFC107", "#606060"],
        ];
      };

      if (!layerExists(map, "animal-tiles-layer")) {
        layersToAdd.push({
          id: "animal-tiles-layer",
          type: "fill",
          source: "animal-tiles",
          "source-layer": "ibi_animals",
          minzoom: ANIMALS_MIN_ZOOM,
          maxzoom: ANIMALS_MAX_ZOOM,
          paint: {
            "fill-color": getFilteredColor(),
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0.5,
              ["boolean", ["feature-state", "hover"], false],
              0.7,
              0.5,
            ],
          },
        });
      }

      if (!layerExists(map, "animal-tiles-border")) {
        layersToAdd.push({
          id: "animal-tiles-border",
          type: "line",
          source: "animal-tiles",
          "source-layer": "ibi_animals",
          minzoom: ANIMALS_MIN_ZOOM,
          maxzoom: ANIMALS_MAX_ZOOM,
          paint: {
            "line-color": getFilteredStrokeColor(),
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 2, 1],
            "line-opacity": 0.8,
          },
        });
      }

      layersToAdd.forEach((layer) => {
        if (layer && layer.id) {
          try {
            map.addLayer(layer);
          } catch (error) {
            console.error(`Error adding layer ${layer.id}:`, error);
          }
        }
      });

      map.on("mousemove", "animal-tiles-layer", onMouseMove);

      map.on("mouseleave", "animal-tiles-layer", () => {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "animal-tiles", sourceLayer: "ibi_animals", id: currentHoveredId.current },
            { hover: false },
          );
          currentHoveredId.current = null;
        }
        map.getCanvas().style.cursor = "";
      });

      map.on("click", "animal-tiles-layer", onAnimalClick);

      map.on("click", (e) => {
        const animalFeatures = safeQueryRenderedFeatures(map, e.point, {
          layers: ["animal-tiles-layer"],
        });

        if (animalFeatures.length === 0) {
          clearSelection();
        }
      });
    } catch (error) {
      console.error("Error setting up animals layer:", error);
    }
  }, [mapRef, mapLoaded, cleanupLayers, clearSelection, onMouseMove, onAnimalClick, selectedSpeciesFilter, filterBy]);

  useEffect(() => {
    if (mapLoaded) {
      setupAnimalsLayer();
    }
  }, [mapLoaded, setupAnimalsLayer]);

  useEffect(() => {
    return () => {
      resetAnimalData();
      cleanupLayers();
    };
  }, [resetAnimalData, cleanupLayers]);

  return {
    setupAnimalsLayer,
    cleanupLayers,
    onMouseMove,
    clearSelection,
  };
}

export { useAnimalsManagementStore } from "./store";
