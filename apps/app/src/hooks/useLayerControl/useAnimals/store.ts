import { create } from "zustand";

interface AnimalDetails {
  id: string;
  properties?: any;
}

interface AnimalsManagementState {
  selectedAnimalId: string | null;
  setSelectedAnimalId: (id: string | null) => void;

  selectedAnimalDetails: AnimalDetails | null;
  setSelectedAnimalDetails: (details: AnimalDetails | null) => void;

  isLoadingAnimal: boolean;
  setIsLoadingAnimal: (isLoading: boolean) => void;

  resetAnimalData: () => void;

  selectedAnimalsMulti: string[];
  addSelectedAnimal: (id: string) => void;
  removeSelectedAnimal: (id: string) => void;
  clearSelectedAnimals: () => void;

  isMultiSelectMode: boolean;
  setMultiSelectMode: (isActive: boolean) => void;
  showMultiSelectTooltip: boolean;
  setShowMultiSelectTooltip: (show: boolean) => void;

  selectedSpeciesFilter: string | null;
  setSelectedSpeciesFilter: (species: string | null) => void;
  filterBy: "class" | "family" | "genus" | "sci_name";
  setFilterBy: (filterBy: "class" | "family" | "genus" | "sci_name") => void;
}

export const useAnimalsManagementStore = create<AnimalsManagementState>((set) => ({
  selectedAnimalId: null,
  setSelectedAnimalId: (id) => set({ selectedAnimalId: id }),

  selectedAnimalDetails: null,
  setSelectedAnimalDetails: (details) => set({ selectedAnimalDetails: details }),

  isLoadingAnimal: false,
  setIsLoadingAnimal: (isLoading) => set({ isLoadingAnimal: isLoading }),

  resetAnimalData: () =>
    set({
      selectedAnimalDetails: null,
      isLoadingAnimal: false,
      selectedAnimalId: null,
      selectedAnimalsMulti: [],
      isMultiSelectMode: false,
      showMultiSelectTooltip: false,
      selectedSpeciesFilter: null,
    }),

  selectedAnimalsMulti: [],
  addSelectedAnimal: (id) =>
    set((state) => {
      if (!state.selectedAnimalsMulti.includes(id)) {
        return { selectedAnimalsMulti: [...state.selectedAnimalsMulti, id] };
      }
      return state;
    }),
  removeSelectedAnimal: (id) =>
    set((state) => ({
      selectedAnimalsMulti: state.selectedAnimalsMulti.filter((animalId) => animalId !== id),
    })),
  clearSelectedAnimals: () => set({ selectedAnimalsMulti: [] }),

  isMultiSelectMode: false,
  setMultiSelectMode: (isActive) => set({ isMultiSelectMode: isActive }),
  showMultiSelectTooltip: false,
  setShowMultiSelectTooltip: (show) => set({ showMultiSelectTooltip: show }),

  // Filter functionality
  selectedSpeciesFilter: null,
  setSelectedSpeciesFilter: (species) => set({ selectedSpeciesFilter: species }),
  filterBy: "sci_name",
  setFilterBy: (filterBy) => set({ filterBy }),
}));
