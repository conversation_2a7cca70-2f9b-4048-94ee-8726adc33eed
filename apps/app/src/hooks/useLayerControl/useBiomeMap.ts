import useMap from "@/hooks/useMap";
import { useBiomeSelector } from "@/stores/biome.store";
import { useMapSelector } from "@/stores/map.store";
import { BIOME_COLORS } from "@/utils/constants";
import * as turf from "@turf/turf";
import mapboxgl from "mapbox-gl";
import { useCallback, useEffect, useRef, useState } from "react";

const getMapInstance = (mapRef: React.RefObject<mapboxgl.Map | null> | null): mapboxgl.Map | null => {
  if (!mapRef?.current) return null;

  try {
    return (mapRef.current as any).getMap();
  } catch (error) {
    return mapRef.current;
  }
};

export interface BiomeLayer {
  id: string;
  name: string;
  visible: boolean;
  color: string;
}

export interface EcoregionLayer {
  id: string;
  name: string;
  visible: boolean;
  color: string;
  biomeId?: string;
}

export type DataType = "biomes" | "ecoregions" | "voronoi";

export const useBiomeMap = () => {
  const { mapRef } = useMap();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const setSelectedBiome = useBiomeSelector.use.setSelectedBiome();
  const setSelectedItem = useMapSelector.use.setSelectedItem();
  const selectedItem = useMapSelector.use.selectedItem();

  const [biomeLayers, setBiomeLayers] = useState<BiomeLayer[]>([]);
  const [ecoregionLayers, setEcoregionLayers] = useState<EcoregionLayer[]>([]);

  const hoveredBiomeRef = useRef<number | null>(null);
  const hoveredEcoregionRef = useRef<number | null>(null);
  const hoveredVoronoiRef = useRef<{ id: number; source: string; sourceLayer: string } | null>(null);

  const selectedBiomeRef = useRef<number | null>(null);
  const selectedEcoregionRef = useRef<number | null>(null);
  const selectedVoronoiRef = useRef<{ id: number; source: string; sourceLayer: string } | null>(null);

  const currentVoronoiEcoId = useRef<string | null>(null);
  const ecoregiaoComVoronoiAtivo = useRef<number | null>(null);

  const [activeLayers, setActiveLayers] = useState<{
    showBiomes: boolean;
    showEcoregions: boolean;
    showVoronoi: boolean;
  }>({
    showBiomes: true,
    showEcoregions: false,
    showVoronoi: false,
  });

  const setIsTransitioning = useMapSelector.use.setIsTransitioning();

  const fetchBiomes = useCallback(async (): Promise<GeoJSON.FeatureCollection | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("https://cdn.yby.energy/geojson/biomes.geojson");

      if (!response.ok) {
        throw new Error(`Error fetching biomes: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.features) {
        data.features = data.features.map((feature: GeoJSON.Feature, index: number) => {
          return {
            ...feature,
            id: feature.properties?.id || `biome-${index}`,
          };
        });
      }

      return data;
    } catch (error) {
      console.error("Error fetching biomes data:", error);
      setError("Failed to load biomes data");
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchEcoregions = useCallback(async (biomeId: string): Promise<GeoJSON.FeatureCollection | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`https://cdn.yby.energy/geojson/ecoregions/${biomeId}.geojson`);

      if (!response.ok) {
        throw new Error(`Error fetching ecoregions: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.type === "FeatureCollection" && Array.isArray(data.features)) {
        data.features = data.features.map((feature: GeoJSON.Feature, index: number) => {
          return {
            ...feature,
            id: feature.properties?.id || `ecoregion-${biomeId}-${index}`,
          };
        });

        return data;
      } else {
        console.error("Invalid data format:", data);
        throw new Error("Invalid data format for ecoregions");
      }
    } catch (error) {
      console.error(`Error fetching ecoregions for biome ${biomeId}:`, error);
      setError(`Unable to load ecoregions for the selected biome`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadBiomes = useCallback(async () => {
    try {
      const data = await fetchBiomes();
      if (!data) return null;

      const layers = data.features.map((feature, index) => ({
        id: feature.properties?.id || `biome-${index}`,
        name: feature.properties?.name || `Biome ${index}`,
        visible: true,
        color: BIOME_COLORS[index % BIOME_COLORS.length],
      }));

      setBiomeLayers(layers);
      return data;
    } catch (error) {
      console.error("Error loading biomes:", error);
      return null;
    }
  }, [fetchBiomes]);

  const loadEcoregions = useCallback(
    async (biomeId: string) => {
      try {
        const data = await fetchEcoregions(biomeId);
        if (!data) return null;

        const layers = data.features.map((feature, index) => ({
          id: feature.properties?.id || `ecoregion-${index}`,
          name: feature.properties?.name || `Ecoregion ${index}`,
          biomeId: biomeId,
          visible: true,
          color: BIOME_COLORS[(index + 3) % BIOME_COLORS.length],
        }));

        setEcoregionLayers(layers);
        return data;
      } catch (error) {
        console.error(`Error loading ecoregions for biome ${biomeId}:`, error);
        return null;
      }
    },
    [fetchEcoregions],
  );

  const loadVoronoiTiles = useCallback(
    async (ecoId: string) => {
      const map = getMapInstance(mapRef);
      if (!map) return;
      setIsLoading(true);

      try {
        if (currentVoronoiEcoId.current === ecoId) {
          setIsLoading(false);
          return true;
        }

        if (currentVoronoiEcoId.current !== null && currentVoronoiEcoId.current !== ecoId) {
          const previousSourceId = `voronoi-${currentVoronoiEcoId.current}-source`;
          const previousLayerId = `voronoi-${currentVoronoiEcoId.current}-layer`;

          const layersToRemove = [`${previousLayerId}-outline`, previousLayerId, `${previousLayerId}-highlight`];

          layersToRemove.forEach((layerId) => {
            if (map.getLayer(layerId)) {
              map.removeLayer(layerId);
            }
          });

          if (map.getSource(previousSourceId)) {
            try {
              map.removeSource(previousSourceId);
            } catch (e) {
              console.warn("Could not remove previous source, may still be in use:", e);
            }
          }

          if (selectedVoronoiRef.current !== null) {
            try {
              map.setFeatureState(
                {
                  source: selectedVoronoiRef.current.source,
                  sourceLayer: selectedVoronoiRef.current.sourceLayer,
                  id: selectedVoronoiRef.current.id,
                },
                { selected: false },
              );
              selectedVoronoiRef.current = null;
            } catch (e) {
              console.warn("Could not clear previous voronoi selection:", e);
            }
          }

          if (hoveredVoronoiRef.current !== null) {
            try {
              map.setFeatureState(
                {
                  source: hoveredVoronoiRef.current.source,
                  sourceLayer: hoveredVoronoiRef.current.sourceLayer,
                  id: hoveredVoronoiRef.current.id,
                },
                { hover: false },
              );
              hoveredVoronoiRef.current = null;
            } catch (e) {
              console.warn("Could not clear previous voronoi hover:", e);
            }
          }
        }

        currentVoronoiEcoId.current = ecoId;

        const sourceId = `voronoi-${ecoId}-source`;
        const layerId = `voronoi-${ecoId}-layer`;

        try {
          const mapExt = map as any;
          if (mapExt._voronoiClickHandler) {
            map.off("click", mapExt._voronoiClickHandler);
            mapExt._voronoiClickHandler = null;
          }

          const tileJsonResponse = await fetch(`https://tiles.yby.energy/dev/voronoi-${ecoId}`);
          if (tileJsonResponse.ok) {
            const tileJson = await tileJsonResponse.json();
            const sourceLayer = tileJson.vector_layers?.[0]?.id || `VORONOI_${ecoId}`;

            if (map.getLayer(`${layerId}-outline`)) {
              map.removeLayer(`${layerId}-outline`);
            }

            if (map.getLayer(layerId)) {
              map.removeLayer(layerId);
            }

            if (map.getSource(sourceId)) {
              try {
                map.removeSource(sourceId);
              } catch (e) {
                console.warn("Could not remove source, may still be in use:", e);
              }
            }

            map.addSource(sourceId, {
              type: "vector",
              tiles: [`https://tiles.yby.energy/dev/voronoi-${ecoId}/{z}/{x}/{y}`],
            });

            let beforeLayerId: string | undefined = undefined;
            const allLayers = map.getStyle()?.layers;
            if (allLayers && allLayers.length > 0) {
              const nonVoronoiLayers = allLayers.filter((layer: any) => !layer.id.startsWith("voronoi-"));
              if (nonVoronoiLayers.length > 0) {
                beforeLayerId = nonVoronoiLayers[nonVoronoiLayers.length - 1].id;
              }
            }

            map.addLayer(
              {
                id: `${layerId}-outline`,
                type: "line",
                source: sourceId,
                "source-layer": sourceLayer,
                paint: {
                  "line-color": "#FFFFFF",
                  "line-width": 0.5,
                  "line-opacity": 0.5,
                },
                layout: {
                  visibility: "visible",
                },
              },
              beforeLayerId,
            );

            map.addLayer(
              {
                id: layerId,
                type: "fill",
                source: sourceId,
                "source-layer": sourceLayer,
                paint: {
                  "fill-color": "#FFFFFF",
                  "fill-opacity": [
                    "case",
                    ["boolean", ["feature-state", "selected"], false],
                    0,
                    ["boolean", ["feature-state", "hover"], false],
                    0.1,
                    0,
                  ],
                  "fill-outline-color": "#FFFFFF",
                },
                layout: {
                  visibility: "visible",
                },
              },
              beforeLayerId,
            );

            const highlightLayerId = `${layerId}-highlight`;

            map.addLayer(
              {
                id: highlightLayerId,
                type: "line",
                source: sourceId,
                "source-layer": sourceLayer,
                paint: {
                  "line-color": "#FFFFFF",
                  "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 2.5, 0.5],
                  "line-opacity": ["case", ["boolean", ["feature-state", "selected"], false], 1, 0.5],
                  "line-dasharray": [2, 1],
                },
                layout: {
                  visibility: "visible",
                },
              },
              beforeLayerId,
            );

            const waitForLayerLoad = () => {
              if (!map.getLayer(layerId)) {
                setTimeout(waitForLayerLoad, 100);
                return;
              }

              const directClickHandler = (e: any) => {
                const featuresNoClick = map.queryRenderedFeatures(e.point, {
                  layers: [layerId],
                });

                if (featuresNoClick.length === 0) {
                  return;
                }

                e.preventDefault();
                if (e.stopPropagation) {
                  e.stopPropagation();
                }
                if (e.stopImmediatePropagation) {
                  e.stopImmediatePropagation();
                }

                if (e.originalEvent) {
                  e.originalEvent.preventDefault();
                  e.originalEvent.stopPropagation();
                  if (e.originalEvent.stopImmediatePropagation) {
                    e.originalEvent.stopImmediatePropagation();
                  }
                  e.originalEvent.cancelBubble = true;
                }

                try {
                  if (!map.getLayer(layerId)) {
                    return false;
                  }

                  const features = map.queryRenderedFeatures(e.point, {
                    layers: [layerId],
                  });

                  if (features.length > 0) {
                    const feature = features[0];

                    if (feature.source === sourceId && feature.sourceLayer === sourceLayer) {
                      handleVoronoiClick(e);
                    }
                  }
                  return false;
                } catch (error) {
                  console.error("Error in direct click handler:", error);
                  return false;
                }
              };

              const mapExt = map as any;
              if (mapExt._voronoiClickHandler) {
                map.off("click", mapExt._voronoiClickHandler);
              }

              map.off("click", directClickHandler);

              mapExt._voronoiClickHandler = directClickHandler;

              map.on("click", (e: mapboxgl.MapMouseEvent) => directClickHandler(e));

              map.on("mousemove", layerId, (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.GeoJSONFeature[] }) => {
                if (e.features && e.features.length > 0) {
                  map.getCanvas().style.cursor = "pointer";
                  const feature = e.features[0];

                  if (feature.id !== undefined) {
                    const hoveredFeatures = map.querySourceFeatures(sourceId, {
                      sourceLayer: sourceLayer,
                    });

                    hoveredFeatures.forEach((f: any) => {
                      if (f.id !== undefined && f.id !== feature.id) {
                        map.setFeatureState({ source: sourceId, sourceLayer: sourceLayer, id: f.id }, { hover: false });
                      }
                    });

                    map.setFeatureState(
                      { source: sourceId, sourceLayer: sourceLayer, id: feature.id },
                      { hover: true },
                    );
                  }
                }
              });

              map.on("mouseleave", layerId, () => {
                map.getCanvas().style.cursor = "";

                const hoveredFeatures = map.querySourceFeatures(sourceId, {
                  sourceLayer: sourceLayer,
                });

                hoveredFeatures.forEach((f: any) => {
                  if (f.id !== undefined) {
                    map.setFeatureState({ source: sourceId, sourceLayer: sourceLayer, id: f.id }, { hover: false });
                  }
                });
              });
            };

            waitForLayerLoad();

            setActiveLayers((prev) => ({
              ...prev,
              showVoronoi: true,
            }));

            map.triggerRepaint();

            return true;
          } else {
            throw new Error(`TileJSON not available for ecoId ${ecoId}`);
          }
        } catch (tileJsonError) {
          console.error("Error fetching TileJSON, trying to load directly:", tileJsonError);
        }

        return true;
      } catch (error) {
        console.error(`Error loading voronoi tiles for ecoregion ${ecoId}:`, error);
        setError(`Failed to load voronoi grid for ecoregion ${ecoId}`);
      } finally {
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [mapRef, setSelectedItem],
  );

  const initializeLayers = useCallback(async () => {
    const map = getMapInstance(mapRef);
    if (!map) return;
    setIsLoading(true);

    try {
      const biomesData = await loadBiomes();

      if (!map.getSource("biomes-source")) {
        map.addSource("biomes-source", {
          type: "geojson",
          data: biomesData || { type: "FeatureCollection", features: [] },
          generateId: true,
        });
      } else {
        (map.getSource("biomes-source") as mapboxgl.GeoJSONSource).setData(
          biomesData || { type: "FeatureCollection", features: [] },
        );
      }

      if (!map.getSource("ecoregions-source")) {
        map.addSource("ecoregions-source", {
          type: "geojson",
          data: { type: "FeatureCollection", features: [] },
          generateId: true,
        });
      }

      if (!map.getSource("biome-mask-source")) {
        map.addSource("biome-mask-source", {
          type: "geojson",
          data: {
            type: "Feature",
            geometry: {
              type: "Polygon",
              coordinates: [],
            },
            properties: {},
          },
        });
      }

      if (!map.getLayer("biomes-fill")) {
        map.addLayer({
          id: "biomes-fill",
          type: "fill",
          source: "biomes-source",
          paint: {
            "fill-color": "#FFFFFF",
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              0.1,
              ["boolean", ["feature-state", "selected"], false],
              0,
              0,
            ],
            "fill-opacity-transition": {
              duration: 200,
            },
          },
        });

        map.addLayer({
          id: "biomes-outline",
          type: "line",
          source: "biomes-source",
          paint: {
            "line-color": "#FFFFFF",
            "line-width": [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              1.5,
              ["boolean", ["feature-state", "selected"], false],
              3,
              0.5,
            ],
            "line-opacity": [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              0.8,
              ["boolean", ["feature-state", "selected"], false],
              1,
              0.8,
            ],
          },
        });
      }

      if (!map.getLayer("biome-mask")) {
        map.addLayer(
          {
            id: "biome-mask",
            type: "fill",
            source: "biome-mask-source",
            paint: {
              "fill-color": "#000000",
              "fill-opacity": 0.85,
            },
          },
          "biomes-fill",
        );
      }

      if (!map.getLayer("ecoregions-fill")) {
        map.addLayer({
          id: "ecoregions-fill",
          type: "fill",
          source: "ecoregions-source",
          paint: {
            "fill-color": "#FFFFFF",
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              0.1,
              ["boolean", ["feature-state", "selected"], false],
              0,
              0,
            ],
          },
        });

        map.addLayer({
          id: "ecoregions-outline",
          type: "line",
          source: "ecoregions-source",
          paint: {
            "line-color": "#FFFFFF",
            "line-width": [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              2.5,
              ["boolean", ["feature-state", "selected"], false],
              2.5,
              1,
            ],
            "line-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              1,
              ["boolean", ["feature-state", "hover"], false],
              0.7,
              0.5,
            ],
            "line-dasharray": [3, 2],
          },
        });
      }

      updateLayerColors();

      setupEventHandlers();
    } catch (error) {
      console.error("Error initializing layers:", error);
      setError("Failed to initialize map layers");
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadBiomes, mapRef]);

  const updateLayerColors = useCallback(() => {
    const map = getMapInstance(mapRef);
    if (!map) return;

    if (map.getLayer("biomes-fill")) {
      map.setPaintProperty("biomes-fill", "fill-color", "#FFFFFF");
      map.setPaintProperty("biomes-fill", "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.1,
        ["boolean", ["feature-state", "selected"], false],
        0,
        0,
      ]);
    }

    if (map.getLayer("ecoregions-fill")) {
      map.setPaintProperty("ecoregions-fill", "fill-color", "#FFFFFF");
      map.setPaintProperty("ecoregions-fill", "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.1,
        ["boolean", ["feature-state", "selected"], false],
        0,
        0,
      ]);
    }

    const allLayers = map.getStyle()?.layers || [];
    const voronoiLayers = allLayers
      .filter(
        (layer: any) =>
          layer.id &&
          layer.id.startsWith("voronoi-") &&
          layer.id.includes("-layer") &&
          !layer.id.includes("-outline") &&
          !layer.id.includes("-highlight"),
      )
      .map((layer: any) => layer.id);

    voronoiLayers.forEach((layerId: string) => {
      map.setPaintProperty(layerId, "fill-color", "#FFFFFF");
      map.setPaintProperty(layerId, "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.1,
        ["boolean", ["feature-state", "selected"], false],
        0,
        0,
      ]);
    });
  }, [mapRef]);

  useEffect(() => {
    updateLayerColors();
  }, [updateLayerColors, biomeLayers, ecoregionLayers]);

  const clearSelection = useCallback(() => {
    const map = getMapInstance(mapRef);
    if (!map) return;

    ecoregiaoComVoronoiAtivo.current = null;

    if (selectedBiomeRef.current !== null) {
      map.setFeatureState({ source: "biomes-source", id: selectedBiomeRef.current }, { selected: false });
      selectedBiomeRef.current = null;
    }

    if (selectedEcoregionRef.current !== null) {
      map.setFeatureState({ source: "ecoregions-source", id: selectedEcoregionRef.current }, { selected: false });
      selectedEcoregionRef.current = null;
    }

    if (selectedVoronoiRef.current !== null) {
      map.setFeatureState(
        {
          source: selectedVoronoiRef.current.source,
          sourceLayer: selectedVoronoiRef.current.sourceLayer || "",
          id: selectedVoronoiRef.current.id,
        },
        { selected: false },
      );
      selectedVoronoiRef.current = null;
    }

    if (map.getLayer("biome-mask")) {
      map.setLayoutProperty("biome-mask", "visibility", "none");
    }

    if (map.getLayer("ecoregions-fill") && map.getLayer("ecoregions-outline")) {
      map.setPaintProperty("ecoregions-fill", "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "selected"], false],
        0,
        ["boolean", ["feature-state", "hover"], false],
        0.1,
        0,
      ]);

      map.setPaintProperty("ecoregions-outline", "line-opacity", [
        "case",
        ["boolean", ["feature-state", "selected"], false],
        1,
        ["boolean", ["feature-state", "hover"], false],
        0.7,
        0.5,
      ]);

      map.setLayoutProperty("ecoregions-fill", "visibility", "none");
      map.setLayoutProperty("ecoregions-outline", "visibility", "none");
      setActiveLayers((prev) => ({ ...prev, showEcoregions: false }));
    }

    const clearVoronoiLayers = () => {
      const mapExt = map as any;
      if (mapExt._voronoiClickHandler) {
        map.off("click", mapExt._voronoiClickHandler);
        mapExt._voronoiClickHandler = null;
      }

      const allLayers = map.getStyle()?.layers || [];
      const voronoiLayers = allLayers
        .filter((layer: any) => layer.id && layer.id.startsWith("voronoi-"))
        .map((layer: any) => layer.id);

      voronoiLayers.forEach((layerId: string) => {
        try {
          // @ts-expect-error - Ignoring type errors for event removal
          map.off("click", layerId);
          // @ts-expect-error - Ignoring type errors for event removal
          map.off("mousemove", layerId);
          // @ts-expect-error - Ignoring type errors for event removal
          map.off("mouseleave", layerId);
        } catch (e) {
          console.warn(`Failed to remove event listeners for layer ${layerId}:`, e);
        }
      });

      const outlineLayers = voronoiLayers.filter((layerId: string) => layerId.endsWith("-outline"));
      const highlightLayers = voronoiLayers.filter((layerId: string) => layerId.endsWith("-highlight"));
      const fillLayers = voronoiLayers.filter(
        (layerId: string) =>
          layerId.endsWith("-layer") && !layerId.endsWith("-outline") && !layerId.endsWith("-highlight"),
      );

      outlineLayers.forEach((layerId: string) => {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
      });

      highlightLayers.forEach((layerId: string) => {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
      });

      fillLayers.forEach((layerId: string) => {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
      });

      const sources = fillLayers.map((layerId: string) => layerId.replace("-layer", "-source"));

      sources.forEach((sourceId: string) => {
        if (map.getSource(sourceId)) {
          try {
            map.removeSource(sourceId);
          } catch (error) {
            console.warn(`Failed to remove source ${sourceId}:`, error);
          }
        }
      });

      currentVoronoiEcoId.current = null;

      setActiveLayers((prev) => ({ ...prev, showVoronoi: false }));
    };

    clearVoronoiLayers();

    setSelectedItem({
      id: null,
      type: null,
      properties: null,
    });
  }, [mapRef, setSelectedItem]);

  const zoomToFeature = useCallback(
    (feature: mapboxgl.GeoJSONFeature) => {
      if (!feature.geometry) return;

      const map = getMapInstance(mapRef);
      if (!map) return;
      setIsTransitioning(true);

      try {
        if (feature.geometry.type === "MultiPolygon") {
          try {
            let largestPolygon: GeoJSON.Feature<GeoJSON.Polygon> | null = null;
            let maxArea = 0;

            for (const polygon of feature.geometry.coordinates) {
              if (!polygon || !Array.isArray(polygon)) continue;

              const singlePolygon: GeoJSON.Feature<GeoJSON.Polygon> = {
                type: "Feature",
                properties: {},
                geometry: {
                  type: "Polygon",
                  coordinates: polygon,
                },
              };

              try {
                const area = turf.area(singlePolygon as any);
                if (area > maxArea) {
                  maxArea = area;
                  largestPolygon = singlePolygon;
                }
              } catch (err) {
                console.log(err);
              }
            }

            if (largestPolygon) {
              const significantPolygons: GeoJSON.Feature<GeoJSON.Polygon>[] = [];
              significantPolygons.push(largestPolygon);

              const threshold = maxArea * 0.2;

              for (const polygon of feature.geometry.coordinates) {
                if (!polygon || !Array.isArray(polygon)) continue;

                const singlePolygon: GeoJSON.Feature<GeoJSON.Polygon> = {
                  type: "Feature",
                  properties: {},
                  geometry: {
                    type: "Polygon",
                    coordinates: polygon,
                  },
                };

                try {
                  const area = turf.area(singlePolygon as any);
                  if (area >= threshold && area < maxArea) {
                    significantPolygons.push(singlePolygon);
                  }
                } catch (err) {
                  console.log(err);
                }
              }

              if (significantPolygons.length > 1) {
                const collection = turf.featureCollection(significantPolygons as any[]);
                const bbox = turf.bbox(collection);

                const bboxWidth = bbox[2] - bbox[0];

                if (bboxWidth > 180) {
                  const largeBbox = turf.bbox(largestPolygon as any);
                  const bounds = new mapboxgl.LngLatBounds([largeBbox[0], largeBbox[1]], [largeBbox[2], largeBbox[3]]);

                  map.fitBounds(bounds, {
                    padding: { top: 50, bottom: 50, left: 50, right: 50 },
                    duration: 200,
                    essential: true,
                  });
                } else {
                  const bounds = new mapboxgl.LngLatBounds([bbox[0], bbox[1]], [bbox[2], bbox[3]]);

                  map.fitBounds(bounds, {
                    padding: { top: 50, bottom: 50, left: 50, right: 50 },
                    duration: 200,
                    essential: true,
                  });
                }
              } else {
                const bbox = turf.bbox(largestPolygon as any);
                const bounds = new mapboxgl.LngLatBounds([bbox[0], bbox[1]], [bbox[2], bbox[3]]);

                map.fitBounds(bounds, {
                  padding: { top: 50, bottom: 50, left: 50, right: 50 },
                  duration: 200,
                  essential: true,
                });
              }
              return;
            }
          } catch (error) {
            console.error("Error processing MultiPolygon:", error);
          }
        }

        const turfFeature = {
          type: "Feature",
          properties: {},
          geometry: feature.geometry,
        };

        const bbox = turf.bbox(turfFeature as any);
        const bounds = new mapboxgl.LngLatBounds([bbox[0], bbox[1]], [bbox[2], bbox[3]]);

        map.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          duration: 200,
          essential: true,
        });
      } catch (error) {
        console.error("Error zooming to feature:", error);
      } finally {
        setTimeout(() => setIsTransitioning(false), 600);
      }
    },
    [mapRef, setIsTransitioning],
  );

  const handleVoronoiClick = useCallback(
    (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.GeoJSONFeature[] }) => {
      if (!e.features?.length) return;

      e.preventDefault();
      if ((e as any).stopPropagation) {
        (e as any).stopPropagation();
      }
      if ((e as any).stopImmediatePropagation) {
        (e as any).stopImmediatePropagation();
      }

      if (e.originalEvent) {
        e.originalEvent.preventDefault();
        e.originalEvent.stopPropagation();
        if (e.originalEvent.stopImmediatePropagation) {
          e.originalEvent.stopImmediatePropagation();
        }
        (e.originalEvent as any).cancelBubble = true;
      }

      const map = getMapInstance(mapRef);
      if (!map) return;
      const feature = e.features[0];
      const featureId = feature.id || "";
      const sourceId = feature.source || "";
      const sourceLayer = feature.sourceLayer || "";

      if (!featureId || !sourceLayer) return;

      const isClickingSelectedCell =
        selectedVoronoiRef.current?.id === featureId &&
        selectedVoronoiRef.current?.source === sourceId &&
        selectedVoronoiRef.current?.sourceLayer === sourceLayer;

      if (selectedVoronoiRef.current !== null && !isClickingSelectedCell) {
        try {
          map.setFeatureState(
            {
              source: selectedVoronoiRef.current.source,
              sourceLayer: selectedVoronoiRef.current.sourceLayer,
              id: selectedVoronoiRef.current.id,
            },
            { selected: false },
          );
        } catch (error) {
          console.error("Error clearing previous selection:", error);
        }
      }

      if (isClickingSelectedCell) {
        selectedVoronoiRef.current = null;
        map.setFeatureState({ source: sourceId, sourceLayer, id: featureId }, { selected: false });

        setSelectedItem({
          id: selectedItem.type === "ecoregions" ? selectedItem.id : null,
          type: selectedItem.type === "ecoregions" ? selectedItem.type : null,
          properties: selectedItem.type === "ecoregions" ? selectedItem.properties : null,
        });
      } else {
        selectedVoronoiRef.current = { id: featureId as number, source: sourceId, sourceLayer };

        try {
          map.setFeatureState({ source: sourceId, sourceLayer, id: featureId }, { selected: true });
        } catch (error) {
          console.error("Error applying selection:", error);
        }

        setSelectedItem({
          id: feature.properties?.GRID_ID || String(featureId),
          type: "voronoi",
          properties: {
            ...feature.properties,
            ecoregionId: selectedItem.type === "ecoregions" ? selectedItem.id : null,
            ecoregionProperties: selectedItem.type === "ecoregions" ? selectedItem.properties : null,
          },
        });
      }
    },
    [mapRef, setSelectedItem, selectedItem],
  );

  const setupEventHandlers = useCallback(() => {
    if (!mapRef?.current) return;

    const map = (mapRef.current as any).getMap();

    const clearBiomeHover = () => {
      if (hoveredBiomeRef.current !== null) {
        map.setFeatureState({ source: "biomes-source", id: hoveredBiomeRef.current }, { hover: false });
        hoveredBiomeRef.current = null;
      }
    };

    const clearEcoregionHover = () => {
      if (hoveredEcoregionRef.current !== null) {
        map.setFeatureState({ source: "ecoregions-source", id: hoveredEcoregionRef.current }, { hover: false });
        hoveredEcoregionRef.current = null;
      }
    };

    const clearVoronoiHover = () => {
      if (hoveredVoronoiRef.current !== null) {
        try {
          const sourceLayer = hoveredVoronoiRef.current.sourceLayer || "";

          map.setFeatureState(
            {
              source: hoveredVoronoiRef.current.source,
              sourceLayer,
              id: hoveredVoronoiRef.current.id,
            },
            { hover: false },
          );
          hoveredVoronoiRef.current = null;
        } catch (e) {
          console.warn("Could not clear previous voronoi hover:", e);
        }
      }
    };

    const handleBiomeHover = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.GeoJSONFeature[] }) => {
      if (!e.features?.length) return;

      const feature = e.features[0];
      const featureId = feature.id || "";

      if (!featureId) return;

      if (selectedBiomeRef.current === featureId) {
        map.getCanvas().style.cursor = "pointer";
        return;
      }

      if (hoveredBiomeRef.current !== null && hoveredBiomeRef.current !== featureId) {
        map.setFeatureState({ source: "biomes-source", id: hoveredBiomeRef.current }, { hover: false });
      }

      hoveredBiomeRef.current = featureId as number;
      map.setFeatureState({ source: "biomes-source", id: featureId }, { hover: true });

      map.getCanvas().style.cursor = "pointer";
    };

    const handleEcoregionHover = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.GeoJSONFeature[] }) => {
      if (!e.features?.length) return;

      const feature = e.features[0];
      const featureId = feature.id || "";

      if (!featureId) return;

      if (selectedEcoregionRef.current === featureId) {
        map.getCanvas().style.cursor = "pointer";
        return;
      }

      if (hoveredEcoregionRef.current !== null && hoveredEcoregionRef.current !== featureId) {
        map.setFeatureState({ source: "ecoregions-source", id: hoveredEcoregionRef.current }, { hover: false });
      }

      hoveredEcoregionRef.current = featureId as number;
      map.setFeatureState({ source: "ecoregions-source", id: featureId }, { hover: true });

      map.getCanvas().style.cursor = "pointer";
    };

    const handleVoronoiHover = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.GeoJSONFeature[] }) => {
      if (!e.features?.length) return;

      const feature = e.features[0];
      const featureId = feature.id || "";
      const sourceId = feature.source || "";
      const sourceLayer = feature.sourceLayer || "";

      if (!featureId || !sourceLayer) return;

      if (
        selectedVoronoiRef.current?.id === featureId &&
        selectedVoronoiRef.current?.source === sourceId &&
        selectedVoronoiRef.current?.sourceLayer === sourceLayer
      ) {
        map.getCanvas().style.cursor = "pointer";
        return;
      }

      if (
        hoveredVoronoiRef.current !== null &&
        (hoveredVoronoiRef.current.id !== featureId ||
          hoveredVoronoiRef.current.source !== sourceId ||
          hoveredVoronoiRef.current.sourceLayer !== sourceLayer)
      ) {
        try {
          map.setFeatureState(
            {
              source: hoveredVoronoiRef.current.source,
              sourceLayer: hoveredVoronoiRef.current.sourceLayer,
              id: hoveredVoronoiRef.current.id,
            },
            { hover: false },
          );
        } catch (error) {
          console.error("Error clearing previous voronoi hover:", error);
        }
      }

      hoveredVoronoiRef.current = { id: featureId as number, source: sourceId, sourceLayer };

      try {
        map.setFeatureState({ source: sourceId, sourceLayer, id: featureId }, { hover: true });
      } catch (error) {
        console.error("Error applying hover state to voronoi cell:", error);
      }

      map.getCanvas().style.cursor = "pointer";
    };

    const handleMouseLeave = () => {
      map.getCanvas().style.cursor = "";
      clearBiomeHover();
      clearEcoregionHover();
      clearVoronoiHover();
    };

    const handleClick = async (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.GeoJSONFeature[] }) => {
      if (e.defaultPrevented) {
        return;
      }

      if (!e.features?.length) {
        clearSelection();
        setSelectedItem({ id: null, type: null, properties: null });
        return;
      }

      const feature = e.features[0];
      const sourceId = feature.source || "";
      const featureId = feature.id || "";

      if (!featureId) return;

      if (sourceId?.startsWith("voronoi-")) {
        return;
      }

      if (sourceId === "ecoregions-source" && featureId === ecoregiaoComVoronoiAtivo.current) {
        return;
      }

      if (
        sourceId === "ecoregions-source" &&
        ecoregiaoComVoronoiAtivo.current !== null &&
        featureId !== ecoregiaoComVoronoiAtivo.current
      ) {
        if (selectedVoronoiRef.current !== null) {
          try {
            map.setFeatureState(
              {
                source: selectedVoronoiRef.current.source,
                sourceLayer: selectedVoronoiRef.current.sourceLayer,
                id: selectedVoronoiRef.current.id,
              },
              { selected: false },
            );
            selectedVoronoiRef.current = null;
          } catch (e) {
            console.warn("Erro ao limpar seleção de voronoi na troca de ecorregião:", e);
          }
        }
      }

      let type: DataType;
      if (sourceId === "biomes-source") type = "biomes";
      else if (sourceId === "ecoregions-source") type = "ecoregions";
      else return;

      if (feature.properties?.id) {
        setSelectedBiome(feature.properties.id);
      }

      clearBiomeHover();
      clearEcoregionHover();
      clearVoronoiHover();

      if (type === "biomes") {
        if (selectedBiomeRef.current === featureId && selectedItem.type === "biomes") {
          return;
        }

        if (selectedBiomeRef.current !== null && selectedBiomeRef.current !== featureId) {
          map.setFeatureState({ source: "biomes-source", id: selectedBiomeRef.current }, { selected: false });
        }
        selectedBiomeRef.current = featureId as number;
      } else if (type === "ecoregions") {
        if (selectedEcoregionRef.current !== null) {
          map.setFeatureState({ source: "ecoregions-source", id: selectedEcoregionRef.current }, { selected: false });
        }

        if (selectedVoronoiRef.current !== null) {
          try {
            map.setFeatureState(
              {
                source: selectedVoronoiRef.current.source,
                sourceLayer: selectedVoronoiRef.current.sourceLayer,
                id: selectedVoronoiRef.current.id,
              },
              { selected: false },
            );
            selectedVoronoiRef.current = null;
          } catch (e) {
            console.warn("Could not clear voronoi selection:", e);
          }
        }

        selectedEcoregionRef.current = featureId as number;
      }

      map.setFeatureState({ source: sourceId, id: featureId }, { selected: true });

      setSelectedItem({
        id: feature.properties?.id || String(featureId),
        type,
        properties: feature.properties,
      });

      if (type === "biomes") {
        const biomeId = feature.properties?.id;

        if (feature.geometry) {
          try {
            const worldBbox = [-180, -90, 180, 90];
            const worldPoly = turf.bboxPolygon(worldBbox as any);

            const featureGeom = {
              type: "Feature",
              properties: {},
              geometry: feature.geometry,
            };

            const geomFeatures = turf.featureCollection([worldPoly, featureGeom as any]);
            const mask = turf.difference(geomFeatures as any);

            const maskSource = map.getSource("biome-mask-source") as mapboxgl.GeoJSONSource;
            if (maskSource) {
              maskSource.setData(mask as any);
              map.setLayoutProperty("biome-mask", "visibility", "visible");
            }
          } catch (error) {
            console.error("Failed to create mask:", error);
          }
        }

        if (biomeId) {
          try {
            setIsLoading(true);

            const ecoregionsData = await loadEcoregions(biomeId);

            if (ecoregionsData) {
              const ecoregionsSource = map.getSource("ecoregions-source") as mapboxgl.GeoJSONSource;
              if (ecoregionsSource) {
                ecoregionsSource.setData(ecoregionsData);

                updateLayerColors();

                map.setLayoutProperty("ecoregions-fill", "visibility", "visible");
                map.setLayoutProperty("ecoregions-outline", "visibility", "visible");
                setActiveLayers((prev) => ({ ...prev, showEcoregions: true }));
              }
            }
          } catch (error) {
            console.error("Error loading ecoregions:", error);
            setError(`Error loading ecoregions for biome ${biomeId}`);
          } finally {
            setIsLoading(false);
          }
        }
      } else if (type === "ecoregions" && feature.properties?.ecoId) {
        const newEcoId = feature.properties.ecoId;

        ecoregiaoComVoronoiAtivo.current = featureId as number;

        loadVoronoiTiles(newEcoId).then(() => {
          if (map.getLayer("ecoregions-fill")) {
            map.setLayoutProperty("ecoregions-fill", "visibility", "visible");

            map.setPaintProperty("ecoregions-fill", "fill-opacity", [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0,
              0,
            ]);
          }

          if (map.getLayer("ecoregions-outline")) {
            map.setLayoutProperty("ecoregions-outline", "visibility", "visible");

            map.setPaintProperty("ecoregions-outline", "line-opacity", [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              1,
              0.5,
            ]);
          }
        });
      }

      zoomToFeature(feature);
    };

    map._ecoregionsClickHandler = handleClick;

    map.on("mousemove", "biomes-fill", handleBiomeHover);
    map.on("mousemove", "ecoregions-fill", handleEcoregionHover);
    map.on("mouseleave", "biomes-fill", handleMouseLeave);
    map.on("mouseleave", "ecoregions-fill", handleMouseLeave);
    map.on("click", "biomes-fill", handleClick);
    map.on("click", "ecoregions-fill", handleClick);

    const addVoronoiHandlers = () => {
      const voronoiLayers = Object.keys(map.style._layers || {}).filter(
        (layerId) =>
          layerId.startsWith("voronoi-") &&
          layerId.includes("-layer") &&
          !layerId.includes("-outline") &&
          !layerId.includes("-highlight"),
      );

      voronoiLayers.forEach((layerId) => {
        map.off("mousemove", layerId, handleVoronoiHover);
        map.off("mouseleave", layerId, handleMouseLeave);
        map.off("click", layerId, handleVoronoiClick);

        map.on("mousemove", layerId, handleVoronoiHover);
        map.on("mouseleave", layerId, handleMouseLeave);
        map.on("click", layerId, handleVoronoiClick);
      });
    };

    addVoronoiHandlers();

    const originalAddLayer = map.addLayer;
    map.addLayer = function (layer: any, before?: string) {
      const result = originalAddLayer.apply(this, [layer, before]);

      if (
        layer.id &&
        layer.id.startsWith("voronoi-") &&
        layer.id.includes("-layer") &&
        !layer.id.includes("-outline") &&
        !layer.id.includes("-highlight")
      ) {
        setTimeout(() => {
          map.off("mousemove", layer.id, handleVoronoiHover);
          map.off("mouseleave", layer.id, handleMouseLeave);
          map.off("click", layer.id, handleVoronoiClick);

          map.on("mousemove", layer.id, handleVoronoiHover);
          map.on("mouseleave", layer.id, handleMouseLeave);
          map.on("click", layer.id, handleVoronoiClick);
        }, 200);
      }

      return result;
    };

    const generalClickHandler = (e: mapboxgl.MapMouseEvent) => {
      const layersToQuery = ["biomes-fill", "ecoregions-fill"].filter((layerId) => map.getLayer(layerId));

      if (layersToQuery.length === 0) return;

      const features = map.queryRenderedFeatures(e.point, {
        layers: layersToQuery,
      });

      if (features.length === 0) {
        clearSelection();
      }
    };

    map.on("click", generalClickHandler);

    return () => {
      map.off("mousemove", "biomes-fill", handleBiomeHover);
      map.off("mousemove", "ecoregions-fill", handleEcoregionHover);
      map.off("mouseleave", "biomes-fill", handleMouseLeave);
      map.off("mouseleave", "ecoregions-fill", handleMouseLeave);
      map.off("click", "biomes-fill", handleClick);
      map.off("click", "ecoregions-fill", handleClick);
      map.off("click", generalClickHandler);

      map.addLayer = originalAddLayer;

      // Get all layers from the map
      const allLayers = map.getStyle()?.layers || [];
      // Extract layer IDs that match our criteria
      const voronoiLayers = allLayers
        .filter(
          (layer: any) =>
            layer.id &&
            layer.id.startsWith("voronoi-") &&
            layer.id.includes("-layer") &&
            !layer.id.includes("-outline") &&
            !layer.id.includes("-highlight"),
        )
        .map((layer: any) => layer.id);

      voronoiLayers.forEach((layerId: string) => {
        map.off("mousemove", layerId, handleVoronoiHover);
        map.off("mouseleave", layerId, handleMouseLeave);
        map.off("click", layerId, handleVoronoiClick);
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    mapRef,
    loadEcoregions,
    clearSelection,
    zoomToFeature,
    updateLayerColors,
    loadVoronoiTiles,
    setSelectedBiome,
    handleVoronoiClick,
    selectedItem,
  ]);

  const toggleLayerVisibility = useCallback(
    (type: DataType, visible: boolean) => {
      const map = getMapInstance(mapRef);
      if (!map) return;

      const allLayers = map.getStyle()?.layers || [];
      const voronoiLayerIds = allLayers
        .filter((layer: any) => layer.id && layer.id.startsWith("voronoi-") && layer.id.endsWith("-layer"))
        .map((layer: any) => layer.id);

      const layersMap = {
        biomes: ["biomes-fill", "biomes-outline"],
        ecoregions: ["ecoregions-fill", "ecoregions-outline"],
        voronoi: voronoiLayerIds,
      };

      layersMap[type].forEach((layerId: string) => {
        map.setLayoutProperty(layerId, "visibility", visible ? "visible" : "none");
      });

      setActiveLayers((prev) => ({
        ...prev,
        [`show${type.charAt(0).toUpperCase() + type.slice(1)}`]: visible,
      }));
    },
    [mapRef],
  );

  const updateSource = useCallback(
    (type: DataType, data: GeoJSON.FeatureCollection) => {
      const map = getMapInstance(mapRef);
      if (!map) return;
      const source = map.getSource(`${type}-source`) as mapboxgl.GeoJSONSource;

      if (data && data.features) {
        data.features = data.features.map((feature, index) => {
          return {
            ...feature,
            id: feature.properties?.id || `${type}-${index}`,
          };
        });
      }

      if (source) {
        source.setData(data);
      }
    },
    [mapRef],
  );

  const cleanup = useCallback(() => {
    const map = getMapInstance(mapRef);
    if (!map) return;

    ["biomes-fill", "biomes-outline", "ecoregions-fill", "ecoregions-outline", "biome-mask"].forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    const voronoiLayers = Object.keys(map.style._layers || {}).filter(
      (layerId) => layerId.startsWith("voronoi-") && layerId.endsWith("-layer"),
    );

    voronoiLayers.forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }

      const sourceId = layerId.replace("-layer", "-source");
      if (map.getSource(sourceId)) {
        map.removeSource(sourceId);
      }
    });

    ["biomes-source", "ecoregions-source", "biome-mask-source"].forEach((sourceId) => {
      if (map.getSource(sourceId)) {
        map.removeSource(sourceId);
      }
    });
  }, [mapRef]);

  const resetView = useCallback(() => {
    const map = getMapInstance(mapRef);
    if (!map) return;

    clearSelection();

    map.flyTo({
      center: [0, 20],
      zoom: 2,
      duration: 200,
    });
  }, [clearSelection, mapRef]);

  return {
    biomeLayers,
    ecoregionLayers,
    activeLayers,
    selectedItem,
    isLoading,
    error,
    initializeLayers,
    toggleLayerVisibility,
    clearSelection,
    resetView,
    cleanup,
    updateSource,
    loadVoronoiTiles,
    getSelectedCellInfo: () => {
      if (selectedItem?.type === "voronoi" && selectedItem?.properties) {
        const { GRID_ID, ECO_ID, cellNumber } = selectedItem.properties;

        return {
          cellId: cellNumber || (GRID_ID ? GRID_ID.split("_")[1] : null),
          ecoregionId: ECO_ID,
          gridId: GRID_ID,
          center: selectedItem.properties.center || null,
          allProperties: selectedItem.properties,
        };
      }
      return null;
    },
  };
};
