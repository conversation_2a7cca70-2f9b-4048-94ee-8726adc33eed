import { TOOLTIP_MESSAGES, createMultiSelectTooltip } from "@/utils/multiSelectTooltip";
import throttle from "lodash/throttle";
import type { LayerSpecification } from "mapbox-gl";
import mapboxgl from "mapbox-gl";
import { useCallback, useEffect, useRef } from "react";

import { useSoilsManagementStore } from "./store";

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: mapboxgl.Map,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

export function useSoils(mapRef?: React.RefObject<mapboxgl.Map | null>, mapLoaded?: boolean) {
  const currentHoveredId = useRef<string | null>(null);
  const currentSelectedId = useRef<string | null>(null);
  const {
    setSelectedSoilId,
    clearSelectedSoils,
    setMultiSelectMode,
    setIsLoadingSoil,
    setSelectedSoilDetails,
    resetSoilData,
  } = useSoilsManagementStore();

  const SOILS_MIN_ZOOM = 0;
  const SOILS_MAX_ZOOM = 22;

  const onMouseMove = useCallback(
    throttle((e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map) return;

      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as string;

      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    }, 16),
    [mapRef],
  );

  const clearSelection = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (currentSelectedId.current !== null) {
      map.setFeatureState(
        { source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: currentSelectedId.current },
        { selected: false },
      );
      currentSelectedId.current = null;
    }

    const { selectedSoilsMulti } = useSoilsManagementStore.getState();
    selectedSoilsMulti.forEach((id) => {
      map.setFeatureState({ source: "soil-tiles", sourceLayer: "ibi_fao_soils", id }, { selected: false });
    });

    clearSelectedSoils();
    setSelectedSoilId(null);
    setMultiSelectMode(false);
    resetSoilData();
  }, [mapRef, clearSelectedSoils, setSelectedSoilId, setMultiSelectMode, resetSoilData]);

  const onSoilClick = useCallback(
    async (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map || !e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as string;
      const isCtrlPressed = e.originalEvent ? (e.originalEvent as MouseEvent).ctrlKey : false;
      const soilsStore = useSoilsManagementStore.getState();
      const {
        addSelectedSoil,
        removeSelectedSoil,
        setMultiSelectMode,
        selectedSoilsMulti,
        clearSelectedSoils,
        setSelectedSoilId,
      } = soilsStore;

      if (currentSelectedId.current === null) {
        createMultiSelectTooltip({
          id: "soils-multi-tooltip",
          message: TOOLTIP_MESSAGES.SOILS,
        });
      }

      if (isCtrlPressed) {
        setMultiSelectMode(true);
        if (selectedSoilsMulti.includes(featureId)) {
          removeSelectedSoil(featureId);
          map.setFeatureState(
            { source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: featureId },
            { selected: false },
          );
        } else {
          addSelectedSoil(featureId);
          map.setFeatureState(
            { source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: featureId },
            { selected: true },
          );
        }
        setSelectedSoilId(featureId);
        return;
      } else {
        setMultiSelectMode(false);
        if (selectedSoilsMulti.length > 0) {
          selectedSoilsMulti.forEach((id) => {
            if (id !== featureId) {
              map.setFeatureState({ source: "soil-tiles", sourceLayer: "ibi_fao_soils", id }, { selected: false });
            }
          });
          clearSelectedSoils();
        }
        if (currentSelectedId.current && currentSelectedId.current !== featureId) {
          map.setFeatureState(
            { source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: currentSelectedId.current },
            { selected: false },
          );
        }
        map.setFeatureState({ source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: featureId }, { selected: true });
        currentSelectedId.current = featureId;
        setSelectedSoilId(featureId);

        if (e.features[0].geometry.type === "Polygon" || e.features[0].geometry.type === "MultiPolygon") {
          const bounds = new mapboxgl.LngLatBounds();

          const coordinates =
            e.features[0].geometry.type === "Polygon"
              ? e.features[0].geometry.coordinates[0]
              : e.features[0].geometry.coordinates.flat(1);

          if (coordinates && coordinates.length) {
            coordinates.forEach((coord: any) => {
              if (Array.isArray(coord) && coord.length >= 2) {
                bounds.extend([coord[0], coord[1]]);
              }
            });
          }

          if (!bounds.isEmpty()) {
            const center = bounds.getCenter();
            map.flyTo({
              center: [center.lng, center.lat],
              zoom: map.getZoom(),
              animate: true,
              duration: 500,
              essential: true,
            });
          }
        } else if (e.lngLat) {
          map.flyTo({
            center: [e.lngLat.lng, e.lngLat.lat],
            zoom: map.getZoom(),
            essential: true,
            animate: true,
            duration: 500,
          });
        }

        try {
          setIsLoadingSoil(true);
          setSelectedSoilDetails({
            id: featureId,
            properties: e.features[0].properties,
          });
        } catch (error) {
          console.error("Failed to fetch soil details:", error);
        } finally {
          setIsLoadingSoil(false);
        }
      }
    },
    [mapRef, setIsLoadingSoil, setSelectedSoilDetails],
  );

  const cleanupLayers = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (layerExists(map, "soil-tiles-layer")) {
      map.off("mousemove", "soil-tiles-layer", onMouseMove);
      map.off("mouseleave", "soil-tiles-layer", () => {});
      map.off("click", "soil-tiles-layer", onSoilClick);
    }

    ["soil-tiles-layer", "soil-tiles-border", "soil-tiles-glow"].forEach((layerId) => {
      if (layerExists(map, layerId)) {
        try {
          map.removeLayer(layerId);
        } catch (error) {
          console.error(`Error removing layer ${layerId}:`, error);
        }
      }
    });

    if (sourceExists(map, "soil-tiles")) {
      try {
        map.removeSource("soil-tiles");
      } catch (error) {
        console.error("Error removing soil-tiles source:", error);
      }
    }

    currentHoveredId.current = null;
    currentSelectedId.current = null;
  }, [mapRef, onMouseMove, onSoilClick]);

  const setupSoilsLayer = useCallback(() => {
    const map = mapRef?.current;
    if (!map || !mapLoaded) return;

    cleanupLayers();

    try {
      if (!sourceExists(map, "soil-tiles")) {
        map.addSource("soil-tiles", {
          type: "vector",
          tiles: ["https://tiles.yby.energy/dev/soils/{z}/{x}/{y}"],
          minzoom: SOILS_MIN_ZOOM,
          maxzoom: SOILS_MAX_ZOOM,
        });
      }

      const layersToAdd: LayerSpecification[] = [];

      if (!layerExists(map, "soil-tiles-layer")) {
        layersToAdd.push({
          id: "soil-tiles-layer",
          type: "fill",
          source: "soil-tiles",
          "source-layer": "ibi_fao_soils",
          minzoom: SOILS_MIN_ZOOM,
          maxzoom: SOILS_MAX_ZOOM,
          paint: {
            "fill-color": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              "transparent",
              ["boolean", ["feature-state", "hover"], false],
              "#D2691E",
              "#A0522D",
            ],
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0.4,
              ["boolean", ["feature-state", "hover"], false],
              0.6,
              0.3,
            ],
          },
        });
      }

      if (!layerExists(map, "soil-tiles-border")) {
        layersToAdd.push({
          id: "soil-tiles-border",
          type: "line",
          source: "soil-tiles",
          "source-layer": "ibi_fao_soils",
          minzoom: SOILS_MIN_ZOOM,
          maxzoom: SOILS_MAX_ZOOM,
          paint: {
            "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#CD853F", "#D2691E"],
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 4, 2],
            "line-opacity": ["case", ["boolean", ["feature-state", "selected"], false], 1, 0.7],
          },
        });
      }

      layersToAdd.forEach((layer) => {
        if (layer && layer.id) {
          try {
            map.addLayer(layer);
          } catch (error) {
            console.error(`Error adding layer ${layer.id}:`, error);
          }
        }
      });

      map.on("mousemove", "soil-tiles-layer", onMouseMove);

      map.on("mouseleave", "soil-tiles-layer", () => {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "soil-tiles", sourceLayer: "ibi_fao_soils", id: currentHoveredId.current },
            { hover: false },
          );
          currentHoveredId.current = null;
        }
        map.getCanvas().style.cursor = "";
      });

      map.on("click", "soil-tiles-layer", onSoilClick);

      map.on("click", (e) => {
        const soilFeatures = safeQueryRenderedFeatures(map, e.point, {
          layers: ["soil-tiles-layer"],
        });

        if (soilFeatures.length === 0) {
          clearSelection();
        }
      });
    } catch (error) {
      console.error("Error setting up soils layer:", error);
    }
  }, [mapRef, mapLoaded, cleanupLayers, clearSelection, onMouseMove, onSoilClick]);

  useEffect(() => {
    if (mapLoaded) {
      setupSoilsLayer();
    }
  }, [mapLoaded, setupSoilsLayer]);

  useEffect(() => {
    return () => {
      resetSoilData();
      cleanupLayers();
    };
  }, [resetSoilData, cleanupLayers]);

  return {
    setupSoilsLayer,
    cleanupLayers,
    onMouseMove,
    clearSelection,
  };
}
