import { create } from "zustand";

interface SoilDetails {
  id: string;
  properties?: any;
}

interface SoilsManagementState {
  selectedSoilId: string | null;
  setSelectedSoilId: (id: string | null) => void;

  selectedSoilDetails: SoilDetails | null;
  setSelectedSoilDetails: (details: SoilDetails | null) => void;

  isLoadingSoil: boolean;
  setIsLoadingSoil: (isLoading: boolean) => void;

  resetSoilData: () => void;

  selectedSoilsMulti: string[];
  addSelectedSoil: (id: string) => void;
  removeSelectedSoil: (id: string) => void;
  clearSelectedSoils: () => void;

  isMultiSelectMode: boolean;
  setMultiSelectMode: (isActive: boolean) => void;
  showMultiSelectTooltip: boolean;
  setShowMultiSelectTooltip: (show: boolean) => void;
}

export const useSoilsManagementStore = create<SoilsManagementState>((set) => ({
  selectedSoilId: null,
  setSelectedSoilId: (id) => set({ selectedSoilId: id }),

  selectedSoilDetails: null,
  setSelectedSoilDetails: (details) => set({ selectedSoilDetails: details }),

  isLoadingSoil: false,
  setIsLoadingSoil: (isLoading) => set({ isLoadingSoil: isLoading }),

  resetSoilData: () =>
    set({
      selectedSoilDetails: null,
      isLoadingSoil: false,
      selectedSoilId: null,
      selectedSoilsMulti: [],
      isMultiSelectMode: false,
      showMultiSelectTooltip: false,
    }),

  selectedSoilsMulti: [],
  addSelectedSoil: (id) =>
    set((state) => {
      if (!state.selectedSoilsMulti.includes(id)) {
        return { selectedSoilsMulti: [...state.selectedSoilsMulti, id] };
      }
      return state;
    }),
  removeSelectedSoil: (id) =>
    set((state) => ({
      selectedSoilsMulti: state.selectedSoilsMulti.filter((soilId) => soilId !== id),
    })),
  clearSelectedSoils: () => set({ selectedSoilsMulti: [] }),

  isMultiSelectMode: false,
  setMultiSelectMode: (isActive) => set({ isMultiSelectMode: isActive }),
  showMultiSelectTooltip: false,
  setShowMultiSelectTooltip: (show) => set({ showMultiSelectTooltip: show }),
}));
