import { useCallback, useState } from "react";

import useMap from "../useMap";
import { useMapZoom } from "../useMapZoom";
import { useAlertsLayer } from "./useAlertsLayer";
import { useForestLayer } from "./useForestLayer";
import { useMoveBankLayer } from "./useMoveBankLayer";
import { useNasaFireLayer } from "./useNasaFireLayer";
import { useOpenWeatherLayers } from "./useOpenWeatherLayers";
import { useProperties } from "./useProperties";

export const useLayerControl = () => {
  const [mapLoaded, setMapLoaded] = useState(false);
  const { currentLayers, mapRef } = useMap();

  useProperties(mapRef || undefined, mapLoaded);

  useForestLayer({
    map: mapRef?.current as mapboxgl.Map,
    enabled: currentLayers.includes("forest"),
  });

  useAlertsLayer({
    map: mapRef?.current as mapboxgl.Map,
    enabled: currentLayers.includes("alerts"),
  });

  useMoveBankLayer({
    map: mapRef?.current as mapboxgl.Map,
    enabled: currentLayers.includes("movebank"),
  });

  useNasaFireLayer({
    map: mapRef?.current as mapboxgl.Map,
    enabled: currentLayers.includes("nasa"),
  });

  useOpenWeatherLayers(mapRef?.current as mapboxgl.Map, currentLayers);

  const { setupPropertyHandlers } = useMapZoom({
    mapRef,
  });

  const handleMapLoad = useCallback(
    (event: { target: mapboxgl.Map }) => {
      if (mapRef) (mapRef as React.MutableRefObject<mapboxgl.Map>).current = event.target;
      setMapLoaded(true);
      setupPropertyHandlers();
    },
    [setupPropertyHandlers],
  );

  return {
    mapRef,
    handleMapLoad,
  };
};
