import { useMapData } from "@/queries/hooks/useTerritories";
import { Map } from "mapbox-gl";
import { useEffect, useRef } from "react";

const getMapInstance = (map: Map | null): mapboxgl.Map | null => {
  if (!map) return null;

  try {
    return (map as any).getMap();
  } catch (error) {
    return map;
  }
};

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

interface UseAlertsLayerProps {
  map: Map | null;
  enabled: boolean;
}

export const useAlertsLayer = ({ map, enabled }: UseAlertsLayerProps) => {
  const { alertsData } = useMapData({
    enabled: {
      alerts: enabled,
      forest: false,
    },
  });

  const previousUrl = useRef<string | undefined>();
  const alertsUrl = alertsData?.data?.data?.url;

  useEffect(() => {
    if (!map) return;

    const mapInstance = getMapInstance(map);
    if (!mapInstance) return;

    const hasAlertsSource = sourceExists(mapInstance, "alerts-source");

    if (previousUrl.current === alertsUrl && hasAlertsSource) {
      return;
    }

    previousUrl.current = alertsUrl;

    if (enabled && alertsUrl) {
      if (!hasAlertsSource) {
        try {
          mapInstance.addSource("alerts-source", {
            type: "raster",
            tiles: [alertsUrl],
            tileSize: 256,
          });
        } catch (error) {
          console.error("Error adding alerts source:", error);
          return;
        }
      } else {
        try {
          (mapInstance.getSource("alerts-source") as mapboxgl.RasterTileSource).setTiles([alertsUrl]);
        } catch (error) {
          console.error("Error setting alerts tiles:", error);
          return;
        }
      }

      const hasAlertsLayer = layerExists(mapInstance, "alerts-layer");

      if (!hasAlertsLayer) {
        try {
          mapInstance.addLayer({
            id: "alerts-layer",
            type: "raster",
            source: "alerts-source",
            paint: {
              "raster-opacity": 0.7,
            },
          });
        } catch (error) {
          console.error("Error adding alerts layer:", error);
        }
      }
    } else {
      if (layerExists(mapInstance, "alerts-layer")) {
        try {
          mapInstance.removeLayer("alerts-layer");
        } catch (error) {
          console.error("Error removing alerts layer:", error);
        }
      }

      if (sourceExists(mapInstance, "alerts-source")) {
        try {
          mapInstance.removeSource("alerts-source");
        } catch (error) {
          console.error("Error removing alerts source:", error);
        }
      }
    }

    return () => {
      if (map) {
        const mapInstance = getMapInstance(map);
        if (!mapInstance) return;

        if (layerExists(mapInstance, "alerts-layer")) {
          try {
            mapInstance.removeLayer("alerts-layer");
          } catch (error) {
            console.error("Error removing alerts layer in cleanup:", error);
          }
        }

        if (sourceExists(mapInstance, "alerts-source")) {
          try {
            mapInstance.removeSource("alerts-source");
          } catch (error) {
            console.error("Error removing alerts source in cleanup:", error);
          }
        }
      }
    };
  }, [map, enabled, alertsUrl]);

  return {
    isLoading: alertsData.isLoading,
    error: alertsData.error,
  };
};
