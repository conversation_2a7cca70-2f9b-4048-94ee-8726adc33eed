import { PropertyDetails } from "@/types";
import { create } from "zustand";

interface PropertyManagementStoreState {
  selectedPropertyDetails: PropertyDetails | null;
  setSelectedPropertyDetails: (details: PropertyDetails | null) => void;
  isLoadingProperty: boolean;
  setIsLoadingProperty: (isLoading: boolean) => void;
  resetPropertyData: () => void;
}

export const usePropertyManagementStore = create<PropertyManagementStoreState>((set) => ({
  selectedPropertyDetails: null,
  setSelectedPropertyDetails: (details) => set({ selectedPropertyDetails: details }),
  isLoadingProperty: false,
  setIsLoadingProperty: (isLoading) => set({ isLoadingProperty: isLoading }),
  resetPropertyData: () => set({ selectedPropertyDetails: null, isLoadingProperty: false }),
}));
