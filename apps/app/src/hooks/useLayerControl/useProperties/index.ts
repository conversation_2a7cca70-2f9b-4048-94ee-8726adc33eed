import useMap from "@/hooks/useMap";
import { useClaimedProperties } from "@/queries/hooks/useTerritories";
import { territoriesService } from "@/services/territories.service";
import { useMapSelector } from "@/stores/map.store";
import { useQueryClient } from "@tanstack/react-query";
import throttle from "lodash/throttle";
import type { LayerSpecification } from "mapbox-gl";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useRef } from "react";

import { propertyEventHandler as p } from "./events";
import { usePropertyManagementStore } from "./store";

const DEFAULT_BEARING = 0;
const DEFAULT_PITCH = 0;

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: mapboxgl.Map,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

export function useProperties(mapRef?: React.RefObject<mapboxgl.Map | null>, mapLoaded?: boolean) {
  const pathname = usePathname();
  const shouldEnableProperties = pathname.includes("claim");

  const currentHoveredId = useRef<number | null>(null);
  const currentSelectedId = useRef<number | null>(null);
  const { setSelectedPropertyDetails, setIsLoadingProperty, resetPropertyData } = usePropertyManagementStore();
  const { data: claimedProperties } = useClaimedProperties();
  const queryClient = useQueryClient();
  const setSelectedTerritory = useMapSelector.use.setSelectedTerritory();
  const { currentBaseMap } = useMap();
  const reloadProperties = useMapSelector.use.reloadProperties();
  const triggerPropertiesReload = useMapSelector.use.triggerPropertiesReload();
  const mapType = useMapSelector.use.selectedItem()?.type;

  const currentMapType = pathname === "/predict" ? "hydro" : "default";

  const PROPERTIES_MIN_ZOOM = 2;
  const PROPERTIES_MAX_ZOOM = 12;

  const onMouseMove = useCallback(
    throttle((e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map) return;

      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as number;

      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "properties-source", sourceLayer: "imoveis", id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "properties-source", sourceLayer: "imoveis", id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    }, 16),
    [mapRef],
  );

  const clearSelection = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (currentSelectedId.current !== null) {
      map.setFeatureState(
        { source: "properties-source", sourceLayer: "imoveis", id: currentSelectedId.current },
        { selected: false },
      );
      currentSelectedId.current = null;
    }

    resetPropertyData();
    setSelectedTerritory(null);

    map.easeTo({
      bearing: DEFAULT_BEARING,
      pitch: DEFAULT_PITCH,
      duration: 200,
    });
  }, [mapRef, resetPropertyData, setSelectedTerritory]);

  const onPropertyClick = useCallback(
    async (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map || !e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as number;

      if (currentSelectedId.current !== null) {
        map.setFeatureState(
          { source: "properties-source", sourceLayer: "imoveis", id: currentSelectedId.current },
          { selected: false },
        );
      }

      map.setFeatureState({ source: "properties-source", sourceLayer: "imoveis", id: featureId }, { selected: true });

      currentSelectedId.current = featureId;

      try {
        setIsLoadingProperty(true);
        const details = await queryClient.fetchQuery({
          queryKey: ["property", featureId.toString(), e?.lngLat?.lat, e?.lngLat?.lng],
          queryFn: () => territoriesService.getPropertyDetails(featureId.toString(), e?.lngLat?.lat, e?.lngLat?.lng),
        });

        setSelectedPropertyDetails({
          ...details,
          propertyId: featureId,
        });
      } catch (error) {
        console.error("Failed to fetch property details:", error);
      } finally {
        setIsLoadingProperty(false);
      }
    },
    [mapRef, queryClient, setIsLoadingProperty, setSelectedPropertyDetails],
  );

  const cleanupLayers = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (layerExists(map, "properties")) {
      map.off("mousemove", "properties", onMouseMove);
      map.off("mouseleave", "properties", () => {});
      map.off("click", "properties", onPropertyClick);
    }

    map.off("click", p.events.onBaseMapClick as any);

    [
      "properties-border",
      "properties-glow-3",
      "properties-glow-2",
      "properties-glow-1",
      "properties",
      "property-outline",
    ].forEach((layerId) => {
      if (layerExists(map, layerId)) {
        try {
          map.removeLayer(layerId);
        } catch (error) {
          console.error(`Error removing layer ${layerId}:`, error);
        }
      }
    });

    if (sourceExists(map, "properties-source")) {
      try {
        map.removeSource("properties-source");
      } catch (error) {
        console.error("Error removing properties-source:", error);
      }
    }

    currentHoveredId.current = null;
    currentSelectedId.current = null;
  }, [mapRef, onMouseMove, onPropertyClick]);

  const setupPropertyLayer = useCallback(() => {
    const map = mapRef?.current;
    if (!map || !mapLoaded || !shouldEnableProperties) return;

    cleanupLayers();

    try {
      if (!sourceExists(map, "properties-source")) {
        map.addSource("properties-source", {
          type: "vector",
          tiles: ["https://tiles.yby.energy/dev/all-properties/{z}/{x}/{y}"],
          minzoom: PROPERTIES_MIN_ZOOM,
          maxzoom: PROPERTIES_MAX_ZOOM,
          promoteId: "IBI_COD",
        });
      }

      const layersToAdd: LayerSpecification[] = [];

      if (!layerExists(map, "properties")) {
        layersToAdd.push({
          id: "properties",
          type: "fill",
          source: "properties-source",
          "source-layer": "imoveis",
          minzoom: PROPERTIES_MIN_ZOOM,
          paint: {
            "fill-color": ["case", ["boolean", ["feature-state", "hover"], false], "#768FFF", "#0288d1"],
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0,
              ["boolean", ["feature-state", "hover"], false],
              0.6,
              0.4,
            ],
          },
        });
      }

      if (!layerExists(map, "property-outline")) {
        layersToAdd.push({
          id: "property-outline",
          type: "line",
          source: "properties-source",
          "source-layer": "imoveis",
          minzoom: PROPERTIES_MIN_ZOOM,
          paint: {
            "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
            "line-opacity": 1,
          },
        });
      }

      layersToAdd.forEach((layer) => {
        if (layer && layer.id) {
          try {
            map.addLayer(layer);
          } catch (error) {
            console.error(`Error adding layer ${layer.id}:`, error);
          }
        }
      });

      map.on("mousemove", "properties", onMouseMove);

      map.on("mouseleave", "properties", () => {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "properties-source", sourceLayer: "imoveis", id: currentHoveredId.current },
            { hover: false },
          );
          currentHoveredId.current = null;
        }
        map.getCanvas().style.cursor = "";
      });

      map.on("click", "properties", onPropertyClick);

      map.on("click", (e) => {
        const propertyFeatures = safeQueryRenderedFeatures(map, e.point, {
          layers: ["properties"],
        });

        if (propertyFeatures.length === 0) {
          clearSelection();
        }
      });
    } catch (error) {
      console.error("Error setting up property layer:", error);
    }
  }, [
    mapRef,
    mapLoaded,
    shouldEnableProperties,
    cleanupLayers,
    claimedProperties,
    clearSelection,
    onMouseMove,
    onPropertyClick,
  ]);

  useEffect(() => {
    if (mapLoaded && shouldEnableProperties) {
      setupPropertyLayer();
    }
  }, [mapLoaded, shouldEnableProperties, setupPropertyLayer]);

  useEffect(() => {
    if (!mapRef?.current || !mapLoaded || !shouldEnableProperties) return;

    setTimeout(() => {
      triggerPropertiesReload();
    }, 500);
  }, [currentBaseMap, mapRef, mapLoaded, shouldEnableProperties, triggerPropertiesReload]);

  useEffect(() => {
    if (!mapRef?.current || !mapLoaded || !shouldEnableProperties) return;

    setTimeout(() => {
      cleanupLayers();
      setupPropertyLayer();
    }, 300);
  }, [reloadProperties, mapRef, mapLoaded, shouldEnableProperties, cleanupLayers, setupPropertyLayer]);

  useEffect(() => {
    if (shouldEnableProperties && (mapType === "biomes" || mapType === "ecoregions" || mapType === "voronoi")) {
      clearSelection();
    }
  }, [mapType, shouldEnableProperties, clearSelection]);

  useEffect(() => {
    if (shouldEnableProperties && currentMapType !== "default") {
      clearSelection();
    }
  }, [currentMapType, shouldEnableProperties, clearSelection]);

  useEffect(() => {
    // Cleanup quando não está na tela /claim
    if (!shouldEnableProperties) {
      cleanupLayers();
      resetPropertyData();
    }
  }, [shouldEnableProperties, cleanupLayers, resetPropertyData]);

  useEffect(() => {
    return () => {
      resetPropertyData();
      cleanupLayers();
    };
  }, [resetPropertyData, cleanupLayers]);

  return {
    setupPropertyLayer,
    cleanupLayers,
    onMouseMove,
    clearSelection,
  };
}
