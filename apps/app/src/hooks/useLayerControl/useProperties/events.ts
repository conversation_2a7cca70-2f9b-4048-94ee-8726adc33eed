import type { ClaimedProperties } from "@/types";
import { PROPERTIES_MIN_ZOOM } from "@/utils/constants";
import { FillLayer, MapMouseEvent } from "mapbox-gl";
import { LineLayer } from "mapbox-gl";
import type { Map as Map$1 } from "mapbox-gl";
import mapboxgl from "mapbox-gl";

const ZOOM_PROPERTY_CLICK = 7;

const layerExists = (map: Map$1, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: Map$1, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: Map$1,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

interface PropertyGlowLayer {
  1: LineLayer;
  2: LineLayer;
  3: LineLayer;
}

export const propertyEventHandler = {
  events: {
    onPropertyClick: async (
      e: MapMouseEvent,
      map: Map$1,
      currentSelectedId: React.MutableRefObject<number | null>,
      setIsLoadingProperty: (isLoading: boolean) => void,
      setSelectedPropertyDetails: (details: any) => void,
      queryClient: any,

      // TODO: might need to check typesafety later in here aswell.
      territoriesService: any,
    ) => {
      if (!e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as number;
      if (currentSelectedId.current !== null) {
        map.setFeatureState(
          { source: "properties-source", sourceLayer: "properties", id: currentSelectedId.current },
          { selected: false },
        );
      }

      map.setFeatureState(
        { source: "properties-source", sourceLayer: "properties", id: featureId },
        { selected: true },
      );

      currentSelectedId.current = featureId;

      if (e.features[0].geometry.type === "Polygon" || e.features[0].geometry.type === "MultiPolygon") {
        const bounds = new mapboxgl.LngLatBounds();

        const coordinates =
          e.features[0].geometry.type === "Polygon"
            ? e.features[0].geometry.coordinates[0]
            : e.features[0].geometry.coordinates.flat(1);

        if (coordinates && coordinates.length) {
          coordinates.forEach((coord: any) => {
            if (Array.isArray(coord) && coord.length >= 2) {
              bounds.extend([coord[0], coord[1]]);
            }
          });
        }

        map.fitBounds(bounds, {
          padding: 50,
          maxZoom: ZOOM_PROPERTY_CLICK,
          animate: true,
          duration: 200,
        });
      } else if (e.lngLat) {
        map.flyTo({
          center: [e.lngLat.lng, e.lngLat.lat],
          zoom: ZOOM_PROPERTY_CLICK,
          essential: true,
          animate: true,
          duration: 200,
        });
      }

      try {
        setIsLoadingProperty(true);
        const details = await queryClient.fetchQuery({
          queryKey: ["property", featureId.toString()],
          queryFn: () => territoriesService.getPropertyDetails(featureId.toString()),
        });

        setSelectedPropertyDetails({
          ...details,
          propertyId: featureId,
        });
      } catch (error) {
        console.error("Failed to fetch property details:", error);
      } finally {
        setIsLoadingProperty(false);
      }
    },
    onBaseMapClick: (e: MapMouseEvent, map: Map$1, clearSelection: () => void) => {
      const propertyFeatures = safeQueryRenderedFeatures(map, e.point, {
        layers: ["properties"],
      });

      if (propertyFeatures.length === 0) {
        const countryStateFeatures = safeQueryRenderedFeatures(map, e.point, {
          layers: ["state-fills", "state-borders", "country-fills", "country-borders"],
        });

        if (countryStateFeatures.length === 0) {
          clearSelection();
        }
      }
    },
    onMouseLeaveProperty: (map: Map$1, currentHoveredId: React.MutableRefObject<number | null>) => {
      if (currentHoveredId.current !== null) {
        map.setFeatureState(
          { source: "properties-source", sourceLayer: "properties", id: currentHoveredId.current },
          { hover: false },
        );
        currentHoveredId.current = null;
      }

      map.getCanvas().style.cursor = "";
    },
    onMouseMoveOnProperty: (e: MapMouseEvent, map: Map$1, currentHoveredId: React.MutableRefObject<number | null>) => {
      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as number;
      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "properties-source", sourceLayer: "properties", id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "properties-source", sourceLayer: "properties", id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    },
    clearPropertySelection: (map: Map$1, currentSelectedId: React.MutableRefObject<number | null>) => {
      if (currentSelectedId.current !== null) {
        map.setFeatureState(
          { source: "properties-source", sourceLayer: "properties", id: currentSelectedId.current },
          { selected: false },
        );
        currentSelectedId.current = null;
      }
    },
    removePropertyLayers: (
      map: Map$1,
      currentHoveredId: React.MutableRefObject<number | null>,
      currentSelectedId: React.MutableRefObject<number | null>,
      onMouseMove: (e: MapMouseEvent) => void,
    ) => {
      try {
        if (layerExists(map, "properties")) {
          map.off("mousemove", "properties", onMouseMove);
          map.off("mouseleave", "properties", () => {});
          map.off("click", "properties", () => {});
        }

        ["properties-border", "properties-glow-3", "properties-glow-2", "properties-glow-1", "properties"].forEach(
          (layerId) => {
            if (layerExists(map, layerId)) {
              try {
                map.removeLayer(layerId);
              } catch (error) {
                console.error(`Error removing layer ${layerId}:`, error);
              }
            }
          },
        );

        if (sourceExists(map, "properties-source")) {
          try {
            map.removeSource("properties-source");
          } catch (error) {
            console.error("Error removing properties-source:", error);
          }
        }

        currentHoveredId.current = null;
        currentSelectedId.current = null;
      } catch (error) {
        console.error("Error in removePropertyLayers:", error);
      }
    },
  },
  layers: {
    property_layer: (claimedProperties: ClaimedProperties): FillLayer => {
      return {
        id: "properties",
        type: "fill",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "fill-color": [
            "case",
            ["boolean", ["feature-state", "hover"], false],
            "#768FFF",
            ["in", ["get", "id"], ["literal", claimedProperties.map((prop) => prop.id)]],
            "#4CAF50",
            "#0288d1",
          ],
          "fill-opacity": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            0,
            ["boolean", ["feature-state", "hover"], false],
            0.6,
            ["in", ["get", "id"], ["literal", claimedProperties.map((prop) => prop.id)]],
            0.5,
            0.4,
          ],
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      };
    },
    property_glow_layer: (claimedProperties: ClaimedProperties): PropertyGlowLayer => {
      return {
        1: {
          id: "properties-glow-1",
          type: "line",
          source: "properties-source",
          "source-layer": "properties",
          paint: {
            "line-color": [
              "case",
              ["in", ["get", "id"], ["literal", claimedProperties.map((prop) => prop.id)]],
              "#4CAF50",
              "#03a9f4",
            ],
            "line-width": 5,
            "line-opacity": 0.7,
          },
          minzoom: PROPERTIES_MIN_ZOOM,
        },
        2: {
          id: "properties-glow-2",
          type: "line",
          source: "properties-source",
          "source-layer": "properties",
          paint: {
            "line-color": [
              "case",
              ["in", ["get", "id"], ["literal", claimedProperties.map((prop) => prop.id)]],
              "#4CAF50",
              "#03a9f4",
            ],
            "line-width": 12,
            "line-opacity": 0.5,
            "line-blur": 5,
          },
          minzoom: PROPERTIES_MIN_ZOOM,
        },
        3: {
          id: "properties-glow-3",
          type: "line",
          source: "properties-source",
          "source-layer": "properties",
          paint: {
            "line-color": [
              "case",
              ["in", ["get", "id"], ["literal", claimedProperties.map((prop) => prop.id)]],
              "#4CAF50",
              "#03a9f4",
            ],
            "line-width": 24,
            "line-opacity": 0.35,
            "line-blur": 13,
          },
          minzoom: PROPERTIES_MIN_ZOOM,
        },
      };
    },
    property_border_layer: (claimedProperties: ClaimedProperties): LineLayer => {
      return {
        id: "properties-border",
        type: "line",
        source: "properties-source",
        "source-layer": "properties",
        paint: {
          "line-color": [
            "case",
            ["in", ["get", "id"], ["literal", claimedProperties.map((prop) => prop.id)]],
            "#4CAF50",
            "#03a9f4",
          ],
          "line-width": 1,
        },
        minzoom: PROPERTIES_MIN_ZOOM,
      };
    },
  },
};
