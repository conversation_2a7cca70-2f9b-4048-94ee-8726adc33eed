export interface MovebankCredentials {
  username: string;
  password: string;
}

export interface MovebankStudy {
  id: number;
  name: string;
  taxon_ids?: number[];
  main_location_lat?: number;
  main_location_long?: number;
}

export interface MovebankIndividual {
  id: number;
  study_id: number;
  local_identifier?: string;
  taxon_canonical_name?: string;
}

export interface MovebankLocation {
  event_id: number;
  individual_id: number;
  study_id: number;
  timestamp: string;
  location_lat: number;
  location_long: number;
}

export interface MovebankPointProperties {
  id: number;
  individualId: number;
  studyId: number;
  timestamp: string;
  name: string;
  species: string;
}

export interface MovebankState {
  studies: MovebankStudy[];
  individuals: MovebankIndividual[];
  locations: MovebankLocation[];
  isLoading: boolean;
  error: Error | null;
}

export interface MovebankActions {
  refetchData: () => Promise<void>;
}

export type UseMovebankReturn = MovebankState & MovebankActions;
