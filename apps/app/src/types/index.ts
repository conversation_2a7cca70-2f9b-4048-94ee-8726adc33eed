import { ConnectedWallet, User } from "@privy-io/react-auth";
import { DialogProps } from "@radix-ui/react-dialog";
import { RefObject } from "react";
import { MapRef, ViewStateChangeEvent } from "react-map-gl";
import { PaddingOptions } from "react-map-gl/dist/esm/types";

export interface IAppHeaderStore {
  confirmLogout: boolean;
  toggleConfirmLogout: () => void;
}

export interface OpenMeteoResponse {
  current: {
    temperature_2m: number;
    time: string;
    weather_code: number;
    wind_speed_10m: number;
  };
  daily: {
    temperature_2m_max: number[];
    temperature_2m_min: number[];
    time: string[];
    weather_code: number[];
  };
}

export interface IWeatherStore {
  loadingWeather: boolean;
  weatherInfo: Partial<OpenMeteoResponse>;
  failedToFetchWeather: boolean;
  currentAddress: Partial<Locations>;
  currentTime: string;
  fetchWeatherInfo: () => void;
  setCurrentAddress: () => void;
  weatherIcon: () => string;
  temperature: () => string;
  updateTime: () => void;
}

export interface ICodeStore {
  code: string;
  failed: boolean;
  inviteCode: Partial<InviteCode> | null;
  clearCode: () => void;
  setFailed: (failed: boolean) => void;
  handleOTPChange: (e: string) => void;
  setInviteCode: (inviteCode: Partial<InviteCode> | null) => void;
}

export interface IbiConfirmDialogProps extends DialogProps {
  title: string;
  description: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => void;
}

export interface IPrivyAuthStore {
  user: Partial<User>;
  wallets: ConnectedWallet[];
  ready: boolean;
  isCodeValid: boolean;
  updateUser: (user: Partial<User>) => void;
  updateWallets: (wallets: ConnectedWallet[]) => void;
  updateIsCodeValid: (isCodeValid: boolean) => void;
  updateReady: (ready: boolean) => void;
}

export interface PrivyAuthContextProps {
  user?: User | null;
  wallets?: ConnectedWallet[] | null;
  ready?: boolean;
  authenticated?: boolean;
  onLogin: () => void;
  onLogout: () => void;
  confirmLogout: (direct?: boolean) => void;
  onCreateWallet: () => Promise<void>;
  getJWT: () => Promise<string | null>;
}

export type TFallbackProps = {
  condition: boolean;
  fallback: React.ReactNode;
  children: React.ReactNode;
};

export interface Welcome {
  timelines: Timelines;
  location: Location;
}

export interface Location {
  lat: number;
  lon: number;
}

export interface Timelines {
  minutely: Hourly[];
  hourly: Hourly[];
  daily: Daily[];
}

export interface Daily {
  time: Date;
  values: Values;
}

export interface Hourly {
  time: Date;
  values: Values;
}

export interface Values {
  cloudBase: number | null;
  cloudCeiling: number | null;
  cloudCover: number;
  dewPoint: number;
  evapotranspiration: number;
  freezingRainIntensity: number;
  humidity: number;
  iceAccumulation: number;
  iceAccumulationLwe: number;
  precipitationProbability: number;
  pressureSurfaceLevel: number;
  rainAccumulation: number;
  rainAccumulationLwe: number;
  rainIntensity: number;
  sleetAccumulation: number;
  sleetAccumulationLwe: number;
  sleetIntensity: number;
  snowAccumulation: number;
  snowAccumulationLwe: number;
  snowDepth: number;
  snowIntensity: number;
  temperature: number;
  temperatureApparent: number;
  uvHealthConcern: number;
  uvIndex: number;
  visibility: number;
  weatherCode: number;
  windDirection: number;
  windGust: number;
  windSpeed: number;
}

export interface Locations {
  data: Partial<LocationData[]>;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  type: string;
  distance: number;
  name: string;
  number: string;
  postal_code: string;
  street: string;
  confidence: number;
  region: string;
  region_code: string;
  county: string;
  locality: string;
  administrative_area: string | null;
  neighbourhood: string;
  country: string;
  country_code: string;
  continent: string;
  label: string;
}

export interface IValidateCodeResponse {
  message: string;
  error?: string;
  statusCode?: number;
  success?: boolean;
}
export interface IbiCardProps {
  children: React.ReactNode;
  pathVariant: "first-card-path" | "second-card-path" | "third-card-path" | "fourth-card-path";
  headerTitle: string;
  headerSubtitle?: string | React.ReactNode;
  subHeaderTitle?: string;
  subHeaderSubtitle?: string;
  subHeaderContent?: string | React.ReactNode;
  subHeaderSubtitleContent?: string | React.ReactNode;
  headerIcon?: string;
  className?: string;
}

export interface InviteCodeUse {
  id: string;
  usedAt: string;
  usedBy: string;
  userWallet: string;
}

export interface InviteCode {
  id: number;
  code: string;
  createdAt: string;
  uses: InviteCodeUse[];
  totalUses: number;
}

export interface Territory {
  id: string;
  name: string;
  type: "country" | "state";
  country: string | null;
}

interface ForestEcoregion {
  id: string;
  name: string;
  forestArea: number;
}

interface ForestHistoryEntry {
  year: number;
  ecoregions?: Array<{
    ecoregionName: string;
    areaHa: number;
  }>;
  areaHa?: string | number;
}

export interface ForestHistoryData {
  id: string;
  externalId?: string;
  name?: string;
  type?: string;
  adm0Id?: string;
  adm1Id?: string | null;
  parentId?: string | null;
  areaHa?: number;
  createdAt?: string;
  updatedAt?: string;
  ecoregions?: ForestEcoregion[];
  forestHistory: ForestHistoryEntry[];
  isPropertyData?: boolean;
}

export interface BiodiversityTerritoryCount {
  amphibians: number;
  birds: number;
  mammals: number;
  plants: number;
  reptiles: number;
}

export interface BiodiversityTerritory {
  counts: BiodiversityTerritoryCount;
  maxValues: BiodiversityTerritoryCount;
  territoryId: string;
  territoryType: string;
}

export interface CircularChartProps {
  data: ForestHistoryData | null;
  isLoading: boolean;
  territoryName?: string;
}

export interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: {
      year: number;
      areaHa: number;
    };
  }>;
}

export interface BaseMapStore {
  lat: number;
  lng: number;
  zoom: number;
  bearing: number;
  pitch: number;
  bounds: mapboxgl.LngLatBoundsLike;
  padding: PaddingOptions;
}

export interface BreadcrumbItem {
  id: string;
  name: string;
  type: "country" | "state" | "property";
  feature?: any;
  country?: string | null;
}

export interface IMapStore extends BaseMapStore {
  selectedTerritory: Territory | null;
  selectedPropertyDetails: PropertyDetails | null;
  forestHistory: ForestHistoryData | null;
  isLoadingForestHistory: boolean;
  forestHistoryError: string | null;
  isTransitioning: boolean;
  breadcrumbs: BreadcrumbItem[];
  setIsTransitioning: (value: boolean) => void;
  setSelectedTerritory: (territory: Territory | null) => void;
  setSelectedPropertyDetails: (details: PropertyDetails | null) => void;
  setForestHistory: (data: ForestHistoryData | null) => void;
  setIsLoadingForestHistory: (isLoading: boolean) => void;
  setForestHistoryError: (error: string | null) => void;
  setZoom: (zoom: number) => void;
  setPadding: (padding: PaddingOptions) => void;
  updateLocation(e: ViewStateChangeEvent): void;
  setBreadcrumbs: (breadcrumbs: BreadcrumbItem[] | ((prev: BreadcrumbItem[]) => BreadcrumbItem[])) => void;
}

export interface RadialBarProps {
  data: ForestHistoryData | null;
  isLoading: boolean;
  territoryName: string | undefined;
}

export type CurrentLayers =
  | "default"
  | "wind"
  | "forest"
  | "nasa"
  | "alerts"
  | "movebank"
  | "precipitation"
  | "clouds"
  | "temperature"
  | "pressure";

export type CurrentBaseMap =
  | "default"
  | "dark"
  | "biome"
  | "streets"
  | "outdoors"
  | "light"
  | "navigation-day"
  | "navigation-night";

export interface TomorrowLayerSettings {
  windSpeed?: {
    opacity: number;
    visible: boolean;
  };
  precipitation?: {
    opacity: number;
    visible: boolean;
  };
  temperature?: {
    opacity: number;
    visible: boolean;
  };
}

export interface BiomeData {
  id: string;
  name: string;
  area?: number;
  properties?: Record<string, any>;
}

export interface EcoregionData {
  id: string;
  name: string;
  biomeId?: string;
  area?: number;
  properties?: Record<string, any>;
}

export interface VoronoiData {
  id: string;
  name: string;
  properties?: Record<string, any>;
}

export interface BiomeStore {
  selectedBiome: BiomeData | null;
  selectedEcoregion: EcoregionData | null;
  selectedVoronoi: VoronoiData | null;

  setSelectedBiome: (biome: BiomeData | null) => void;
  setSelectedEcoregion: (ecoregion: EcoregionData | null) => void;
  setSelectedVoronoi: (voronoi: VoronoiData | null) => void;
}

export interface MapContextProps {
  mapRef: RefObject<mapboxgl.Map | null> | null;
  minimapRef: React.RefObject<MapRef> | null;
  mapLoaded: boolean;
  currentLayers: CurrentLayers[];
  currentBaseMap: CurrentBaseMap;
  showProperties: boolean;
  showRestorations: boolean;
  setShowProperties: (showProperties: boolean) => void;
  setShowRestorations: (showRestorations: boolean) => void;
  setMapLoaded: (mapLoaded: boolean) => void;
  setCurrentLayers: (layers: CurrentLayers[] | ((prev: CurrentLayers[]) => CurrentLayers[])) => void;
  setCurrentBaseMap: (currentBaseMap: CurrentBaseMap) => void;
}

export interface UserWallet {
  address: string;
  verifiedAt: string;
  firstVerifiedAt: string;
  latestVerifiedAt: string;
  chainType: string;
  chainId: string;
  walletClientType: string;
  connectorType: string;
  hdWalletIndex: number;
  imported: boolean;
  formattedAddress: string;
}

export interface UserData {}

export interface IUserStore {
  userData: Partial<UserData>;
}

export interface CurrencyData {
  code: string;
  name: string;
  icon: string;
}

export interface MemeCoin {
  name: string;
  price: number;
  change: number;
  image: string;
}

export interface StakingToken {
  name: string;
  price: number;
  change: number;
  image: string;
}

export interface CryptoIndex {
  name: string;
  price: number;
  change: number;
  verified: boolean;
  image: string;
}

export interface DefiToken {
  name: string;
  price: number;
  change: number;
  verified: boolean;
  image: string;
}

export interface NFT {
  name: string;
  price: number;
  volume: string;
  volumeChange: number;
  image: string;
}

export interface DashboardState {
  searchQuery: string;
  currency: string;

  currencyData: CurrencyData[];
  memeCoins: MemeCoin[];
  stakingTokens: StakingToken[];
  cryptoIndexes: CryptoIndex[];
  defiBlueChips: DefiToken[];
  trendingNFTs: NFT[];

  filteredMemeCoins: MemeCoin[];
  filteredStakingTokens: StakingToken[];
  filteredCryptoIndexes: CryptoIndex[];
  filteredDefiTokens: DefiToken[];
  filteredNFTs: NFT[];

  setSearchQuery: (query: string) => void;
  setCurrency: (currency: string) => void;
  formatPrice: (price: number, currency?: string) => string;
  initialize: () => void;
}

export interface PropertyClaim {
  id: string;
  propertyId: string;
  userId: string;
  status: "APPROVED" | "UNVERIFIED" | "VERIFIED" | "REJECTED";
  user: {
    authId: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PropertyDetails {
  id: string;
  name: string | null;
  propertyId: number;
  areaHa: string;
  ibiCode: string;
  propertyEcoregions: {
    forestArea: number;
    ecoregion: {
      name: string;
      code: string;
      biomeName: string;
    };
  }[];
  claims?: PropertyClaim[];
  totalForestArea: number;
  state?: string;
  country?: string;
}

export interface PopupInfo {
  lngLat: { lng: number; lat: number };
  propertyDetails: PropertyDetails | null;
  isLoading: boolean;
  error?: string;
  propertyId: number | null;
}

export interface CachedProperty {
  details: PropertyDetails;
  timestamp: number;
}

export interface IPointsResponse {
  address: string;
  amount: number;
}

export interface LeaderboardResponse {
  data: LeaderboardItem[];
  meta: {
    page: number;
    take: number;
    itemCount: number;
    pageCount: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
  };
}

export interface LeaderboardItem {
  address: string;
  points: number;
}

export interface IpApiResponse {
  lat: number;
  lon: number;
  status: string;
}

export interface MapLayerResponse {
  url: string;
}

export type ClaimedPropertyItem = {
  id: string;
  ibiCode: string;
  claims: {
    status: "APPROVED" | "PENDING" | "REJECTED";
  }[];
};

export type ClaimedProperties = ClaimedPropertyItem[];

export interface ForestHistory {
  id: string;
  propertyId: string;
  year: number;
  areaHa: number;
  createdAt: string;
  updatedAt: string;
  property: string;
}

export interface Ecoregion {
  id: string;
  name: string;
  code: string;
  areaHa: number;
  biomeId: string;
  biomeName: string;
  realm: string;
  geometry: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface PropertyEcoregion {
  propertyId: number;
  ecoregionId: string;
  forestArea: number;
  createdAt: string;
  updatedAt: string;
  ecoregion: Ecoregion;
  property: string;
}

export interface Property {
  id: string;
  name: string;
  areaHa: number;
  geometry: Record<string, any>;
  ibiCode: string;
  createdAt: string;
  updatedAt: string;
  propertyEcoregions: PropertyEcoregion[];
  forestHistory: ForestHistory[];
}

export interface ReferralFromCollection {
  id: number;
  code: string;
  createdBy: string;
  usedBy: string;
  isUsed: boolean;
  createdAt: string;
  updatedAt: string;
  createdByUser: string;
  usedByUser: string;
}

export interface UserFromCollection {
  id: string;
  authId: string;
  isBanned: boolean;
  createdAt: string;
  updatedAt: string;
  referrals: ReferralFromCollection[];
  collections: string[];
}

export interface Collection {
  id: string;
  name: string;
  description: string;
  userId: string;
  isPrivate: boolean;
  createdAt: string;
  user: UserFromCollection;
  properties: Property[];
}

export interface NewCollection {
  name: string;
  description: string;
  isPrivate: boolean;
}

export interface Claims {
  data: Array<{
    id: string;
    propertyId: string;
    userId: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    property: {
      id: string;
      name: string | null;
      areaHa: number;
      geometry: Record<string, any>;
      ibiCode: string;
      createdAt: string;
      updatedAt: string;
      propertyEcoregions: Array<{
        propertyId: number;
        ecoregionId: string;
        forestArea: number;
        createdAt: string;
        updatedAt: string;
        ecoregion: {
          id: string;
          name: string;
          code: string;
          areaHa: number;
          biomeId: string;
          biomeName: string;
          realm: string;
          geometry: Record<string, any>;
          createdAt: string;
          updatedAt: string;
        };
      }>;
    };
    documents: Array<{
      claimId: string;
      documentId: string;
      document: {
        id: string;
        name: string;
        url: string;
        mimeType: string;
        size: number;
        createdAt: string;
        uploadedBy: string;
      };
    }>;
  }>;
  meta: {
    page: number;
    take: number;
    itemCount: number;
    pageCount: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
  };
}

export interface NewClaim {
  files: FormData;
}

export interface Document {
  id: string;
  name: string;
  url: string;
  mimeType: string;
  size: number;
  status: "UPLOADED" | string;
  error: string | null;
  createdAt: string;
  updatedAt: string;
  uploadedBy: string;
}

export interface ClaimDocument {
  claimId: string;
  documentId: string;
  document: Document;
}

export interface Claim {
  id: string;
  propertyId: string;
  userId: string;
  status: "PRE_CLAIM" | "UNVERIFIED" | "VERIFIED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  documents: ClaimDocument[];
}

export interface HydroshedLevel {
  id: string;
  label: string;
}

export interface ForestHistoryHydroshedEntry {
  year: number;
  ecoregions: Array<{
    ecoregionName: string;
    areaHa: number;
  }>;
}

export interface EcoregionHydroshed {
  id: string;
  name: string;
  forestArea: number;
}

export interface ForestHistoryHydroshed {
  id: string;
  hybasId: number;
  areaHa: number;
  createdAt: string;
  updatedAt: string;
  ecoregions: EcoregionHydroshed[];
  forestHistory: ForestHistoryHydroshedEntry[];
}

export interface ForestHistorySoilsEntry {
  year: number;
  ecoregions: Array<{
    ecoregionName: string;
    areaHa: number;
  }>;
}

export interface EcoregionSoils {
  id: string;
  name: string;
  forestArea: number;
}

export interface ForestHistorySoils {
  id: string;
  soilsId: number;
  areaHa: number;
  createdAt: string;
  updatedAt: string;
  ecoregions: EcoregionSoils[];
  forestHistory: ForestHistorySoilsEntry[];
}
