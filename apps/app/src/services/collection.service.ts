import { api } from "@/lib/axios";
import { type Collection, type NewCollection } from "@/types";

export const collectionService = {
  getCollections: () => api.get<Collection[]>("/api/collections"),
  createCollection: (newCollection: NewCollection) => api.post<Collection>("/api/collections", newCollection),
  getCollectionByID: (id: string) => api.get<Collection>(`/api/collections/${id}`),
  postBookmark: (id: string, propertyId: string) =>
    api.post<Collection>(`/api/collections/${id}/bookmark`, { propertyId }),
  unpostBookmark: (id: string, propertyId: string) =>
    api.post<Collection>(`/api/collections/${id}/unbookmark`, { propertyId }),

  findOrCreateGeneralCollection: async () => {
    const response = await api.get<Collection[]>("/api/collections");
    let generalCollection = response.data.find((c) => c.name === "General");

    if (!generalCollection) {
      const newGeneral = await api.post<Collection>("/api/collections", {
        name: "General",
        description: "Default collection for bookmarks",
        isPrivate: false,
      });
      generalCollection = newGeneral.data;
    }

    return generalCollection;
  },
} as const;
