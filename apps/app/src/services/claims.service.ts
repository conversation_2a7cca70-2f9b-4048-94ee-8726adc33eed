import { api } from "@/lib/axios";
import { Claims } from "@/types";

export const claimService = {
  getClaims: (page: number = 1) => api.get<Claims>(`/api/claims?page=${page}`),
  requestOwnership: async (formData: FormData) => {
    return api.post<FormData>("/api/claims", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  challengeClaim: async (formData: FormData) => {
    return api.post<FormData>("/api/claims/challenge", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
} as const;
