import { api } from "@/lib/axios";
import { IpApiResponse, OpenMeteoResponse } from "@/types";

export const weatherService = {
  getLocationFromIP: () => api.get<IpApiResponse>("http://ip-api.com/json/"),

  getWeatherInfo: (lat: number, lng: number) =>
    api.get<OpenMeteoResponse>(
      `https://api.open-meteo.com/v1/forecast?latitude=${lat}&longitude=${lng}&current=temperature_2m,weather_code,wind_speed_10m&daily=temperature_2m_max,temperature_2m_min,weather_code&timezone=auto`,
    ),
} as const;
