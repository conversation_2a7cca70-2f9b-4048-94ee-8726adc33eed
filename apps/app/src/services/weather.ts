import { IpApiResponse } from "@/types";
import { makeRequest } from "@/utils/make-request";

interface OpenMeteoResponse {
  current: {
    temperature_2m: number;
    time: string;
    weather_code: number;
    wind_speed_10m: number;
  };
  daily: {
    temperature_2m_max: number[];
    temperature_2m_min: number[];
    time: string[];
    weather_code: number[];
  };
}

async function getLocationFromIP(): Promise<{ lat: number; lng: number } | undefined> {
  try {
    const { result, error } = await makeRequest<IpApiResponse>({ method: "GET" }, undefined, "http://ip-api.com/json/");

    if (error || !result || result.status !== "success") {
      return undefined;
    }

    return { lat: result.lat || 0, lng: result.lon || 0 };
  } catch (error) {
    console.error("Error getting location from IP:", error);
    return undefined;
  }
}

async function getBrowserLocation(): Promise<{ lat: number; lng: number } | undefined> {
  try {
    const position = await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        timeout: 5000,
        maximumAge: 0,
      });
    });

    return {
      lat: position.coords.latitude,
      lng: position.coords.longitude,
    };
  } catch (error) {
    console.error("Browser geolocation failed:", error);
    return undefined;
  }
}

export async function fetchWeatherInfo(): Promise<OpenMeteoResponse | undefined> {
  try {
    let location = await getBrowserLocation();

    if (!location) {
      console.log("Browser geolocation failed, trying IP-based location...");
      location = await getLocationFromIP();
    }

    if (!location) {
      console.error("Could not determine location through any method");
      return undefined;
    }

    const { result, error } = await makeRequest<OpenMeteoResponse>(
      {
        method: "GET",
      },
      undefined,
      `https://api.open-meteo.com/v1/forecast?latitude=${location.lat}&longitude=${location.lng}&current=temperature_2m,weather_code,wind_speed_10m&daily=temperature_2m_max,temperature_2m_min,weather_code&timezone=auto`,
    );

    if (error || !result) {
      return undefined;
    }

    return result as OpenMeteoResponse;
  } catch (error) {
    console.error("Error in fetchWeatherInfo:", error);
    return undefined;
  }
}
