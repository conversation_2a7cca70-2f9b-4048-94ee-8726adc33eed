import { api } from "@/lib/axios";
import { IPointsResponse, LeaderboardResponse } from "@/types";

export const pointsService = {
  trackPoints: () => api.post("/api/points/track"),

  getLeaderboard: (page = 1, take = 10) =>
    api.get<LeaderboardResponse>(`/api/points/leaderboard?page=${page}&take=${take}`).then((response) => response.data),

  getPointsEvents: (account: string) => api.get<IPointsResponse>(`/api/points?account=${account}`),

  trackPointEvent: (event: { event: string; points: number }) => api.post("/api/points/track", event),
} as const;
