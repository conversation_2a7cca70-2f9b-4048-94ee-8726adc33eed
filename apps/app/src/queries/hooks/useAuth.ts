import { authService } from "@/services/auth.service";
import { useQuery } from "@tanstack/react-query";

/**
 * Hook for fetching authentication-related data using React Query
 * @returns Object containing the referral code query
 */
export function useAuth() {
  const referralCode = useQuery({
    queryKey: ["referralCode"],
    queryFn: () => authService.getReferralCode(),
    enabled: !!localStorage.getItem("privy:token"),
  });

  return {
    referralCode,
  };
}
