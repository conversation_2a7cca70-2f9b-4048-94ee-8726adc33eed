import { weatherService } from "@/services/weather.service";
import { useQuery } from "@tanstack/react-query";

export function useWeather(lat?: number, lng?: number) {
  const location = useQuery({
    queryKey: ["ipLocation"],
    queryFn: () => weatherService.getLocationFromIP(),
  });

  const weather = useQuery({
    queryKey: ["weather", lat, lng],
    queryFn: () => weatherService.getWeatherInfo(lat!, lng!),
    enabled: !!lat && !!lng,
  });

  return {
    location,
    weather,
  };
}
