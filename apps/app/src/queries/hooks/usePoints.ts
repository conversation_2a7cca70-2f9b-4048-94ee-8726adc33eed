import { pointsService } from "@/services/points.service";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";

export function usePoints(account?: string) {
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  const leaderboard = useQuery({
    queryKey: ["pointsLeaderboard", page],
    queryFn: () => pointsService.getLeaderboard(page, ITEMS_PER_PAGE),
  });

  const pointsEvents = useQuery({
    queryKey: ["pointsEvents", account],
    queryFn: () => pointsService.getPointsEvents(account!),
    enabled: !!account,
  });

  const handleNextPage = () => {
    if (leaderboard.data?.meta.hasNextPage) {
      setPage((prev) => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (leaderboard.data?.meta.hasPreviousPage) {
      setPage((prev) => prev - 1);
    }
  };

  return {
    leaderboard: {
      ...leaderboard,
      currentPage: page,
      handleNextPage,
      handlePreviousPage,
    },
    pointsEvents,
  };
}
