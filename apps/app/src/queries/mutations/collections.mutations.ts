import { collectionService } from "@/services/collection.service";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useCollectionsMutations = () => {
  const queryClient = useQueryClient();

  return {
    createCollection: useMutation({
      mutationFn: collectionService.createCollection,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["collections"] });
      },
    }),

    bookmarkProperty: useMutation({
      mutationFn: ({ id, propertyId }: { id: string; propertyId: string }) =>
        collectionService.postBookmark(id, propertyId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["collections"] });
      },
    }),

    unbookmarkProperty: useMutation({
      mutationFn: ({ id, propertyId }: { id: string; propertyId: string }) =>
        collectionService.unpostBookmark(id, propertyId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["collections"] });
      },
    }),
  };
};
