import { territoriesService } from "@/services/territories.service";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useTerritoriesMutation = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => territoriesService.claimProperty(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["claimedProperties"] });
    },
  });
};
