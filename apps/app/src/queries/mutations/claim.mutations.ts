import { claimService } from "@/services/claims.service";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useClaimsMutations = () => {
  const queryClient = useQueryClient();

  return {
    requestOwnership: useMutation({
      mutationFn: claimService.requestOwnership,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["claims"] });
      },
    }),
    challengeClaim: useMutation({
      mutationFn: claimService.challengeClaim,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["claims"] });
      },
    }),
  };
};
