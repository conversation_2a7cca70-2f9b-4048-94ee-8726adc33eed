import AppWrapper from "@/components/AppWrapper";
import PWAUpdatePrompt from "@/components/PWAUpdatePrompt";
import ServiceWorkerRegistration from "@/components/ServiceWorkerRegistration";
import { Toaster } from "@/components/ui/toaster";
import Providers from "@/contexts/Providers";
import "animate.css";
import "mapbox-gl/dist/mapbox-gl.css";
// import type { Metadata } from "next";
import NextTopLoader from "nextjs-toploader";

import { nextLoaderConfig } from "../utils/configs";
import "./globals.css";

export const viewport = {
  themeColor: "#070F11",
  viewport: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
};

export const metadata = {
  title: "IbiCash",
  manifest: "/public/manifest.json",
  themeColor: "#070F11",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "IbiCash",
    startupImage: [
      {
        url: "/icons/apple-splash-2048-2732.png",
        media: "(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)",
      },
    ],
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="theme-color" content="#070F11" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-touch-fullscreen" content="yes" />
        <link rel="apple-touch-icon" href="https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581" />
        <link rel="manifest" href="/public/manifest.json" />
        <link rel="stylesheet" href="/apple-splash-screens.css" />
      </head>
      <body className="bg-primary-dark text-white overflow-y-auto overflow-x-hidden h-dvh w-full">
        <NextTopLoader {...nextLoaderConfig} />
        <Toaster />
        <PWAUpdatePrompt />
        <ServiceWorkerRegistration />
        <Providers>
          <AppWrapper>{children}</AppWrapper>
        </Providers>
      </body>
    </html>
  );
}
