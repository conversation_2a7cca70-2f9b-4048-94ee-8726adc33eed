import ReferralRedirectArea from "@/components/ReferralRedirectArea";
import { Metadata, ResolvingMetadata } from "next";

type Props = {
  params: { ibicode: string };
};

export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
  await parent;
  const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000";
  // let coordinates = [-55.0, -10.0]; // Brazil's center coordinates
  let geojsonData: any = null;

  try {
    const response = await fetch(`${baseUrl}/api/map-info/${params.ibicode}`, {
      cache: "no-store",
    });

    if (!response.ok) {
      console.log("Error fetching coordinates:", response.statusText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    // coordinates = data.coordinates.coordinates;
    geojsonData = data.geojson;
    console.log(data);
  } catch (error) {
    console.log("Error in generateMetadata:", error);
  }

  const getMapboxImageUrl = () => {
    if (!geojsonData) return null;
    // Encode the GeoJSON object properly for the URL
    const encodedGeojson = encodeURIComponent(JSON.stringify(geojsonData));
    console.log(
      `https://api.mapbox.com/styles/v1/geodatin/clawpmxqa000014mrgrn39mtd/static/geojson(${encodedGeojson})/auto/600x400?access_token=${process.env.MAPBOX_TOKEN}`,
    );
    return `https://api.mapbox.com/styles/v1/geodatin/clawpmxqa000014mrgrn39mtd/static/geojson(${encodedGeojson})/auto/600x400?access_token=${process.env.MAPBOX_TOKEN}`;
  };

  const mapboxImageUrl = getMapboxImageUrl();

  const metadata: Metadata = {
    metadataBase: new URL("https://yby.energy"),
    title: "YBYENERGY - Harnessing Forest Energy",
    description:
      "ybyCash is a protocol that mimics nature on-chain by issuing tokens daily based on forest area calculations across 846 terrestrial ecoregions, creating a neutral and dynamic governance system.",
    openGraph: {
      title: "YBYENERGY - Harnessing Forest Energy",
      description:
        "ybyCash is a protocol that mimics nature on-chain by issuing tokens daily based on forest area calculations across 846 terrestrial ecoregions, creating a neutral and dynamic governance system.",
      ...(mapboxImageUrl && {
        images: [{ url: mapboxImageUrl }],
      }),
    },
    twitter: {
      card: "summary_large_image",
      title: "YBYENERGY - Harnessing Forest Energy",
      description:
        "ybyCash is a protocol that mimics nature on-chain by issuing tokens daily based on forest area calculations across 846 terrestrial ecoregions, creating a neutral and dynamic governance system.",
      ...(mapboxImageUrl && {
        images: [mapboxImageUrl],
      }),
    },
  };

  return metadata;
}

export default function IbiCodePage() {
  return <ReferralRedirectArea />;
}
