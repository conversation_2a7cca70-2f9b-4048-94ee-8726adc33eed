"use client";

export default function OfflinePage() {
  return (
    <div className="h-screen w-screen flex flex-col items-center justify-center p-4">
      <h1 className="text-2xl font-bold mb-4">You&apos;re offline</h1>
      <p className="text-center mb-6">Please check your internet connection and try again.</p>
      <button
        onClick={() => window.location.reload()}
        className="bg-[#ffffff0d] hover:bg-[#ffffff1a] px-6 py-2 rounded-lg transition-colors"
      >
        Retry
      </button>
    </div>
  );
}
