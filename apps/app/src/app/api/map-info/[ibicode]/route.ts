import { NextResponse } from "next/server";

export async function GET(request: Request, { params }: { params: { ibicode: string } }) {
  try {
    const { searchParams } = new URL(request.url);
    const isProperty = searchParams.get("isProperty") === "true";
    const paramName = isProperty ? "id" : "ibiCode";
    const paramValue = params.ibicode;

    const coordinatesRes = await fetch(`https://api.yby.energy/api/properties/coordinates?${paramName}=${paramValue}`);
    const geojsonRes = await fetch(`https://api.yby.energy/api/properties/geometry?${paramName}=${paramValue}`);

    if (!coordinatesRes.ok || !geojsonRes.ok) {
      console.log(coordinatesRes, geojsonRes);
      throw new Error(`API responded with status: ${coordinatesRes.status} and ${geojsonRes.status}`);
    }

    const coordinates = await coordinatesRes.json();
    const geojson = await geojsonRes.json();

    console.log(coordinates, geojson);
    return NextResponse.json({ coordinates, geojson }, { status: 200 });
  } catch (error) {
    console.error("Error fetching data", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
