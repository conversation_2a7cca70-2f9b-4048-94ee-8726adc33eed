"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { ChevronDown, Grid, Plus, Search, Send, Share2 } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { Line, LineChart, YAxis } from "recharts";

const CryptoWallet = () => {
  const { user, wallets } = usePrivyAuth();
  const [activeTab, setActiveTab] = useState("Tokens");
  const [chartWidth, setChartWidth] = useState(0);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const width = entries[0].contentRect.width;
      setChartWidth(width);
    });

    if (chartContainerRef.current) {
      observer.observe(chartContainerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const chartData = Array.from({ length: 100 }, () => ({
    value: 8000000 + Math.random() * 400000,
  }));

  const transactions = [
    { type: "Receive", date: "Feb 11", amount: "+10 USDC", icon: "🔵" },
    { type: "Receive", date: "Feb 11", amount: "+0.0001 ETH", icon: "⚡" },
    { type: "Receive", date: "Feb 11", amount: "+0.0001 ETH", icon: "⚡" },
    { type: "Receive", date: "Feb 11", amount: "+10 LOVE", icon: "💜" },
    { type: "Receive", date: "Feb 11", amount: "+0.0001 ETH", icon: "⚡" },
  ];

  const getUserDisplayName = () => {
    if (user?.email?.address) return user.email.address;
    if (user?.wallet?.address) return `${user.wallet.address.slice(0, 6)}...${user.wallet.address.slice(-4)}`;
    return "Anonymous User";
  };

  return (
    <div className="text-white w-full h-screen overflow-y-auto">
      <div className="text-white w-full">
        <div className="flex items-center mb-8 bg-[#01050dD9] py-6">
          <div className="w-full max-w-6xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="p-2 bg-slate-800/50 rounded-lg">
                <IbiIcon icon="si:dashboard-vert-line" className="text-xl text-emerald-400" />
              </div>
              <h1 className="text-xl sm:text-2xl font-bold">Overview</h1>
            </div>
            <div className="flex flex-1 max-w-[600px]">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Asset, wallet, domain or identity"
                  className="w-full bg-gray-800 rounded-lg py-2 px-4 text-gray-300"
                />
                <Search className="absolute right-3 top-2.5 text-gray-500" size={20} />
              </div>
              <div className="flex items-center gap-4 ml-4">
                <button className="flex items-center gap-1 bg-gray-800 px-3 py-1 rounded-lg">
                  1 <ChevronDown size={16} />
                </button>
                <button className="flex items-center gap-1 bg-gray-800 px-3 py-1 rounded-lg">
                  USD <ChevronDown size={16} />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto pb-6">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-600 rounded-lg"></div>
              <div>
                <div className="flex items-center gap-2">
                  <span>{getUserDisplayName()}</span>
                  <ChevronDown size={16} />
                </div>
                <div className="text-2xl font-bold">
                  {wallets && wallets.length > 0
                    ? `${wallets[0].address.slice(0, 6)}...${wallets[0].address.slice(-4)}`
                    : "No wallet connected"}
                </div>
                <div className="text-green-500">Connected since {user?.createdAt.toLocaleDateString()}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button className="p-2 bg-gray-800 rounded-lg">
                <Share2 size={20} />
              </button>
              <button className="p-2 bg-gray-800 rounded-lg">
                <Send size={20} />
              </button>
              <button className="px-4 py-2 bg-gray-800 rounded-lg flex items-center gap-2">
                Add wallet
                <Plus size={16} />
              </button>
            </div>
          </div>

          <div className="flex items-center gap-6 border-b border-gray-800 mb-6">
            {["Tokens", "NFTs", "History"].map((tab) => (
              <button
                key={tab}
                className={`pb-2 px-1 ${
                  activeTab === tab ? "border-b-2 border-blue-500 text-blue-500" : "text-gray-400"
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </button>
            ))}
            <div className="flex-1"></div>
            <button className="flex items-center gap-2 bg-gray-800 px-3 py-1 rounded-lg">
              <Grid size={16} />
              All Networks
              <ChevronDown size={16} />
            </button>
          </div>

          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl">Performance</h2>
              <div className="flex gap-2">
                {["1H", "1D", "1W", "1M", "1Y", "Max"].map((period) => (
                  <button
                    key={period}
                    className={`px-3 py-1 rounded-lg ${period === "1D" ? "bg-green-500" : "bg-gray-800"}`}
                  >
                    {period}
                  </button>
                ))}
              </div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-2xl font-bold mb-2">$8,206,166</div>
              <div className="text-green-500 mb-4">+1.5% ($113,905.02)</div>
              <div className="h-48" ref={chartContainerRef}>
                <LineChart width={chartWidth || 0} height={200} data={chartData}>
                  <Line type="monotone" dataKey="value" stroke="#22c55e" dot={false} strokeWidth={2} />
                  <YAxis domain={["dataMin - 100000", "dataMax + 100000"]} hide />
                </LineChart>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl mb-4">History</h2>
            <div className="bg-gray-800 rounded-lg p-4">
              {transactions.map((tx, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between py-2 border-b border-gray-700 last:border-0"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">{tx.icon}</div>
                    <div>
                      <div>{tx.type}</div>
                      <div className="text-sm text-gray-400">{tx.date}</div>
                    </div>
                  </div>
                  <div>{tx.amount}</div>
                </div>
              ))}
              <button className="w-full mt-4 py-2 text-center bg-gray-700 rounded-lg">See all</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CryptoWallet;
