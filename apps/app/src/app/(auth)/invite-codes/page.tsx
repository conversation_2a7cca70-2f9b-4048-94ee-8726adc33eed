"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import LeaderBoard from "@/components/Leaderboard";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import useCode from "@/hooks/useCode";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { useToast } from "@/hooks/useToast";
import { usePoints } from "@/queries/hooks/usePoints";
import { InviteCode, InviteCodeUse } from "@/types";
import { copyToClipboard } from "@/utils";
import { shortenText } from "@/utils/formatting";
import { motion } from "framer-motion";
import Link from "next/link";
import React from "react";

const seasons = [
  { id: "I", status: "complete", date: "Ended: May 28th" },
  { id: "II", status: "complete", date: "Started: July 8th" },
  { id: "III", status: "live", date: "Live now" },
];

const levels = [
  { points: "1K", level: 1 },
  { points: "10K", level: 2 },
  { points: "50K", level: 3 },
  { points: "100K", level: 4 },
  { points: "500K", level: 5 },
];

const ShareButtons = ({ inviteCode }: { inviteCode: Partial<InviteCode> | null }) => {
  const { toast } = useToast();

  if (!inviteCode?.code) {
    return null;
  }

  const copyReferralLink = (code: string | undefined) => {
    if (!code) return;

    const referralLink = `${window.location.origin}?referral=${code}`;
    copyToClipboard(referralLink);
    toast({
      title: "Referral link copied to clipboard",
      description: "Share this link with friends to invite them to the platform",
    });
  };

  return (
    <div className="flex gap-2 mb-8">
      {inviteCode.code.split("").map((c, i) => {
        return (
          <button
            key={i}
            className="px-3 py-1.5 bg-[#0a0e15] border border-gray-800/50 rounded-md text-gray-400 hover:text-white transition-colors"
          >
            {c}
          </button>
        );
      })}

      <button
        className="w-9 flex items-center justify-center bg-[#0a0e15] border border-gray-800/50 rounded-md text-gray-400 hover:text-white transition-colors"
        onClick={() => copyReferralLink(inviteCode.code)}
        title="Copy referral link"
      >
        <IbiIcon icon="ph:link-simple" className="text-sm" />
      </button>
    </div>
  );
};

const InviteCodePage = () => {
  const { toast } = useToast();
  const { inviteCode } = useCode();
  const { user } = usePrivyAuth();
  const { pointsEvents } = usePoints(user?.wallet?.address);

  const copyReferralLink = (code: string | undefined) => {
    if (!code) return;

    const referralLink = `${window.location.origin}?referral=${code}`;
    copyToClipboard(referralLink);
    toast({
      title: "Referral link copied to clipboard",
      description: "Share this link with friends to invite them to the platform",
    });
  };

  const points = pointsEvents?.data?.data?.[0]?.amount ?? 0;

  return (
    <div className="h-screen overflow-y-auto">
      <header className="h-auto min-h-[108px] bg-[#01050dD9] backdrop-blur-md py-4 sticky top-0 z-10 max-[750px]:pt-12">
        <div className="max-w-6xl mx-auto flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-4">
          <div className="flex items-center gap-4">
            <Link href="/dashboard" className="flex items-center gap-2">
              <IbiIcon icon="mdi:ticket-confirmation" className="text-xl text-white" />
            </Link>
            <h1 className="text-xl font-mono text-white">{shortenText(user?.wallet?.address as string, 12)}</h1>
          </div>

          <div className="flex items-center gap-4 sm:gap-6 w-full sm:w-auto justify-between sm:justify-end">
            <div className="text-right">
              <p className="text-xs text-gray-400">Your points</p>
              <p className="text-lg font-bold text-white">
                {pointsEvents.isLoading ? <span className="animate-pulse">Loading...</span> : points}
              </p>
            </div>

            <div className="text-right">
              <p className="text-xs text-gray-400">Your rank</p>
              <div className="flex items-center gap-1 text-lg font-bold text-white">
                <IbiIcon icon="game-icons:rank-3" className="text-sm" />
                <span>1444</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto pt-7 px-4">
        <div className="mb-12">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-2">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold mb-2 text-white">Invite your friends!</h2>
              <p className="text-sm sm:text-base text-gray-400">
                Earn 200 points and an additional 10% of their points.
              </p>
            </div>
          </div>

          {inviteCode ? (
            <ShareButtons inviteCode={inviteCode} />
          ) : (
            <div className="mb-8 text-gray-400">No invite code available to share</div>
          )}

          <div className="relative mb-24">
            <div className="absolute w-full h-[2px] bg-gray-800">
              {levels.map((level, index) => (
                <div
                  key={level.level}
                  className="absolute flex flex-col items-center"
                  style={{ left: `${(index / (levels.length - 1)) * 100}%` }}
                >
                  <div className="w-4 h-4 bg-[#010303] border border-gray-800 rounded-full -mt-[6px] flex items-center justify-center">
                    <div className="w-2 h-2 bg-gray-700 rounded-full" />
                  </div>
                  <p className="text-xs text-gray-500 mt-2">LEVEL {level.level}</p>
                  <p className="text-[10px] text-gray-700">{level.points}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8 sm:mb-12">
            {seasons.map((season) => (
              <div
                key={season.id}
                className="bg-[#0a0e15] border border-gray-800/50 rounded-lg p-4 sm:p-6 relative overflow-hidden"
              >
                <div className="flex justify-between items-start mb-8">
                  <h3 className="text-lg font-semibold text-white">Season {season.id}</h3>
                  <p className="text-xs text-gray-500">{season.date}</p>
                </div>
                <div className="flex items-center gap-2 text-sm relative z-10">
                  {season.status === "live" ? (
                    <div className="flex items-center gap-1 text-purple-400">
                      <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                      <span>Live now</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 text-green-400">
                      <IbiIcon icon="ph:check" />
                      <span>Complete</span>
                    </div>
                  )}
                </div>
                <div
                  className="absolute bottom-0 right-0 w-32 h-32 bg-purple-500/5 rounded-full blur-2xl"
                  style={{
                    transform: "translate(25%, 25%)",
                  }}
                />
              </div>
            ))}
          </div>

          <div className="space-y-4 sm:space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <IbiIcon icon="ph:fire" className="text-xl text-white" />
              <h2 className="text-lg sm:text-xl font-semibold text-white">Earn points</h2>
            </div>

            <div className="bg-[#0a0e15] border border-gray-800/50 rounded-lg">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white">Your Invite Code</h3>
                  {inviteCode && <span className="text-sm text-gray-400">Total Uses: {inviteCode.totalUses || 0}</span>}
                </div>

                <p className="text-gray-400 mb-6">
                  Share your invite code with friends to invite them to the platform. You will earn points when they
                  join.
                </p>

                {inviteCode ? (
                  <div className="space-y-6">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center justify-between p-4 rounded-lg bg-[#010303] border border-gray-800/30"
                    >
                      <div className="flex items-center gap-3">
                        <IbiIcon icon="mdi:ticket" className="text-2xl text-white" />
                        <div>
                          <p className="font-medium text-white">{inviteCode.code || "N/A"}</p>
                          <p className="text-sm text-gray-400">
                            Created:{" "}
                            {inviteCode.createdAt ? new Date(inviteCode.createdAt).toLocaleDateString() : "N/A"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:text-blue-400 hover:bg-blue-500/10"
                          onClick={() => copyReferralLink(inviteCode.code)}
                        >
                          <IbiIcon icon="iconamoon:copy" className="text-lg mr-2" />
                          Copy Link
                        </Button>
                      </div>
                    </motion.div>

                    {inviteCode && inviteCode.uses && inviteCode.uses.length > 0 && (
                      <>
                        <h4 className="text-md font-semibold text-white mt-8 mb-4">Usage History</h4>
                        <div className="space-y-4">
                          {(inviteCode.uses || []).map((use: InviteCodeUse) => (
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              key={use.id}
                              className="flex items-center justify-between p-4 rounded-lg bg-[#010303] border border-gray-800/30"
                            >
                              <div className="flex items-center gap-3">
                                <IbiIcon icon="mdi:ticket-confirmation" className="text-2xl text-white" />
                                <div>
                                  <p className="font-medium text-white">
                                    Used by: {use.userWallet ? shortenText(use.userWallet, 12) : "Unknown"}
                                  </p>
                                  <p className="text-sm text-gray-400">
                                    Date: {use.usedAt ? new Date(use.usedAt).toLocaleDateString() : "N/A"}
                                  </p>
                                </div>
                              </div>
                              <span className="text-sm font-semibold text-yellow-600">Used</span>
                            </motion.div>
                          ))}
                        </div>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <div className="animate-pulse">Loading invite code...</div>
                  </div>
                )}
              </div>
            </div>

            <Card className="bg-[#010303]/50 border-gray-800">
              <CardContent className="p-6">
                <LeaderBoard />
              </CardContent>
            </Card>

            <div className="bg-[#0a0e15] border border-gray-800/50 rounded-lg relative overflow-hidden">
              <div className="p-6 flex items-center justify-between relative z-10">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/10 rounded-lg">
                    <IbiIcon icon="ph:crown" className="text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Membership</h3>
                    <p className="text-sm text-gray-400">Variable points</p>
                  </div>
                </div>
                <span className="text-sm text-purple-400">Auto-claimed</span>
              </div>
              <div
                className="absolute bottom-0 right-0 w-32 h-32 bg-purple-500/5 rounded-full blur-2xl"
                style={{
                  transform: "translate(25%, 25%)",
                }}
              />
            </div>

            <div className="bg-[#0a0e15] border border-gray-800/50 rounded-lg relative overflow-hidden">
              <div className="p-6 flex items-center justify-between relative z-10">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/10 rounded-lg">
                    <IbiIcon icon="ph:ticket" className="text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Reserve an Agent Name Service NFT</h3>
                    <p className="text-sm text-gray-400">5,000 points</p>
                  </div>
                </div>
                <Button size="sm" className="text-white border-gray-700 hover:bg-gray-800">
                  Claim
                </Button>
              </div>
              <div
                className="absolute bottom-0 right-0 w-32 h-32 bg-blue-500/5 rounded-full blur-2xl"
                style={{
                  transform: "translate(25%, 25%)",
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InviteCodePage;
