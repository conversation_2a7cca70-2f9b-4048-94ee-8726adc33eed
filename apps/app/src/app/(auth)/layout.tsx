"use client";

import LegacyWrapper from "@/components/LegacyWrapper";
import LoadingState from "@/components/shared/LoadingState";
import useAuthentication from "@/hooks/useAuthentication";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { authenticated, ready, isValidating } = useAuthentication();

  if (isValidating) {
    return <LoadingState text="Validating authentication..." />;
  }

  if (!authenticated || !ready) {
    return null;
  }

  return <LegacyWrapper>{children}</LegacyWrapper>;
}
