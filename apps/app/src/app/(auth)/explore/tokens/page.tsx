"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Area, AreaChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

interface ChartData {
  date: string;
  price: number;
}

interface TokenStats {
  day: { value: number; percentage: number };
  month: { value: number; percentage: number };
  months3: { value: number; percentage: number };
  year: { value: number; percentage: number | null };
}

interface MarketData {
  marketCap: number;
  fullyDiluted: number;
}

const chartData: ChartData[] = Array.from({ length: 100 }, (_, i) => ({
  date: `2024-${(i % 12) + 1}-${(i % 28) + 1}`,
  price: 0.0000015 + Math.random() * 0.0000008,
}));

const Tokens = () => {
  const router = useRouter();

  const timeFrames = [
    { label: "1H", value: "1h" },
    { label: "1D", value: "1d", selected: true },
    { label: "1W", value: "1w" },
    { label: "1M", value: "1m" },
    { label: "1Y", value: "1y" },
    { label: "Max", value: "max" },
  ];

  const stats: TokenStats = {
    day: { value: 8.5, percentage: 8.5 },
    month: { value: 561.5, percentage: 561.5 },
    months3: { value: 1458.3, percentage: 1458.3 },
    year: { value: 0, percentage: null },
  };

  const marketData: MarketData = {
    marketCap: 0.0,
    fullyDiluted: 15_000_000, // $15M
  };

  const formatCurrency = (value: number) => {
    if (value === 0) return "—";
    if (value < 0.0001) return `$${value.toFixed(8)}`;
    if (value < 1) return `$${value.toFixed(4)}`;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatStats = (value: number) => {
    if (value === 0) return "—";
    return `+${value.toFixed(1)}%`;
  };

  const formatMarketCap = (value: number) => {
    if (value === 0) return "$0.00";
    if (value >= 1_000_000) {
      return `$${(value / 1_000_000).toFixed(1)}M`;
    }
    return formatCurrency(value);
  };

  return (
    <div className="max-w-6xl mx-auto mt-7 px-4 max-[750px]:pb-16">
      <Button className="gap-1 h-fit py-1.5 px-3" onClick={() => router.push("/explore")}>
        <IbiIcon icon="lucide:arrow-left" className="text-sm" />
        Return
      </Button>
      <div className="flex items-center gap-3 mb-8 mt-2">
        <IbiIcon icon="cryptocurrency-color:frame" className="w-8 h-8" />
        <h1 className="text-4xl font-bold">FrameToken Price</h1>
        <div className="flex gap-2 ml-auto">
          <button className="p-2 hover:bg-gray-800 rounded-lg">
            <IbiIcon icon="lucide:star" className="text-gray-400" />
          </button>
          <button className="p-2 hover:bg-gray-800 rounded-lg">
            <IbiIcon icon="lucide:share" className="text-gray-400" />
          </button>
          <button className="p-2 hover:bg-gray-800 rounded-lg">
            <IbiIcon icon="lucide:more-horizontal" className="text-gray-400" />
          </button>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-baseline gap-2">
          <span className="text-3xl font-bold">{formatCurrency(0.0000015)}</span>
          <span className="text-green-400">+8.5%</span>
        </div>
      </div>

      <div className="bg-gray-800/50 rounded-2xl p-6 backdrop-blur-md mb-6">
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#22c55e" stopOpacity={0.3} />
                  <stop offset="100%" stopColor="#22c55e" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="date" hide />
              <YAxis hide domain={["dataMin - 0.0000001", "dataMax + 0.0000001"]} />
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="bg-gray-800 border border-gray-700 p-2 rounded">
                        <p className="text-white">{formatCurrency(payload[0].value as any)}</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Area type="monotone" dataKey="price" stroke="#22c55e" fill="url(#gradient)" strokeWidth={2} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        <div className="flex gap-2 mt-4">
          {timeFrames.map((frame) => (
            <button
              key={frame.value}
              className={`px-3 py-1 rounded-lg text-sm ${
                frame.selected ? "bg-green-400/20 text-green-400" : "text-gray-400 hover:bg-gray-700"
              }`}
            >
              {frame.label}
            </button>
          ))}
        </div>
      </div>

      <div className="bg-gray-800/50 rounded-2xl p-6 backdrop-blur-md mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold mb-1">Equity</h3>
            <div className="flex items-center gap-2">
              <span>0 FRAME</span>
            </div>
          </div>
          <div>
            <h3 className="text-sm text-gray-400 text-right mb-1">24-hour Return</h3>
            <div className="text-green-400 text-right">+8.5% ($0.00)</div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-bold mb-4">Stats</h2>
        <div className="grid grid-cols-4 gap-4">
          <div>
            <h3 className="text-sm text-gray-400 mb-1">1 Day</h3>
            <span className="text-green-400">{formatStats(stats.day.percentage)}</span>
          </div>
          <div>
            <h3 className="text-sm text-gray-400 mb-1">1 Month</h3>
            <span className="text-green-400">{formatStats(stats.month.percentage)}</span>
          </div>
          <div>
            <h3 className="text-sm text-gray-400 mb-1">3 Months</h3>
            <span className="text-green-400">{formatStats(stats.months3.percentage)}</span>
          </div>
          <div>
            <h3 className="text-sm text-gray-400 mb-1">1 Year</h3>
            <span>{stats.year.percentage === null ? "—" : formatStats(stats.year.percentage)}</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-8">
        <div>
          <h3 className="text-sm text-gray-400 mb-1">Market Cap</h3>
          <span>{formatMarketCap(marketData.marketCap)}</span>
        </div>
        <div>
          <h3 className="text-sm text-gray-400 mb-1">Fully Diluted</h3>
          <span>{formatMarketCap(marketData.fullyDiluted)}</span>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">About FrameToken</h2>
        <p className="text-gray-400 mb-4">
          An ERC-20 token built to support builders and the community on the social network Farcaster. $FRAME was
          created to display how far you can go with simple technology like embeds on a website!
        </p>
        <div className="flex gap-4">
          <a href="#" className="text-blue-400 hover:underline">
            Website
          </a>
          <a href="#" className="text-blue-400 hover:underline">
            Twitter
          </a>
          <a href="#" className="text-blue-400 hover:underline">
            Coingecko
          </a>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">Explorers</h2>
        <a
          href="#"
          className="flex items-center gap-3 bg-gray-800/50 p-4 rounded-xl hover:bg-gray-800 transition-colors"
        >
          <img src="https://chain-icons.s3.amazonaws.com/chainlist/8453" alt="Base" className="w-6 h-6 rounded-full" />
          <span>Base</span>
          <span className="text-gray-400">0x9f4...35c7</span>
          <IbiIcon icon="lucide:external-link" className="text-gray-400 ml-auto" />
        </a>
      </div>
    </div>
  );
};

export default Tokens;
