"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";

const users = [
  {
    id: 1,
    name: "<PERSON>",
    address: "0x1234...5678",
    claimedUnits: 156,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,230",
      transactions: 234,
      lastActive: "2h ago",
    },
  },
  {
    id: 2,
    name: "<PERSON>",
    address: "0x8765...4321",
    claimedUnits: 89,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$28,450",
      transactions: 156,
      lastActive: "5h ago",
    },
  },
  {
    id: 3,
    name: "<PERSON>",
    address: "0x9876...2345",
    claimedUnits: 267,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$67,890",
      transactions: 345,
      lastActive: "1h ago",
    },
  },
  {
    id: 4,
    name: "Evelyn Brooks",
    address: "0x5678...9012",
    claimedUnits: 56,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$34,567",
      transactions: 178,
      lastActive: "6h ago",
    },
  },
  {
    id: 5,
    name: "Samantha Brown",
    address: "0x5432...1098",
    claimedUnits: 101,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$56,789",
      transactions: 210,
      lastActive: "3h ago",
    },
  },
  {
    id: 6,
    name: "Liam Davis",
    address: "0x6789...7654",
    claimedUnits: 234,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,678",
      transactions: 156,
      lastActive: "2h ago",
    },
  },
  {
    id: 7,
    name: "Alexander White",
    address: "0x4567...8901",
    claimedUnits: 189,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$78,901",
      transactions: 345,
      lastActive: "1h ago",
    },
  },
  {
    id: 8,
    name: "Olivia Martin",
    address: "0x5678...9012",
    claimedUnits: 56,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$34,567",
      transactions: 178,
      lastActive: "6h ago",
    },
  },
  {
    id: 9,
    name: "Oliver Lee",
    address: "0x6789...7654",
    claimedUnits: 234,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,678",
      transactions: 156,
      lastActive: "2h ago",
    },
  },
  {
    id: 10,
    name: "Emily Chen",
    address: "0x9876...2345",
    claimedUnits: 267,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$67,890",
      transactions: 345,
      lastActive: "1h ago",
    },
  },
  {
    id: 11,
    name: "Ava Mitchell",
    address: "0x5432...1098",
    claimedUnits: 101,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$56,789",
      transactions: 210,
      lastActive: "3h ago",
    },
  },
  {
    id: 12,
    name: "Sophia Brown",
    address: "0x5678...9012",
    claimedUnits: 56,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$34,567",
      transactions: 178,
      lastActive: "6h ago",
    },
  },
  {
    id: 13,
    name: "Mia Davis",
    address: "0x6789...7654",
    claimedUnits: 234,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,678",
      transactions: 156,
      lastActive: "2h ago",
    },
  },
  {
    id: 14,
    name: "Isabella White",
    address: "0x4567...8901",
    claimedUnits: 189,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$78,901",
      transactions: 345,
      lastActive: "1h ago",
    },
  },
  {
    id: 15,
    name: "Gabriel Martin",
    address: "0x5678...9012",
    claimedUnits: 56,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$34,567",
      transactions: 178,
      lastActive: "6h ago",
    },
  },
  {
    id: 16,
    name: "Julia Lee",
    address: "0x6789...7654",
    claimedUnits: 234,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,678",
      transactions: 156,
      lastActive: "2h ago",
    },
  },
  {
    id: 17,
    name: "Ethan Chen",
    address: "0x9876...2345",
    claimedUnits: 267,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$67,890",
      transactions: 345,
      lastActive: "1h ago",
    },
  },
  {
    id: 18,
    name: "Hannah Mitchell",
    address: "0x5432...1098",
    claimedUnits: 101,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$56,789",
      transactions: 210,
      lastActive: "3h ago",
    },
  },
  {
    id: 19,
    name: "Abigail Brown",
    address: "0x5678...9012",
    claimedUnits: 56,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$34,567",
      transactions: 178,
      lastActive: "6h ago",
    },
  },
  {
    id: 20,
    name: "Lily Davis",
    address: "0x6789...7654",
    claimedUnits: 234,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,678",
      transactions: 156,
      lastActive: "2h ago",
    },
  },
  {
    id: 21,
    name: "Madison White",
    address: "0x4567...8901",
    claimedUnits: 189,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: true,
    stats: {
      totalValue: "$78,901",
      transactions: 345,
      lastActive: "1h ago",
    },
  },
  {
    id: 22,
    name: "Logan Martin",
    address: "0x5678...9012",
    claimedUnits: 56,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$34,567",
      transactions: 178,
      lastActive: "6h ago",
    },
  },
  {
    id: 23,
    name: "Avery Lee",
    address: "0x6789...7654",
    claimedUnits: 234,
    profilePicture: "/placeholder.svg?height=40&width=40",
    following: false,
    stats: {
      totalValue: "$45,678",
      transactions: 156,
      lastActive: "2h ago",
    },
  },
];

export default function Users() {
  return (
    <div className="max-w-7xl mx-auto mt-5 px-4 sm:px-6 lg:px-8 max-[750px]:pb-20">
      <h1 className="text-4xl font-bold mb-8 text-white">Users</h1>
      <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {users.map((user) => (
          <Card
            key={user.id}
            className="p-4 sm:p-6 transition-all duration-200 hover:bg-slate-800/50 cursor-pointer bg-slate-900 border-slate-800 text-white"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <Avatar className="h-8 w-8 sm:h-10 sm:w-10">
                  <AvatarImage src={user.profilePicture} alt={user.name} />
                  <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold text-white text-sm sm:text-base">{user.name}</h3>
                  <p className="text-xs sm:text-sm text-slate-400 font-mono">{user.address}</p>
                </div>
              </div>
              <div className="flex items-center gap-1 sm:gap-2">
                <Button
                  variant={user.following ? "secondary" : "outline"}
                  size="sm"
                  className={`text-xs sm:text-sm px-2 sm:px-3 py-1 ${
                    user.following
                      ? "bg-slate-700 hover:bg-slate-600"
                      : "border-slate-700 text-slate-300 hover:bg-slate-800"
                  }`}
                >
                  {user.following ? "Following" : "Follow"}
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 sm:h-8 sm:w-8 text-slate-400 hover:text-white hover:bg-slate-800"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-slate-900 border-slate-800">
                    <DropdownMenuItem className="text-slate-300 hover:text-white focus:text-white hover:bg-slate-800 focus:bg-slate-800">
                      View profile
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-slate-300 hover:text-white focus:text-white hover:bg-slate-800 focus:bg-slate-800">
                      Copy address
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-slate-300 hover:text-white focus:text-white hover:bg-slate-800 focus:bg-slate-800">
                      Block user
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <div className="mt-3 sm:mt-4 grid grid-cols-3 gap-2 sm:gap-4 text-xs sm:text-sm">
              <div>
                <p className="text-slate-400">Claimed Units</p>
                <p className="font-medium text-white">{user.claimedUnits}</p>
              </div>
              <div>
                <p className="text-slate-400">Total Value</p>
                <p className="font-medium text-white">{user.stats.totalValue}</p>
              </div>
              <div>
                <p className="text-slate-400">Transactions</p>
                <p className="font-medium text-white">{user.stats.transactions}</p>
              </div>
            </div>
            <p className="mt-2 sm:mt-4 text-2xs sm:text-xs text-slate-400">Last active: {user.stats.lastActive}</p>
          </Card>
        ))}
      </div>
    </div>
  );
}
