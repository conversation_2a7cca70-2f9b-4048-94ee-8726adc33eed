import { ScrollArea } from "@/components/ui/scroll-area";
import React, { lazy } from "react";

const DashboardHeader = lazy(() => import("@/components/Dashboard/DashboardHeader"));

const DashboardWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <ScrollArea className="text-white h-screen">
      <DashboardHeader />
      {children}
    </ScrollArea>
  );
};

export default DashboardWrapper;
