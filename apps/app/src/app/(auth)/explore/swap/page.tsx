"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import React, { useCallback, useMemo, useState } from "react";

interface Token {
  symbol: string;
  name: string;
  icon: string;
  balance: number;
  decimals: number;
}

const Swap = () => {
  const tokens: Token[] = [
    {
      symbol: "ETH",
      name: "Ethereum",
      icon: "cryptocurrency-color:eth",
      balance: 1.5,
      decimals: 18,
    },
    {
      symbol: "USDC",
      name: "USD Coin",
      icon: "cryptocurrency-color:usdc",
      balance: 2500.75,
      decimals: 6,
    },
    {
      symbol: "USDT",
      name: "Tether",
      icon: "cryptocurrency-color:usdt",
      balance: 1000,
      decimals: 6,
    },
    {
      symbol: "DAI",
      name: "<PERSON>",
      icon: "cryptocurrency-color:dai",
      balance: 3000,
      decimals: 18,
    },
  ];

  const [payToken, setPayToken] = useState<Token>(tokens[0]);
  const [receiveToken, setReceiveToken] = useState<Token>(tokens[1]);
  const [payAmount, setPayAmount] = useState<string>("");
  const [receiveAmount, setReceiveAmount] = useState<string>("");
  const [slippage] = useState<number>(0.5);

  const exchangeRate = useMemo(() => {
    if (payToken.symbol === "ETH" && receiveToken.symbol === "USDC") return 1850;
    if (payToken.symbol === "USDC" && receiveToken.symbol === "ETH") return 1 / 1850;
    return 1;
  }, [payToken, receiveToken]);

  const handlePayTokenSelect = (token: Token) => {
    if (token.symbol === receiveToken.symbol) {
      setReceiveToken(payToken);
    }
    setPayToken(token);
    setPayAmount("");
    setReceiveAmount("");
  };

  const handleReceiveTokenSelect = (token: Token) => {
    if (token.symbol === payToken.symbol) {
      setPayToken(receiveToken);
    }
    setReceiveToken(token);
    setPayAmount("");
    setReceiveAmount("");
  };

  const handlePayAmountChange = (amount: string) => {
    setPayAmount(amount);
    const numAmount = parseFloat(amount) || 0;
    setReceiveAmount((numAmount * exchangeRate).toFixed(6));
  };

  const handleReceiveAmountChange = (amount: string) => {
    setReceiveAmount(amount);
    const numAmount = parseFloat(amount) || 0;
    setPayAmount((numAmount / exchangeRate).toFixed(6));
  };

  const handlePercentageClick = (percentage: number) => {
    const amount = (payToken.balance * (percentage / 100)).toFixed(6);
    handlePayAmountChange(amount);
  };

  const handleSwapTokens = () => {
    const tempToken = payToken;
    setPayToken(receiveToken);
    setReceiveToken(tempToken);
    setPayAmount("");
    setReceiveAmount("");
  };

  const canSwap = useMemo(() => {
    const numAmount = parseFloat(payAmount) || 0;
    return numAmount > 0 && numAmount <= payToken.balance;
  }, [payAmount, payToken.balance]);

  const getButtonText = useCallback(() => {
    if (!payAmount || !receiveAmount) return "Enter amount";
    if (parseFloat(payAmount) > payToken.balance) return "Insufficient balance";
    return "Swap";
  }, [payAmount, receiveAmount, payToken.balance]);

  return (
    <div className="max-w-6xl mx-auto mt-7 px-4 sm:px-6 lg:px-8">
      <h1 className="text-4xl font-bold mb-8">Swap</h1>

      <div className="w-full flex justify-center">
        <div className="w-full max-w-[480px] rounded-2xl bg-gray-800/50 backdrop-blur-md p-6">
          <div className="flex items-center gap-2 mb-6">
            <Popover>
              <PopoverTrigger asChild>
                <div className="flex items-center gap-2 cursor-pointer">
                  <IbiIcon icon={payToken.icon} className="w-6 h-6" />
                  <span className="text-xl">{payToken.name}</span>
                  <IbiIcon icon="lucide:chevron-down" className="text-gray-400" />
                </div>
              </PopoverTrigger>
              <PopoverContent className="max-w-[280px] w-full p-2 bg-gray-800 border border-gray-700">
                <div className="flex flex-col gap-1">
                  {tokens.map((token) => (
                    <button
                      key={token.symbol}
                      className={`flex items-center gap-3 p-2 hover:bg-gray-700 rounded-lg transition-colors ${
                        token.symbol === receiveToken.symbol ? "opacity-50 cursor-not-allowed" : ""
                      }`}
                      onClick={() => handlePayTokenSelect(token)}
                      disabled={token.symbol === receiveToken.symbol}
                    >
                      <IbiIcon icon={token.icon} className="w-6 h-6" />
                      <div className="flex flex-col items-start">
                        <span className="font-medium text-white">{token.symbol}</span>
                        <span className="text-sm text-gray-400">{token.name}</span>
                      </div>
                      <span className="ml-auto text-sm text-gray-400">
                        {token.balance} {token.symbol}
                      </span>
                      {token.symbol === payToken.symbol && (
                        <IbiIcon icon="lucide:check" className="text-blue-400 ml-2" />
                      )}
                    </button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
            <IbiIcon icon="lucide:settings" className="ml-auto text-gray-400 cursor-pointer hover:text-gray-300" />
          </div>

          <div className="rounded-xl bg-gray-800 p-4 mb-2">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm text-gray-400">Pay with</span>
              <div className="flex gap-2">
                {[25, 50, 75].map((percentage) => (
                  <span
                    key={percentage}
                    onClick={() => handlePercentageClick(percentage)}
                    className="text-xs py-1 px-2 rounded bg-blue-500/20 text-blue-400 cursor-pointer hover:bg-blue-500/30"
                  >
                    {percentage}%
                  </span>
                ))}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <div className="flex items-center gap-2 cursor-pointer hover:bg-gray-700 p-2 rounded-lg transition-colors">
                    <IbiIcon icon={payToken.icon} className="w-6 h-6" />
                    <span className="font-medium">{payToken.symbol}</span>
                    <IbiIcon icon="lucide:chevron-down" className="text-gray-400" />
                  </div>
                </PopoverTrigger>
                <PopoverContent className="max-w-[280px] w-full p-2 bg-gray-800 border border-gray-700">
                  <div className="flex flex-col gap-1">
                    {tokens.map((token) => (
                      <button
                        key={token.symbol}
                        className={`flex items-center gap-3 p-2 hover:bg-gray-700 rounded-lg transition-colors ${
                          token.symbol === receiveToken.symbol ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                        onClick={() => handlePayTokenSelect(token)}
                        disabled={token.symbol === receiveToken.symbol}
                      >
                        <IbiIcon icon={token.icon} className="w-6 h-6" />
                        <div className="flex flex-col items-start">
                          <span className="font-medium text-white">{token.symbol}</span>
                          <span className="text-sm text-gray-400">{token.name}</span>
                        </div>
                        <span className="ml-auto text-sm text-gray-400">
                          {token.balance} {token.symbol}
                        </span>
                        {token.symbol === payToken.symbol && (
                          <IbiIcon icon="lucide:check" className="text-blue-400 ml-2" />
                        )}
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
              <input
                type="number"
                value={payAmount}
                onChange={(e) => handlePayAmountChange(e.target.value)}
                className="ml-auto bg-transparent text-right outline-none"
                placeholder="0"
              />
            </div>
            <div className="text-sm text-gray-500 mt-1">
              Balance: {payToken.balance} {payToken.symbol}
            </div>
          </div>

          <div className="flex justify-center -my-1">
            <div
              onClick={handleSwapTokens}
              className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors"
            >
              <IbiIcon icon="lucide:arrow-down" className="text-gray-400" />
            </div>
          </div>

          <div className="rounded-xl bg-gray-800 p-4 mb-4">
            <span className="text-sm text-gray-400 mb-3 block">Receive</span>
            <div className="flex items-center gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <div className="flex items-center gap-2 cursor-pointer hover:bg-gray-700 p-2 rounded-lg transition-colors">
                    <IbiIcon icon={receiveToken.icon} className="w-6 h-6" />
                    <span className="font-medium">{receiveToken.symbol}</span>
                    <IbiIcon icon="lucide:chevron-down" className="text-gray-400" />
                  </div>
                </PopoverTrigger>
                <PopoverContent className="max-w-[280px] w-full p-2 bg-gray-800 border border-gray-700">
                  <div className="flex flex-col gap-1">
                    {tokens.map((token) => (
                      <button
                        key={token.symbol}
                        className={`flex items-center gap-3 p-2 hover:bg-gray-700 rounded-lg transition-colors ${
                          token.symbol === payToken.symbol ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                        onClick={() => handleReceiveTokenSelect(token)}
                        disabled={token.symbol === payToken.symbol}
                      >
                        <IbiIcon icon={token.icon} className="w-6 h-6" />
                        <div className="flex flex-col items-start">
                          <span className="font-medium text-white">{token.symbol}</span>
                          <span className="text-sm text-gray-400">{token.name}</span>
                        </div>
                        <span className="ml-auto text-sm text-gray-400">
                          {token.balance} {token.symbol}
                        </span>
                        {token.symbol === receiveToken.symbol && (
                          <IbiIcon icon="lucide:check" className="text-blue-400 ml-2" />
                        )}
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
              <input
                type="number"
                value={receiveAmount}
                onChange={(e) => handleReceiveAmountChange(e.target.value)}
                className="ml-auto bg-transparent text-right outline-none"
                placeholder="0"
              />
            </div>
            <div className="text-sm text-gray-500 mt-1">
              Balance: {receiveToken.balance} {receiveToken.symbol}
            </div>
          </div>

          {payAmount && receiveAmount && (
            <div className="mb-4 p-3 rounded-lg bg-gray-700/30">
              <div className="flex justify-between items-center text-sm text-gray-400">
                <span>Exchange Rate</span>
                <span>
                  1 {payToken.symbol} ≈ {exchangeRate} {receiveToken.symbol}
                </span>
              </div>
              <div className="flex justify-between items-center text-sm text-gray-400 mt-2">
                <span>Slippage Tolerance</span>
                <span>{slippage}%</span>
              </div>
            </div>
          )}

          <Button className="w-full h-12 text-lg" disabled={!canSwap}>
            {getButtonText()}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Swap;
