"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Droplets, Map, Mountain, Palmtree, Sprout, Sun, TreesIcon as Tree, Wind } from "lucide-react";

const landUnits = [
  {
    id: 1,
    name: "Floresta Amazônica",
    size: 150,
    type: "Floresta Tropical",
    resources: ["Madeira", "Plantas Medicinais"],
    tokenPrice: 0.05,
    accumulatedTokens: 2500,
    icon: Tree,
  },
  {
    id: 2,
    name: "<PERSON> da Mantiqueira",
    size: 200,
    type: "Montan<PERSON>",
    resources: ["Ecoturismo", "Água Mineral"],
    tokenPrice: 0.1,
    accumulatedTokens: 1500,
    icon: Mountain,
  },
  {
    id: 3,
    name: "Pantanal",
    size: 100,
    type: "Planície Alagada",
    resources: ["Biodiversidade", "Pesca"],
    tokenPrice: 0.08,
    accumulatedTokens: 3000,
    icon: Droplets,
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>",
    size: 300,
    type: "<PERSON><PERSON>",
    resources: ["<PERSON><PERSON><PERSON> Nativas", "Plantas Medicinais"],
    tokenPrice: 0.15,
    accumulatedTokens: 2000,
    icon: Sun,
  },
  {
    id: 5,
    name: "Chapada Diamantina",
    size: 180,
    type: "Planalto",
    resources: ["Minerais", "Turismo de Aventura"],
    tokenPrice: 0.07,
    accumulatedTokens: 1800,
    icon: Wind,
  },
  {
    id: 6,
    name: "Vale do São Francisco",
    size: 250,
    type: "Vale Fértil",
    resources: ["Vinicultura", "Fruticultura"],
    tokenPrice: 0.12,
    accumulatedTokens: 2200,
    icon: Sprout,
  },
  {
    id: 7,
    name: "Lençóis Maranhenses",
    size: 120,
    type: "Dunas e Lagoas",
    resources: ["Turismo", "Energia Eólica"],
    tokenPrice: 0.06,
    accumulatedTokens: 1000,
    icon: Map,
  },
  {
    id: 8,
    name: "Fernando de Noronha",
    size: 90,
    type: "Arquipélago",
    resources: ["Ecoturismo", "Preservação Marinha"],
    tokenPrice: 0.09,
    accumulatedTokens: 1200,
    icon: Palmtree,
  },
];

export default function LandUnits() {
  return (
    <div className="max-w-7xl mx-auto mt-5 px-4 sm:px-6 lg:px-8 max-[750px]:pb-20">
      <h1 className="text-4xl font-bold mb-8 text-white">Land units</h1>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {landUnits.map((unit) => {
          const Icon = unit.icon;
          return (
            <Card
              key={unit.id}
              className="bg-gradient-to-b from-slate-900 to-slate-800 border-slate-700 text-white overflow-hidden flex justify-between flex-col"
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg font-semibold">{unit.name}</CardTitle>
                  <Icon className="h-6 w-6 text-emerald-400" />
                </div>
                <p className="text-sm text-slate-400">{unit.type}</p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 text-sm mb-4">
                  <div>
                    <p className="text-slate-400">Tamanho</p>
                    <p className="font-medium">{unit.size} hectares</p>
                  </div>
                  <div>
                    <p className="text-slate-400">Recursos</p>
                    <p className="font-medium">{unit.resources.join(", ")}</p>
                  </div>
                </div>
                <div className="space-y-2 mt-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Preço do Token</span>
                    <span className="font-medium">R$ {(unit.tokenPrice * 5).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Tokens Acumulados</span>
                    <span className="font-medium">{unit.accumulatedTokens.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Valor Total</span>
                    <span className="font-medium">R$ {(unit.tokenPrice * unit.accumulatedTokens * 5).toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
              <div className="flex flex-wrap gap-2 p-4">
                {unit.resources.map((resource, index) => (
                  <Badge key={index} variant="secondary" className="bg-emerald-900 text-emerald-100">
                    {resource}
                  </Badge>
                ))}
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
