import gsap from 'gsap'
import { useCallback, useEffect, useRef, useState } from 'react'
import { How<PERSON>, How<PERSON> } from '~/libs/howler'
import '~/libs/howler/howler.convolver'
import '~/libs/howler/howler.filter'
import Sprites from '../public/sounds/sprites.json'

let _howl: Howl | null = null
// let frequency = 1

const proxy = {
  frequency: 1,
}

function getFrequency(inputValue: number) {
  const minValue = 40
  const maxValue = Howler.ctx.sampleRate / 2
  const numberOfOctaves = Math.log(maxValue / minValue) / Math.LN2
  const multiplier = 2 ** (numberOfOctaves * (inputValue - 1.0))

  return maxValue * multiplier
}

export function useAudio() {
  const [howl] = useState<Howl | null>(() => {
    if (!_howl) {
      _howl = new Howl({
        src: Sprites.src.map((resource) => resource.replace('public/', '')),
        sprite: Sprites.sprite,
        preload: true,
        onplay: (id) => {
          _howl.addFilter(
            {
              filterType: 'lowpass',
              frequency: getFrequency(proxy.frequency),
              Q: 1,
            },
            id
          )
        },
      })
      _howl.volume(0)
    }

    _howl.bend = (value: number) => {
      const nodes = []

      const ids = _howl._getSoundIds()
      for (let i = 0; i < ids.length; i++) {
        // Get the sound.
        const sound = _howl._soundById(ids[i])

        if (sound) {
          if (sound._filterNode) {
            nodes.push(sound._filterNode)
          }
        }
      }

      gsap.to(proxy, {
        frequency: value,
        duration: 1,
        ease: 'expo.out',
        onUpdate: () => {
          for (const node of nodes) {
            node.frequency.value = getFrequency(proxy.frequency)
          }
        },
      })
    }

    return _howl
  })

  return howl
}

type PlayOptions = {
  soundId: string
  volume?: number
  loop?: boolean
  rate?: number
  onComplete?: () => void
}

let _debouncedHowl: Howl | null = null
export function useAudioDebounced(delay?: number) {
  const [audio] = useState<Howl | null>(() => {
    if (!_debouncedHowl) {
      _debouncedHowl = new Howl({
        src: Sprites.src.map((resource) => resource.replace('public/', '')),
        sprite: Sprites.sprite,
        preload: true,
      })
      _debouncedHowl.volume(0)
    }

    return _debouncedHowl
  })
  const isDebouncedRef = useRef(false)
  const timeoutRef = useRef<NodeJS.Timeout>(null)
  const soundRef = useRef<Howl | null>(null)

  useEffect(() => {
    return () => {
      audio.stop(soundRef.current)
    }
  }, [audio])

  const play = useCallback(
    (options: PlayOptions) => {
      const {
        soundId,
        volume = 1,
        loop = false,
        rate = 1,
        onComplete,
      } = options

      if (!isDebouncedRef.current) {
        soundRef.current = audio.play(soundId)
        audio.volume(volume, soundRef.current)
        audio.loop(loop, soundRef.current)
        audio.rate(rate, soundRef.current)

        if (onComplete) {
          audio.on('end', onComplete, soundRef.current)
        }

        isDebouncedRef.current = true
      }

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        isDebouncedRef.current = false
      }, delay)

      return soundRef.current
    },
    [audio]
  )

  const stop = useCallback(
    ({ id }: { id: string }) => {
      if (!id) return

      audio.stop(id)
      isDebouncedRef.current = false
    },
    [audio]
  )

  return { play, stop }
}
