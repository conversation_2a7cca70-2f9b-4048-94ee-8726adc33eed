import { useEffect } from 'react'
import { useDeviceDetection } from './use-device-detection'

interface KeyboardShortcutOptions {
  enabled: boolean
}

export function useKeyboardShortcut(
  keys: string[],
  callback: (event: KeyboardEvent) => void,
  options: KeyboardShortcutOptions = { enabled: true }
): void {
  const { isDesktop } = useDeviceDetection()

  useEffect(() => {
    if (!options.enabled || !isDesktop) return

    const handleKeyPress = (event: KeyboardEvent) => {
      if (keys.includes(event.key)) {
        callback(event)
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [keys, callback, options.enabled, isDesktop])
}
