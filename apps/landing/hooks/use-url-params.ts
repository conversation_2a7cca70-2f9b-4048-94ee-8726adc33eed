import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import { isEmptyArray } from '~/libs/utils'

//window.pushState allows change history stack wihout reload the page
//https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#windowhistorypushstate

/* 
  To use searchParams without reload use reload = false
  and use state to store current params, filter to which params listen
  with subscribeTo
  -
  To use searchParams with reload use reload = true and use 
  methods as getParams, setParams, deleteParams 
*/

export function useSearchParamsMethods({
  reload = true,
  subscribeTo = [],
  serverParams = {},
}: SearchParamsOptions = {}) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [currentParams, setCurrentParams] =
    useState<Record<string, string>>(serverParams)

  useEffect(() => {
    if (reload) return

    let params: Record<string, string> = {}
    for (const key of searchParams.keys()) {
      const value = searchParams.get(key)
      if (value) params[key] = value
    }

    // Subscribe to choosen params
    if (!isEmptyArray(subscribeTo)) {
      const filteredParams: Record<string, string> = {}
      Object.entries(params).map(([key, value]) => {
        if (subscribeTo.includes(key)) {
          filteredParams[key] = value
        }
      })

      params = filteredParams
    }

    setCurrentParams((prevParams) => {
      if (areParamsEqual(prevParams, params)) return prevParams
      return params
    })
  }, [searchParams, reload, subscribeTo])

  const getParams = useCallback(
    ({ key }: GetParamsOptions) => {
      if (!key) return null

      if (Array.isArray(key)) {
        return key.map((k) => searchParams.get(k)?.toString())
      }

      return searchParams.get(key)?.toString()
    },
    [searchParams]
  )

  const setParams = useCallback(
    ({
      key,
      value,
      scroll = false,
      anchor = null,
      pushToHistory = true,
    }: SetParamsOptions) => {
      if (!value || !key) return
      const newSeachParams = new URLSearchParams(searchParams.toString())

      const keys = Array.isArray(key) ? key : [key]
      const values = Array.isArray(value) ? value : [value]
      if (keys.every((key, index) => getParams({ key }) === values[index]))
        return

      for (const [index, key] of keys.entries()) {
        newSeachParams.set(key, values[index])
      }

      const route = createRoute({ pathname, newSeachParams, anchor })

      if (reload) {
        withReload({ router, route, scroll, pushToHistory })
        return
      }

      withoutReload({ window, route, pushToHistory })
    },
    [pathname, searchParams, router, reload, getParams]
  )

  const deleteParams = useCallback(
    ({ key, scroll = false, pushToHistory = true }: DeleteParamsOptions) => {
      if (!key) return
      const newSeachParams = new URLSearchParams(searchParams.toString())

      if (Array.isArray(key)) {
        for (const k of key) {
          newSeachParams.delete(k)
        }
      } else {
        newSeachParams.delete(key)
      }

      const route = createRoute({ pathname, newSeachParams })

      if (reload) {
        withReload({ router, route, scroll, pushToHistory })
        return
      }

      withoutReload({ window, route, pushToHistory })
    },
    [pathname, searchParams, router, reload]
  )

  return { currentParams, getParams, setParams, deleteParams }
}

function areParamsEqual(
  prevParams: Record<string, string> | undefined,
  newParams: Record<string, string> | undefined
): boolean {
  const prevKeys = Object.keys(prevParams || {})
  const newKeys = Object.keys(newParams || {})

  if (prevKeys.length !== newKeys.length) return false

  return prevKeys.every((key) => prevParams?.[key] === newParams?.[key])
}

function withReload({
  router,
  route,
  scroll,
  pushToHistory = true,
}: ReloadOptions): void {
  if (pushToHistory) {
    router.push(route, { scroll })
  } else {
    router.replace(route, {
      scroll,
    })
  }
}

function withoutReload({
  window,
  route,
  pushToHistory = true,
}: WithoutReloadOptions): void {
  if (pushToHistory) {
    window.history.pushState(null, '', route)
  } else {
    window.history.replaceState(null, '', route)
  }
}

function createRoute({
  pathname,
  newSeachParams,
  anchor = null,
}: RouteOptions): string {
  if (anchor) return `${pathname}?${newSeachParams}#${anchor}`
  return `${pathname}?${newSeachParams}`
}

interface SearchParamsOptions {
  reload?: boolean
  subscribeTo?: string[]
  serverParams?: Record<string, string>
}

interface SetParamsOptions {
  key: string | string[]
  value: string | string[]
  scroll?: boolean
  anchor?: string | null
  pushToHistory?: boolean
}

interface DeleteParamsOptions {
  key: string | string[]
  scroll?: boolean
  pushToHistory?: boolean
}

interface GetParamsOptions {
  key: string | string[]
}

interface RouteOptions {
  pathname: string
  newSeachParams: URLSearchParams
  anchor?: string | null
}

interface ReloadOptions {
  router: ReturnType<typeof useRouter>
  route: string
  scroll?: boolean
  pushToHistory?: boolean
}

interface WithoutReloadOptions {
  window: Window
  route: string
  pushToHistory?: boolean
}
