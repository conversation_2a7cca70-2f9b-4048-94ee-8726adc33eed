'use client'

import { useMediaQuery } from 'hamo'
import { createContext, useContext } from 'react'
import { breakpoints } from '~/styles/config.mjs'

type DeviceDetectionContextType = {
  isMobile?: boolean
  isDesktop?: boolean
  isReducedMotion?: boolean
  isWebGL?: boolean
  isLowPowerMode: boolean
}

export const DeviceDetectionContext = createContext<DeviceDetectionContextType>(
  {
    isMobile: false,
    isDesktop: false,
    isReducedMotion: false,
    isWebGL: false,
    isLowPowerMode: false,
  }
)

export function DeviceDetectionContextProvider({
  children,
}: { children: React.ReactNode }) {
  const breakpoint = breakpoints.dt

  const isMobile = useMediaQuery(`(max-width: ${breakpoint - 1}px)`)
  const isDesktop = useMediaQuery(`(min-width: ${breakpoint}px)`)
  const isReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)')
  const isWebGL = true
  const isLowPowerMode = false

  return (
    <DeviceDetectionContext.Provider
      value={{ isMobile, isDesktop, isReducedMotion, isWebGL, isLowPowerMode }}
    >
      {children}
    </DeviceDetectionContext.Provider>
  )
}

export function useDeviceDetection() {
  const context = useContext(DeviceDetectionContext)

  if (!context) {
    throw new Error(
      'useDeviceDetection must be used within a DeviceDetectionContextProvider'
    )
  }

  return context
}
