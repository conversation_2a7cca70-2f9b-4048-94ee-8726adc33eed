import { Wrapper } from '~/app/(pages)/(components)/wrapper'
import { TheatreProjectProvider } from '~/libs/theatre'
import * as ScrollSpy from '../(components)/scroll-spy'
import { Chapter2 } from '../home/<USER>/chapter2'
import { THEATRE_CONFIG } from '../home/<USER>'

export default function Page() {
  return (
    <TheatreProjectProvider id="Ibicash" config={THEATRE_CONFIG}>
      <ScrollSpy.Root>
        <Wrapper
          theme="dark"
          webgl={{
            postprocessing: true,
            orbitControls: true,
          }}
          lenis
        >
          <div
            style={{
              height: '100vh',
              background: 'black',
              zIndex: 1,
              opacity: 0.75,
            }}
          />
          <Chapter2 />
          <div
            style={{
              height: '100vh',
              background: 'black',
              zIndex: 1,
              opacity: 0.75,
            }}
          />
        </Wrapper>
      </ScrollSpy.Root>
    </TheatreProjectProvider>
  )
}
