import { MeshBasicMaterial } from 'three'
import {
  getNormalsShader,
  getShadows<PERSON>hader,
  getTimeShader,
  getWorldPositionShader,
} from '~/libs/webgl/utils/extend-shader'

const RANDOM = Math.random()

export class TerrainMaterial extends MeshBasicMaterial {
  constructor({ flowmap, ...props } = {}) {
    super({ ...props })

    this.shadows = getShadowsShader()
    this.normals = getNormalsShader()
    this.worldPosition = getWorldPositionShader()
    this.time = getTimeShader()

    this.uniforms = {
      ...this.uniforms,
      ...this.time.uniforms,
      uExposure: { value: 1 },
    }

    this.defines = {
      ...this.defines,
      ...this.normals.defines,
      ...this.worldPosition.defines,
    }

    this.customProgramCacheKey = () =>
      this.constructor.name + (process.env.NODE_ENV === 'development' && RANDOM)
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    this.worldPosition.extend(shader)
    this.normals.extend(shader)
    this.time.extend(shader)

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      'void main() {',
      /*glsl*/ `
      uniform float uExposure;

      void main() {
      `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      '#include <map_fragment>',
      /*glsl*/ `#include <map_fragment>
      
      diffuseColor *= uExposure;
      `
    )
  }

  raf(deltaTime) {
    this.time.raf(deltaTime)
  }

  setRendererResolution(x, y) {
    this.uniforms.uRendererResolution.value.set(x, y)
  }

  get exposure() {
    return this.uniforms.uExposure.value
  }

  set exposure(value) {
    this.uniforms.uExposure.value = value
  }
}
