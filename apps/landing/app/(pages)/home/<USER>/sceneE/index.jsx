'use client'

import { useGLTF } from '@react-three/drei'
import { types } from '@theatre/core'
import { useState } from 'react'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { updateTextures } from '~/libs/webgl/utils/textures'
import Clouds from '../clouds'
import { Fog } from '../fog'
import { Sky } from '../sky'
import { Stars } from '../stars'
import { TerrainMaterial } from './material'

export function SceneE() {
  const {
    nodes: {
      Landscape_Rework001: terrainNode,
      Landscape_Mesa001: terrainNode2,
    },
  } = useGLTF('/models/Landscape_Rework009.glb')

  // console.log(flowmap)

  const terrainDiffuse = useProgressiveKTX2(
    '/textures/sceneE/Landscape_Night001.ktx2'
  )
  updateTextures([terrainDiffuse])

  const { isDesktop } = useDeviceDetection()

  const [terrainMaterial] = useState(
    () =>
      new TerrainMaterial({
        map: terrainDiffuse,
      })
  )

  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'terrain',
    {
      exposure: types.number(1, {
        nudgeMultiplier: 0.01,
        range: [0, 10],
      }),
    },
    {
      onValuesChange: ({ exposure }) => {
        terrainMaterial.exposure = exposure
      },
    }
  )

  // const [fogMaskMaterial] = useState(
  //   () =>
  //     new FogMaskMaterial({
  //       color: '#1a2f54',
  //     })
  // )

  return (
    <>
      <Fog
        theatreKey="fog"
        far={250}
        near={0}
        color={{ r: 24 / 255, g: 30 / 255, b: 62 / 255 }}
        background
      />
      {/* <primitive object={cameraNode} ref={cameraNodeRef} /> */}
      <mesh matrixAutoUpdate={false}>
        <primitive object={terrainNode.geometry} attach="geometry" />
        <primitive object={terrainMaterial} attach="material" />
      </mesh>
      <mesh matrixAutoUpdate={false}>
        <primitive object={terrainNode2.geometry} attach="geometry" />
        <primitive object={terrainMaterial} attach="material" />
      </mesh>
      <Clouds
        position={[0, 30, 0]}
        theatreKey="clouds-2"
        speed={-5}
        frequency={0.5}
        opacity={0.02}
      />
      <Clouds
        position={[0, 25, 0]}
        theatreKey="clouds-2"
        speed={-5}
        frequency={0.5}
        opacity={0.02}
      />
      <Clouds
        position={[0, 20, 0]}
        theatreKey="clouds-2"
        speed={-5}
        frequency={0.5}
        opacity={0.02}
      />
      <Clouds
        position={[0, 3, 0]}
        theatreKey="clouds"
        opacity={0.02}
        frequency={5}
        speed={-5}
        alphaMap={true}
      />
      <Sky />
      <Stars index={0} />

      {/* <Stars index={1} /> */}
      {/* <mesh
        matrixAutoUpdate={false}
        scale={[400, 15, 100]}
        rotation-y={Math.PI / 2}
        position={[-130, 3, 0]}
        ref={(node) => {
          node?.updateMatrix()
        }}
      >
        <planeGeometry />
        <primitive object={fogMaskMaterial} attach="material" />
      </mesh> */}
      {/* <mesh
        matrixAutoUpdate={false}
        scale={[160, 20, 100]}
        rotation-y={0}
        position={[100, 5, -190]}
        ref={(node) => {
          node?.updateMatrix()
        }}
      >
        <planeGeometry />
        <primitive object={fogMaskMaterial} attach="material" />
      </mesh>
      <mesh
        matrixAutoUpdate={false}
        scale={[160, 20, 100]}
        rotation-y={Math.PI / 2}
        position={[160, 5, -110]}
        ref={(node) => {
          node?.updateMatrix()
        }}
      >
        <planeGeometry />
        <primitive object={fogMaskMaterial} attach="material" />
      </mesh> */}
    </>
  )
}
