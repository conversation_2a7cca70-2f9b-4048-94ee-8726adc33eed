import { types } from '@theatre/core'
import { useEffect } from 'react'
import { MirroredRepeatWrapping, SRGBColorSpace } from 'three'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { shadowsUniforms } from '~/libs/webgl/utils/extend-shader'

export function Shadows() {
  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'shadows',
    {
      speed: types.number(0.5, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
      }),
      frequency: types.number(0.5, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
      }),
      amplitude: types.number(1.5, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ speed, frequency, amplitude }) => {
        shadowsUniforms.uShadowsSpeed.value = speed * 0.01
        shadowsUniforms.uShadowsFrequency.value = frequency * 0.001
        shadowsUniforms.uShadowAmplitude.value = amplitude
      },
    }
  )

  // const noiseTexture = useTexture('/textures/noise/noise.png')
  const noiseTexture = useProgressiveKTX2('/textures/noise/noise.ktx2')

  useEffect(() => {
    noiseTexture.wrapS = noiseTexture.wrapT = MirroredRepeatWrapping
    noiseTexture.colorSpace = SRGBColorSpace
    noiseTexture.needsUpdate = true

    shadowsUniforms.uShadowsNoiseTexture.value = noiseTexture
  }, [noiseTexture])
}
