import { InstancedAttribute } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import { types } from '@theatre/core'
import { useRef, useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { Instance, Instances } from '~/libs/webgl/components/instances'
import { RainMaterial } from './material'

function Drop() {
  const ref = useRef()

  const [height] = useState(() => Math.random())

  const [aPosition] = useState(() => [
    Math.random(),
    Math.random(),
    Math.random(),
    Math.random(),
  ])

  return (
    <Instance
      rotation={[0, Math.PI / 2, 0]}
      scale={[0.005, height, 0.005]}
      ref={ref}
      aPosition={aPosition}
    />
  )
}

export default function Rain() {
  const ref = useRef()
  const debugRef = useRef()

  const [material] = useState(
    () =>
      new RainMaterial({
        color: 'white',
        opacity: 0.25,
        transparent: true,
      })
  )

  const isStudio = useStudio()

  const sheet = useCurrentSheet()
  useTheatre(
    sheet,
    'rain',
    {
      color: types.rgba({
        r: 0.7,
        g: 0.7,
        b: 0.7,
        a: 1,
      }),
      opacity: types.number(0.25, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),

      speed: types.number(50, {
        range: [0, 100],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ opacity, color, speed }) => {
        material.opacity = opacity
        material.color.set(color.toString().slice(0, 7))
        material.speed = speed
      },
      deps: [isStudio],
    }
  )

  useTheatre(
    sheet,
    'rain / matrix',
    {
      position: {
        x: 80,
        y: -1,
        z: -25,
      },
      rotation: {
        x: types.number(-0.2, {
          range: [-Math.PI / 2, Math.PI / 2],
        }),
        y: types.number(0, {
          range: [-Math.PI / 2, Math.PI / 2],
        }),
        z: types.number(0, {
          range: [-Math.PI / 2, Math.PI / 2],
        }),
      },
      scale: {
        x: types.number(20, {
          range: [0, 50],
        }),
        y: types.number(20, {
          range: [0, 50],
        }),
        z: types.number(20, {
          range: [0, 50],
        }),
      },
      debug: types.boolean(false),
    },
    {
      onValuesChange: ({ position, rotation, scale, debug }) => {
        ref.current.position.set(position.x, position.y, position.z)
        ref.current.rotation.set(rotation.x, rotation.y, rotation.z)
        ref.current.updateMatrix()

        material.setScale(scale.x, scale.y, scale.z)

        debugRef.current.scale.set(scale.x, scale.y, scale.z)
        debugRef.current.position.set(
          position.x + scale.x / 2,
          position.y + scale.y / 2 + 2,
          position.z + scale.z / 2 - 2
        )
        debugRef.current.rotation.set(rotation.x, rotation.y, rotation.z)
        debugRef.current.updateMatrix()

        debugRef.current.visible = Boolean(isStudio && debug)
      },
      deps: [isStudio],
    }
  )

  useFrame((_, deltaTime) => {
    material.raf(deltaTime)
  })

  return (
    <>
      <group visible={Boolean(isStudio)} userData={{ debug: true }}>
        <mesh ref={debugRef} matrixAutoUpdate={false}>
          <boxGeometry />
          <meshBasicMaterial color="red" wireframe />
        </mesh>
      </group>
      <Instances
        count={1000}
        limit={1000}
        frustumCulled={false}
        ref={ref}
        frames={1}
        matrixAutoUpdate={false}
      >
        <InstancedAttribute name="aPosition" defaultValue={[0, 0, 0, 0]} />
        <boxGeometry />
        <primitive object={material} />

        {Array.from({ length: 1000 }).map((_, i) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
          <Drop key={i} />
        ))}
      </Instances>
    </>
  )
}
