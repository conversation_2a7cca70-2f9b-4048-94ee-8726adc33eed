import { MeshBasicMaterial, ShaderChunk, Vector3 } from 'three'

const RANDOM = Math.random()

export class RainMaterial extends MeshBasicMaterial {
  constructor({ ...props } = {}) {
    super({ ...props })

    this.uniforms = {
      ...this.uniforms,
      uScale: { value: new Vector3(1, 1, 1) },
      uTime: { value: 0 },
      uSpeed: { value: 1 },
    }

    this.defines = {
      ...this.defines,
      USE_INSTANCING: '',
    }

    this.customProgramCacheKey = () =>
      this.constructor.name + (process.env.NODE_ENV === 'development' && RANDOM)
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.vertexShader = shader.vertexShader.replace(
      'void main() {',
      /*glsl*/ `
      attribute vec4 aPosition;
      uniform vec3 uScale;
      uniform float uTime;
      uniform float uSpeed;

      void main() {`
    )

    shader.vertexShader = shader.vertexShader.replace(
      '#include <project_vertex>',
      ShaderChunk.project_vertex.replace(
        /*glsl*/ `mvPosition = instanceMatrix * mvPosition;`,
        /*glsl*/ `
        mat4 iMatrix = instanceMatrix;

        vec4 iPosition = vec4(aPosition.xyz * uScale, 0.0);
        iPosition.y -= uTime;
        iPosition.y = mod(iPosition.y, uScale.y);
        iMatrix[3] += iPosition;
        mvPosition = iMatrix * mvPosition;
        `
      )
    )
  }

  raf(deltaTime) {
    this.uniforms.uTime.value += deltaTime * this.speed
  }

  set speed(value) {
    this.uniforms.uSpeed.value = value
  }

  get speed() {
    return this.uniforms.uSpeed.value
  }

  setScale(x = 1, y = 1, z = 1) {
    this.uniforms.uScale.value.set(x, y, z)
  }
}
