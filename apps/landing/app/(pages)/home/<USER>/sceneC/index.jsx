import { useGLTF } from '@react-three/drei'
import { useState } from 'react'
import { DoubleSide, MeshBasicMaterial } from 'three'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { updateTextures } from '~/libs/webgl/utils/textures'
import { Fog } from '../fog'

export function SceneC() {
  const {
    nodes: {
      Big_trees: bigTreesNode,
      close_plants: closePlantsNode,
      ground: groundNode,
      plants_far: plantsFarNode,
      Small_trees: smallTreesNode,
      tree_trunk: treeTrunkNode,
      Camera_scene_C: cameraNode,
    },
  } = useGLTF('/models/SceneC005.glb')

  const [bigTreesGeometry] = useState(() => bigTreesNode.geometry.clone())

  const [closePlantsGeometry] = useState(() => closePlantsNode.geometry.clone())

  const [plantsFarGeometry] = useState(() => plantsFarNode.geometry.clone())

  const [smallTreesGeometry] = useState(() => smallTreesNode.geometry.clone())

  const [treeTrunkGeometry] = useState(() => treeTrunkNode.geometry.clone())

  const bigTreesTexture = useProgressiveKTX2(
    '/textures/sceneC/Big_trees_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const closePlantsTexture = useProgressiveKTX2(
    '/textures/sceneC/close_plants_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const groundTexture = useProgressiveKTX2(
    '/textures/sceneC/ground_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const plantsFarTexture = useProgressiveKTX2(
    '/textures/sceneC/plants_far_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const smallTreesTexture = useProgressiveKTX2(
    '/textures/sceneC/Small_trees_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const treeTrunkTexture = useProgressiveKTX2(
    '/textures/sceneC/tree_trunk_Bake1_CyclesBake_COMBINED.ktx2'
  )

  const bigTreesAlpha = useProgressiveKTX2(
    '/textures/sceneC/Big_trees_Bake1_PBR_Alpha.ktx2'
  )
  const closePlantsAlpha = useProgressiveKTX2(
    '/textures/sceneC/close_plants_Bake1_PBR_Alpha.ktx2'
  )
  const groundAlpha = useProgressiveKTX2(
    '/textures/sceneC/ground_Bake1_PBR_Alpha.ktx2'
  )
  const plantsFarAlpha = useProgressiveKTX2(
    '/textures/sceneC/plants_far_Bake1_PBR_Alpha.ktx2'
  )
  const smallTreesAlpha = useProgressiveKTX2(
    '/textures/sceneC/Small_trees_Bake1_PBR_Alpha.ktx2'
  )
  // const treeTrunkAlpha = useProgressiveKTX2(
  //   '/textures/sceneC/tree_trunk_Bake1_PBR_Alpha.ktx2'
  // )

  updateTextures([
    bigTreesTexture,
    closePlantsTexture,
    groundTexture,
    plantsFarTexture,
    smallTreesTexture,
    treeTrunkTexture,
    bigTreesAlpha,
    closePlantsAlpha,
    groundAlpha,
    plantsFarAlpha,
    smallTreesAlpha,
    // treeTrunkAlpha,
  ])

  const [bigTreesMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: bigTreesTexture,
        alphaMap: bigTreesAlpha,
        alphaTest: 0.25,
        side: DoubleSide,
      })
  )
  const [closePlantsMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: closePlantsTexture,
        alphaMap: closePlantsAlpha,
        alphaTest: 0.25,
        side: DoubleSide,
      })
  )
  const [groundMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: groundTexture,
        alphaMap: groundAlpha,
        alphaTest: 0.25,
        side: DoubleSide,
      })
  )
  const [plantsFarMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: plantsFarTexture,
        alphaMap: plantsFarAlpha,
        alphaTest: 0.25,
        side: DoubleSide,
      })
  )
  const [smallTreesMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: smallTreesTexture,
        alphaMap: smallTreesAlpha,
        alphaTest: 0.25,
        side: DoubleSide,
      })
  )
  const [treeTrunkMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: treeTrunkTexture,
        // alphaMap: treeTrunkAlpha,
        // alphaTest: 0.25,
        // side: BackSide,
      })
  )
  return (
    <>
      <Fog theatreKey="fog" background far={15} near={0} />
      <mesh>
        <primitive object={bigTreesGeometry} />
        <primitive object={bigTreesMaterial} />
      </mesh>
      <mesh>
        <primitive object={closePlantsGeometry} />
        <primitive object={closePlantsMaterial} />
      </mesh>
      <mesh>
        <primitive object={plantsFarGeometry} />
        <primitive object={plantsFarMaterial} />
      </mesh>
      <mesh>
        <primitive object={smallTreesGeometry} />
        <primitive object={smallTreesMaterial} />
      </mesh>
      <mesh>
        <primitive object={treeTrunkGeometry} />
        <primitive object={treeTrunkMaterial} />
      </mesh>
    </>
  )
}
