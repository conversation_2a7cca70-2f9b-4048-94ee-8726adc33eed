import { types } from '@theatre/core'
import { useRef, useState } from 'react'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { MeshReflectorMaterial } from '../mesh-reflector-material'

export function Water() {
  const materialRef = useRef()

  const sheet = useCurrentSheet()

  const { isMobile } = useDeviceDetection()

  const [blur, setBlur] = useState(0)
  const [mixBlur, setMixBlur] = useState(0)
  const [mixStrength, setMixStrength] = useState(1)
  const [mixContrast, setMixContrast] = useState(1)
  const [resolution, setResolution] = useState(512)
  const [mirror, setMirror] = useState(0)
  const [depthScale, setDepthScale] = useState(0)
  const [minDepthThreshold, setMinDepthThreshold] = useState(0.9)
  const [maxDepthThreshold, setMaxDepthThreshold] = useState(1)
  const [depthToBlurRatioBias, setDepthToBlurRatioBias] = useState(0.25)
  const [reflectorOffset, setReflectorOffset] = useState(0.2)
  const [color, setColor] = useState('#ffffff')
  const [metalness, setMetalness] = useState(1)
  const [roughness, setRoughness] = useState(0.5)

  useTheatre(
    sheet,
    'water',
    {
      color: types.rgba({
        r: 1,
        g: 1,
        b: 1,
        a: 1,
      }),
      metalness: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      roughness: types.number(0.5, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      blur: types.number(0, {
        range: [0, 1000],
        nudgeMultiplier: 1,
      }),
      mixBlur: types.number(0, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      mixStrength: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      mixContrast: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      resolution: types.number(512, {
        range: [0, 1024],
        nudgeMultiplier: 1,
      }),
      mirror: types.number(0, {
        range: [0, 1],
        nudgeMultiplier: 1,
      }),
      depthScale: types.number(0, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      minDepthThreshold: types.number(0.9, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      maxDepthThreshold: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      depthToBlurRatioBias: types.number(0.25, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      reflectorOffset: types.number(0.2, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({
        metalness,
        roughness,
        blur,
        mixBlur,
        mixStrength,
        mixContrast,
        resolution,
        mirror,
        depthScale,
        minDepthThreshold,
        maxDepthThreshold,
        depthToBlurRatioBias,
        reflectorOffset,
        color,
      }) => {
        setMetalness(metalness)
        setRoughness(roughness)
        setBlur(blur)
        setMixBlur(mixBlur)
        setMixStrength(mixStrength)
        setMixContrast(mixContrast)
        setResolution(resolution)
        setMirror(mirror)
        setDepthScale(depthScale)
        setMinDepthThreshold(minDepthThreshold)
        setMaxDepthThreshold(maxDepthThreshold)
        setDepthToBlurRatioBias(depthToBlurRatioBias)
        setReflectorOffset(reflectorOffset)
        setColor(color.toString())
      },
    }
  )

  return (
    <>
      <mesh
        scale={110}
        rotation={[-Math.PI / 2, 0, 0]}
        position={[50, -1, 0]}
        matrixAutoUpdate={false}
        ref={(node) => {
          node?.updateMatrix()
        }}
      >
        <planeGeometry />
        {isMobile ? (
          <meshBasicMaterial color={color} opacity={0.3} transparent />
        ) : (
          <MeshReflectorMaterial
            ref={materialRef}
            blur={blur} // Blur ground reflections (width, height), 0 skips blur
            mixBlur={mixBlur} // How much blur mixes with surface roughness (default = 1)
            mixStrength={mixStrength} // Strength of the reflections
            mixContrast={mixContrast} // Contrast of the reflections
            resolution={resolution} // Off-buffer resolution, lower=faster, higher=better quality, slower
            mirror={mirror} // Mirror environment, 0 = texture colors, 1 = pick up env colors
            depthScale={depthScale} // Scale the depth factor (0 = no depth, default = 0)
            minDepthThreshold={minDepthThreshold} // Lower edge for the depthTexture interpolation (default = 0)
            maxDepthThreshold={maxDepthThreshold} // Upper edge for the depthTexture interpolation (default = 0)
            depthToBlurRatioBias={depthToBlurRatioBias} // Adds a bias factor to the depthTexture before calculating the blur amount [blurFactor = blurTexture * (depthTexture + bias)]. It accepts values between 0 and 1, default is 0.25. An amount > 0 of bias makes sure that the blurTexture is not too sharp because of the multiplication with the depthTexture
            // distortion={1} // Amount of distortion based on the distortionMap texture
            // distortionMap={distortionTexture} // The red channel of this texture is used as the distortion map. Default is null
            reflectorOffset={reflectorOffset} // Offsets the virtual camera that projects the reflection. Useful when the reflective surface is some distance from the object's origin (default = 0)
            color={color}
            metalness={metalness}
            roughness={roughness}
          />
        )}
      </mesh>
    </>
  )
}
