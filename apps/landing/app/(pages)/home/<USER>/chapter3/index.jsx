'use client'

import { val } from '@theatre/core'
import cn from 'clsx'
import { useRect } from 'hamo'
import dynamic from 'next/dynamic'
import { useRef, useState } from 'react'
import * as ScrollSpy from '~/app/(pages)/(components)/scroll-spy'
import { Description } from '~/components/description'
import { TextFrame } from '~/components/text-frame'
import { useScrollTrigger } from '~/hooks/use-scroll-trigger'
import { SheetProvider } from '~/libs/theatre'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import WebGLText from '~/libs/webgl/components/text'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import s from './chapter3.module.css'

const WebGLChapter3 = dynamic(
  () => import('./webgl').then(({ WebGLChapter3 }) => WebGLChapter3),
  {
    ssr: false,
  }
)

const fontVars = {
  letterSpacing: -0.06,
  lineHeight: 0.9,
  anchorY: 'middle',
  anchorX: 'center',
  color: '#ACFF46',
  font: '/fonts/Denim/DenimINK-Regular.woff',
}

const sheetId = 'chapter3'

export function Chapter3() {
  const [sheet, setSheet] = useState()
  const [setRectRef, rect] = useRect()
  const { getChapterScrollOffset } = ScrollSpy.useScrollSpy()
  const renderRef = useRef(false)

  useScrollTrigger(
    {
      rect,
      start: 'top bottom',
      end: 'bottom top',
      debug: true,
      // onEnter: () => {
      //   renderRef.current = true
      // },
      // onLeave: () => {
      //   renderRef.current = false
      // },
      onProgress: ({ progress, isActive }) => {
        renderRef.current = isActive

        if (sheet) {
          sheet.sequence.position =
            progress * val(sheet.sequence.pointer.length)
        }
      },
    },
    [sheet]
  )

  return (
    <ScrollSpy.Section label="Chapter 3" sheet={sheet}>
      <div
        className={cn('chapter', s.chapter3)}
        ref={(node) => {
          setRectRef(node)
        }}
      >
        <div className="wrapper">
          <div
            className={cn(
              'scene',
              // 'mobile-bkg-pattern',
              // 'animation-glitch-radar',
              s.sceneA
            )}
          >
            <div className="h2">
              <div className={s['top-text']}>
                {/* <h3 className="mobile-only green">A NEW</h3> */}
                <div
                // className="desktop-only"
                >
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 3')}
                    fontVars={fontVars}
                    fontSize={92}
                  >
                    A NEW
                  </WebGLText>
                </div>
              </div>
              <div className={s['middle-text']}>
                {/* <h3 className="mobile-only green">FINANCIAL</h3> */}
                <div
                // className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneAMiddleFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneAMiddleText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 3')}
                    fontVars={fontVars}
                    fontSize={92}
                  >
                    FINANCIAL
                  </WebGLText>
                </div>
              </div>

              <div className={s['bottom-text']}>
                {/* <h3 className="mobile-only green">PRIMITIVE</h3> */}
                <div
                // className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 3')}
                    fontVars={fontVars}
                    fontSize={92}
                  >
                    PRIMITIVE
                  </WebGLText>
                </div>
              </div>
            </div>
            <div className={s.description}>
              <Description
                className="ps-text"
                theaterConfig={{
                  sheet: sheetId,
                  object: 'sceneADescription',
                }}
              >
                Minted through forest proof of work, each unit of ibicash is
                generated by the work done by a verified hectare of forest
                during one day. Supply is hardcapped by forests' existence: no
                forest, no issuance. Growth expands supply; loss halts it.
                Forests gain a tangible monetary unit - a financial lego block
                on top of which novel nature based assets can be built.
              </Description>
              {/* <TertiaryButton
                className="ps"
                theaterConfig={{
                  sheet: sheetId,
                  object: 'sceneAButton',
                }}
                href="https://ibi.cash/"
              >
                Go to the oracle page
              </TertiaryButton> */}
            </div>
          </div>
        </div>

        {/* <div className="mobile-bkg-media">
          <Image
            src="/images/mobile-tmp/chapter3-sceneA.jpg"
            alt="Chapter 3 Scene A"
            fill
            objectFit="cover"
          />
        </div> */}

        <WebGLTunnel>
          <SheetProvider
            id={sheetId}
            ref={(node) => {
              setSheet(node)
            }}
          >
            <ChapterContext
              value={{
                getRender: () => renderRef.current,
                index: 2,
              }}
            >
              <WebGLChapter3 rect={rect} />
            </ChapterContext>
          </SheetProvider>
        </WebGLTunnel>
      </div>
    </ScrollSpy.Section>
  )
}
