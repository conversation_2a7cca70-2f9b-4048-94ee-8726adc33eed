.chapter3 {
  .top-text,
  .bottom-text,
  .middle-text {
    position: relative;
    width: fit-content;

    .frame {
      position: absolute;

      @include-media ('mobile') {
        display: none;
      }
    }
  }

  .sceneA {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: desktop-vw(65px);

    @include-media ('mobile') {
      row-gap: mobile-vw(64px);
      padding-top: mobile-vw(88px);
    }

    .top-text {
      margin-left: calc(columns(1) + 1.25 * var(--layout-margin));

      @include-media ('desktop') {
        padding-top: desktop-vw(25px);
        margin-left: calc(columns(2) + 2 * var(--layout-margin));
      }
    }

    .middle-text {
      margin-left: calc(columns(2) + 1.25 * var(--layout-margin));

      @include-media ('desktop') {
        margin-left: calc(columns(4) + 2 * var(--layout-margin));
      }

      .frame {
        top: desktop-vw(-22px);
        right: desktop-vw(-32px);
        transform: rotate(180deg);
      }
    }

    .bottom-text {
      margin-left: calc(columns(1) + 1.25 * var(--layout-margin));

      @include-media ('desktop') {
        margin-left: calc(columns(3) + 2 * var(--layout-margin));
      }

      .frame {
        position: absolute;
        bottom: desktop-vw(-32px);
        left: desktop-vw(-32px);
      }
    }

    .description {
      display: flex;
      flex-direction: column;
      row-gap: desktop-vw(20px);
      margin-left: calc(columns(7) + 2 * var(--layout-margin));
      max-width: desktop-vw(336px);

      @include-media ('mobile') {
        row-gap: mobile-vw(20px);
        margin-left: calc(columns(2) + var(--layout-margin));
        margin-bottom: mobile-vw(112px);
        max-width: mobile-vw(218px);

        > a {
          opacity: 1 !important;
        }
      }
    }
  }
}
