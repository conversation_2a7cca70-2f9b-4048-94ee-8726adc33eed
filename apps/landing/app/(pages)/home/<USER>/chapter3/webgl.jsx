'use client'

import { types } from '@theatre/core'
import { useContext, useEffect, useRef, useState } from 'react'
import { useAudio } from '~/hooks/use-audio'
import { useCurrentSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { CameraSpline } from '~/libs/webgl/components/camera-spline'
import { Controls } from '~/libs/webgl/components/camera-spline/controls'
import {
  ChapterContext,
  useChaptersStore,
} from '~/libs/webgl/components/postprocessing/use-chapters'
import { RenderTexture } from '~/libs/webgl/components/render-texture'
import { SceneE } from '../sceneE'

export function WebGLChapter3({ rect }) {
  const [texture, setTexture] = useState(null)
  const set = useChaptersStore((state) => state.set)
  const { getRender } = useContext(ChapterContext)

  useEffect(() => {
    set(2, { texture, rect, getRender })
  }, [set, texture, rect, getRender])

  const isStudio = useStudio()

  const howl = useAudio()

  const sound1Ref = useRef(null)

  useEffect(() => {
    sound1Ref.current = howl.play('Sfx_Amb_Chap3_Loop')
    howl.volume(0, sound1Ref.current)

    howl.loop(true, sound1Ref.current)
    return () => {
      howl.stop(sound1Ref.current)
    }
  }, [howl])

  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'sounds',
    {
      Sfx_Amb_Chap3_Loop: types.number(0, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
        label: 'Loop',
      }),
    },
    {
      onValuesChange: ({ Sfx_Amb_Chap3_Loop }) => {
        howl.volume(Sfx_Amb_Chap3_Loop, sound1Ref.current)
      },
    }
  )

  return (
    <>
      {/* <mesh>
        <planeGeometry />
        <meshBasicMaterial> */}
      <RenderTexture
        ref={(node) => {
          setTexture(node)
        }}
      >
        <Controls enabled={isStudio}>
          <CameraSpline origin={[0, 0, 0]} />
          <SceneE />
        </Controls>
        {/* <mesh scale={1} position={[0, 0, -10]}>
          <boxGeometry />
          <meshBasicMaterial color="red" />
        </mesh> */}
      </RenderTexture>
      {/* </meshBasicMaterial>
      </mesh> */}
    </>
  )
}
