import { useThree } from '@react-three/fiber'
import { types } from '@theatre/core'
import { useContext, useRef } from 'react'
import { Color } from 'three'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { IntroContext } from '../intro'

export function Fog({
  theatreKey = 'fog',
  background = false,
  far,
  near,
  color = { r: 0.5, g: 0.5, b: 0.5 },
}) {
  const fogRef = useRef()

  const camera = useThree((state) => state.camera)
  const scene = useThree((state) => state.scene)
  const sheet = useCurrentSheet()
  const { sheet: introSheet } = useContext(IntroContext)

  const config = {
    enabled: true,
    color: types.rgba({
      r: color.r,
      g: color.g,
      b: color.b,
      a: 1,
    }),
    far: far || camera.far,
    near: near || camera.near,
  }

  function onValuesChange({ enabled, color, far, near }) {
    if (!enabled) {
      fogRef.current.far = 1000
      fogRef.current.near = 1000
    } else {
      fogRef.current.color.set(color.toString())
      fogRef.current.far = far
      fogRef.current.near = near
    }

    if (background) {
      if (scene.background instanceof Color) {
        scene.background.set(color.toString())
      } else {
        scene.background = new Color(color.toString())
      }
    }
  }

  useTheatre(sheet, theatreKey, config, {
    onValuesChange,
  })

  useTheatre(introSheet, theatreKey, config, {
    onValuesChange,
  })

  return <fog ref={fogRef} attach="fog" />
}
