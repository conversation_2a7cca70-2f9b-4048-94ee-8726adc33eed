import { DoubleSide, MeshBasicMaterial } from 'three'

const RANDOM = Math.random()

export class FogMaskMaterial extends MeshBasicMaterial {
  constructor({ ...props } = {}) {
    super({ transparent: true, side: DoubleSide, ...props })

    this.uniforms = {}

    this.defines = {
      MAP_UV: 'uv',
      USE_MAP: '',
    }

    this.customProgramCacheKey = () =>
      this.constructor.name +
      JSON.stringify(this.options) +
      (process.env.NODE_ENV === 'development' && RANDOM) // allow HMR
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    shader.uniforms = {
      ...shader.uniforms,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      'vec4 diffuseColor = vec4( diffuse, opacity );',
      /* glsl */ `
      float mask = 1. - smoothstep(0.5, 1., vMapUv.y);
      mask = clamp(mask, 0., 1.);
      vec4 diffuseColor = vec4( diffuse, opacity * mask );
      
      `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      '#include <map_fragment>',
      ''
    )
  }
}
