import { Color, MeshBasicMaterial, Vector3 } from 'three'
import {
  getNormalsShader,
  getShadowsShader,
  getTimeShader,
  getWorldPositionShader,
} from '~/libs/webgl/utils/extend-shader'

const RANDOM = Math.random()

export class TerrainMaterial extends MeshBasicMaterial {
  constructor({ ...props } = {}) {
    super({ ...props })

    this.shadows = getShadowsShader()
    this.normals = getNormalsShader()
    this.worldPosition = getWorldPositionShader()
    this.time = getTimeShader()

    this.uniforms = {
      ...this.uniforms,
      // Textures
      uGridScale: { value: 1 },
      uGridDebug: { value: false },
      uGridDivisions: { value: 1 },
      uGridThickness: { value: 1 },
      uGridColor: { value: new Color(1, 1, 1) },
      uGridLimit: { value: 5 },
      uOverlayColor: { value: new Color(1, 1, 1) },
      uOverlayOpacity: { value: 1 },
      uDistance: { value: 0 },
      uBlendOpacity: { value: 1 },
      uLineColor: { value: new Color(1, 0, 0) },
      uLineWidth: { value: 0.01 },
      uFadeOpacity: { value: 0.1 },
      uFadeWidth: { value: 0.3 },
      uCameraPosition: { value: new Vector3() },
      ...this.time.uniforms,
      // Shadows
      ...this.shadows.uniforms,
    }

    this.defines = {
      ...this.defines,
      ...this.shadows.defines,
      ...this.normals.defines,
      ...this.worldPosition.defines,
    }

    this.customProgramCacheKey = () =>
      this.constructor.name + (process.env.NODE_ENV === 'development' && RANDOM)
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    this.worldPosition.extend(shader)
    this.shadows.extend(shader)
    this.normals.extend(shader)
    this.time.extend(shader)

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      'void main() {',
      /*glsl*/ `


    float grid(vec2 coord, float thickness) { // https://madebyevan.com/shaders/grid/
      vec2 grid = abs(fract(coord - 0.5) - 0.5) / fwidth(coord);
      float line = min(grid.x, grid.y);
      float color = thickness - min(line,thickness);
      return color;
    }


      uniform float uGridScale;
      uniform bool uGridDebug;
      uniform float uGridDivisions;
      uniform float uGridThickness;
      uniform vec3 uGridColor;
      uniform float uGridLimit;
      uniform vec3 uOverlayColor;
      uniform float uOverlayOpacity;

      uniform float uDistance;
      uniform float uBlendOpacity;
      uniform vec3 uLineColor;
      uniform float uLineWidth;
      uniform float uFadeOpacity;
      uniform float uFadeWidth;
      uniform vec3 uCameraPosition;
      void main() {`
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      '#include <map_fragment>',
      /*glsl*/ `
      #include <map_fragment>

      diffuseColor.rgb = blendNormal(diffuseColor.rgb, uOverlayColor, uOverlayOpacity);

      float y = vWorldPosition.y;
      y = smoothstep(uGridLimit, 1., y);
      float gridIntensity = y * grid(vWorldPosition.xz * (1. / uGridScale), uGridThickness);
      gridIntensity = clamp(gridIntensity, 0., 1.);
      diffuseColor.rgb = blendNormal(diffuseColor.rgb, uGridColor, gridIntensity);
      `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      '#include <fog_fragment>',
      /*glsl*/ `
      #include <fog_fragment>


      float distanceToCamera = distance( vWorldPosition.xyz, uCameraPosition ) / uDistance;

      float gradient = 1.-smoothstep(0.0, uFadeWidth, abs(1. - (distanceToCamera)));
      if(distanceToCamera > 1.0) {
        gradient = 0.;
      }
      gradient *= uFadeOpacity;
      gradient*= gridIntensity;

     
      float line = 1.-smoothstep(0.0, uLineWidth * 5., abs(1. - distanceToCamera)) ;
      line *= gridIntensity;
          gl_FragColor.rgb = mix(gl_FragColor.rgb, uLineColor, uBlendOpacity * max(line, gradient));

          // gl_FragColor.rgb = vec3(1.,0.,0.);
        
        `
    )
  }

  raf(deltaTime) {
    this.time.raf(deltaTime)
  }

  setRendererResolution(x, y) {
    this.uniforms.uRendererResolution.value.set(x, y)
  }

  set cameraPosition(value) {
    this.uniforms.uCameraPosition.value.copy(value)
  }

  get cameraPosition() {
    return this.uniforms.uCameraPosition.value
  }

  set gridFlowmapIntensity(value) {
    this.uniforms.uGridFlowmapIntensity.value = value
  }

  set gridScale(value) {
    this.uniforms.uGridScale.value = value
  }

  set gridDebug(value) {
    this.uniforms.uGridDebug.value = value
  }

  set gridDivisions(value) {
    this.uniforms.uGridDivisions.value = value
  }

  set gridThickness(value) {
    this.uniforms.uGridThickness.value = value
  }

  set gridColor(value) {
    this.uniforms.uGridColor.value.set(value)
  }

  set gridLimit(value) {
    this.uniforms.uGridLimit.value = value
  }

  set overlayColor(value) {
    this.uniforms.uOverlayColor.value.set(value)
  }

  set overlayOpacity(value) {
    this.uniforms.uOverlayOpacity.value = value
  }
  set distance(value) {
    this.uniforms.uDistance.value = value
  }

  get distance() {
    return this.uniforms.uDistance.value
  }

  set blendOpacity(value) {
    this.uniforms.uBlendOpacity.value = value
  }

  get blendOpacity() {
    return this.uniforms.uBlendOpacity.value
  }

  set lineColor(value) {
    this.uniforms.uLineColor.value.set(value)
  }

  get lineColor() {
    return this.uniforms.uLineColor.value
  }

  set lineWidth(value) {
    this.uniforms.uLineWidth.value = value
  }

  get lineWidth() {
    return this.uniforms.uLineWidth.value
  }

  set fadeOpacity(value) {
    this.uniforms.uFadeOpacity.value = value
  }

  get fadeOpacity() {
    return this.uniforms.uFadeOpacity.value
  }

  set fadeWidth(value) {
    this.uniforms.uFadeWidth.value = value
  }

  get fadeWidth() {
    return this.uniforms.uFadeWidth.value
  }
}
