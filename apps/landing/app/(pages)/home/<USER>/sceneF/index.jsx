'use client'

import { useGLTF } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { types, val } from '@theatre/core'
import gsap from 'gsap'
import { useContext, useEffect, useRef, useState } from 'react'
import { useAudio } from '~/hooks/use-audio'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useCurrentSheet, useSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import { updateTextures } from '~/libs/webgl/utils/textures'
import { Fog } from '../fog'
import { Sky } from '../sky'
import { TerrainMaterial } from './material'

// @refresh reset

export function SceneF() {
  const {
    nodes: {
      Landscape_Rework001: terrainNode,
      Landscape_Mesa001: terrainNode2,
    },
  } = useGLTF('/models/Landscape_Rework009.glb')

  // console.log(flowmap)

  const terrainDiffuse = useProgressiveKTX2(
    '/textures/sceneF/Landscape001_Grid_Stoneslate001.ktx2'
  )
  updateTextures([terrainDiffuse])

  const [terrainMaterial] = useState(
    () =>
      new TerrainMaterial({
        map: terrainDiffuse,
      })
  )

  const sheet = useCurrentSheet()

  const size = useThree((state) => state.size)

  useTheatre(
    sheet,
    'terrain',
    {
      color: types.rgba(
        { r: 19 / 255, g: 29 / 255, b: 72 / 255, a: 1 },
        {
          label: 'Color',
        }
      ),
      opacity: types.number(0.8, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
        label: 'Opacity',
      }),
    },
    {
      onValuesChange: ({ color, opacity }) => {
        terrainMaterial.overlayColor = color.toString()
        terrainMaterial.overlayOpacity = opacity
      },
    }
  )

  useTheatre(
    sheet,
    'terrain / grid',
    {
      scale: types.number(2, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
        label: 'Scale',
      }),
      thickness: types.number(1, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
        label: 'Thickness',
      }),
      color: types.rgba(
        { r: 105 / 255, g: 117 / 255, b: 172 / 255, a: 1 },
        {
          label: 'Color',
        }
      ),
      limit: types.number(5, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
        label: 'Limit',
      }),
      debug: types.boolean(false, {
        label: 'Debug',
      }),
    },
    {
      onValuesChange: ({ scale, debug, thickness, color, limit }) => {
        terrainMaterial.gridScale = scale
        terrainMaterial.gridDebug = debug
        terrainMaterial.gridThickness = thickness
        terrainMaterial.gridColor = color.toString()
        terrainMaterial.gridLimit = limit
      },
    }
  )

  const scanlineSheet = useSheet('chapter4 / scanline')

  useTheatre(
    scanlineSheet,
    'scanline',
    {
      distance: types.number(0, {
        range: [0, 1000],
        nudgeMultiplier: 0.01,
      }),
      color: types.rgba({
        r: 172 / 255,
        g: 255 / 255,
        b: 70 / 255,
        a: 1,
      }),
      opacity: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      lineWidth: types.number(0.1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      fadeOpacity: types.number(0.1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      fadeWidth: types.number(0.3, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({
        distance,
        color,
        opacity,
        lineWidth,
        fadeOpacity,
        fadeWidth,
      }) => {
        color = color.toString().slice(0, 7)

        terrainMaterial.distance = distance
        terrainMaterial.lineColor = color
        terrainMaterial.blendOpacity = opacity
        terrainMaterial.lineWidth = lineWidth * 0.1
        terrainMaterial.fadeOpacity = fadeOpacity
        terrainMaterial.fadeWidth = fadeWidth
      },
    }
  )

  const timelineRef = useRef(null)

  const isStudio = useStudio()

  const { getRender } = useContext(ChapterContext)

  const camera = useThree((state) => state.camera)

  const howl = useAudio()

  useEffect(() => {
    const abortController = new AbortController()
    const mainElement = document.querySelector('main')
    // Revert wtih a better fix
    mainElement?.addEventListener(
      'click',
      (e) => {
        if (!getRender()) return

        if (isStudio) {
          if (!e.shiftKey) return

          e.preventDefault()
          e.stopPropagation()
        }

        if (
          typeof timelineRef.current?.progress() === 'number' &&
          timelineRef.current?.progress() < 1
        )
          return

        const timeline = gsap.timeline({})

        const duration = val(scanlineSheet.sequence.pointer.length)

        const proxy = {
          progress: 0,
        }

        camera.getWorldPosition(terrainMaterial.cameraPosition)

        const scannerSound = `Sfx_Scanner${Math.floor(Math.random() * 3 + 1)}`

        const sound = howl.play(scannerSound)
        howl.volume(1, sound)

        timeline.to(proxy, {
          progress: 1,
          duration,
          onUpdate: () => {
            scanlineSheet.sequence.position = proxy.progress * duration
          },
        })

        timelineRef.current = timeline
      },
      {
        signal: abortController.signal,
        capture: true,
      }
    )

    return () => abortController.abort()
  }, [sheet, isStudio, camera, howl])

  return (
    <>
      <Fog
        theatreKey="fog"
        far={250}
        near={0}
        color={{ r: 24 / 255, g: 30 / 255, b: 62 / 255 }}
        background
      />
      {/* <primitive object={cameraNode} ref={cameraNodeRef} /> */}
      <mesh matrixAutoUpdate={false}>
        <primitive object={terrainNode.geometry} attach="geometry" />
        <primitive object={terrainMaterial} attach="material" />
      </mesh>
      <mesh matrixAutoUpdate={false}>
        <primitive object={terrainNode2.geometry} attach="geometry" />
        <primitive object={terrainMaterial} attach="material" />
      </mesh>
      <Sky />
      {/* <Water /> */}
    </>
  )
}
