.chapter4 {
  .top-text,
  .bottom-text,
  .middle-text {
    position: relative;
    width: fit-content;

    .frame {
      position: absolute;

      @include-media ('mobile') {
        display: none;
      }
    }
  }

  .sceneA {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: desktop-vw(60px);

    @include-media ('mobile') {
      row-gap: mobile-vw(64px);
      padding-top: mobile-vw(88px);
    }

    .top-text {
      margin-left: calc(columns(1) + 1.25 * var(--layout-margin));

      @include-media ('desktop') {
        margin-top: desktop-vw(-15px);
        /* margin-left: desktop-vw(457px); */
        margin-left: calc(columns(2) + 2 * var(--layout-margin));
      }

      .frame {
        top: desktop-vw(-48px);
        left: desktop-vw(-16px);
        transform: rotate(90deg);
      }
    }

    /* .middle-text {
      margin-left: calc(columns(2) + 1.25 * var(--layout-margin));

      @include-media ('desktop') {
        margin-left: calc(columns(2) + 2 * var(--layout-margin));
      }
    } */

    .bottom-text {
      margin-left: calc(columns(1) + 1.25 * var(--layout-margin));

      @include-media ('desktop') {
        /* margin-left: calc(columns(5) + 2 * var(--layout-margin)); */
        margin-left: calc(columns(3) + 2 * var(--layout-margin));
      }

      .frame {
        bottom: desktop-vw(-48px);
        right: desktop-vw(-16px);
        transform: rotate(270deg);
      }
    }

    .description {
      /* margin-left: desktop-vw(462px); */
      margin-left: calc(columns(3) + 2.15 * var(--layout-margin));
      max-width: desktop-vw(330px);

      @include-media ('mobile') {
        margin-left: calc(columns(2) + var(--layout-margin));
        margin-bottom: mobile-vw(112px);
        max-width: mobile-vw(218px);
      }
    }
  }
}
