'use client'

import { val } from '@theatre/core'
import cn from 'clsx'
import { useRect } from 'hamo'
import dynamic from 'next/dynamic'
import { useRef, useState } from 'react'
import * as ScrollSpy from '~/app/(pages)/(components)/scroll-spy'
import { CursorButton, CursorTunnel } from '~/components/cursor'
import { Description } from '~/components/description'
import { TextFrame } from '~/components/text-frame'
import { useScrollTrigger } from '~/hooks/use-scroll-trigger'
import { SheetProvider } from '~/libs/theatre'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import WebGLText from '~/libs/webgl/components/text'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import s from './chapter4.module.css'

const WebGLChapter4 = dynamic(
  () => import('./webgl').then(({ WebGLChapter4 }) => WebGLChapter4),
  {
    ssr: false,
  }
)

const fontVars = {
  letterSpacing: -0.06,
  lineHeight: 0.9,
  anchorY: 'middle',
  anchorX: 'center',
  color: '#ACFF46',
  font: '/fonts/Denim/DenimINK-Regular.woff',
}

const sheetId = 'chapter4'

export function Chapter4() {
  const sceneRef = useRef()
  const [sheet, setSheet] = useState()
  const [setRectRef, rect] = useRect()
  const { getChapterScrollOffset } = ScrollSpy.useScrollSpy()
  const renderRef = useRef(false)

  useScrollTrigger(
    {
      rect,
      start: 'top bottom',
      end: 'bottom top',
      debug: true,
      // onEnter: () => {
      //   renderRef.current = true
      // },
      // onLeave: () => {
      //   renderRef.current = false
      // },
      onProgress: ({ progress, isActive }) => {
        renderRef.current = isActive

        if (sheet) {
          sheet.sequence.position =
            progress * val(sheet.sequence.pointer.length)
        }
      },
    },
    [sheet]
  )

  return (
    <ScrollSpy.Section label="Chapter 4" sheet={sheet}>
      <div
        className={cn('chapter', s.chapter4)}
        ref={(node) => {
          setRectRef(node)
        }}
      >
        <div className="wrapper">
          <div
            className={cn(
              'scene',
              // 'mobile-bkg-pattern',
              // 'animation-glitch-borders',
              s.sceneA
            )}
            ref={sceneRef}
          >
            <div className="h2">
              <div className={s['top-text']}>
                {/* <h3 className="mobile-only green">DIGITAL</h3> */}
                <div
                // className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 4')}
                    fontVars={fontVars}
                    fontSize={92}
                  >
                    DIGITAL
                  </WebGLText>
                </div>
              </div>

              {/* <div className={s['middle-text']}>
                <h3 className="mobile-only green">OPERATING</h3>
                <div className="desktop-only">
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneAMiddleText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 4')}
                    fontVars={fontVars}
                    fontSize={92}
                  >
                    OPERATING
                  </WebGLText>
                </div>
              </div> */}

              <div className={s['bottom-text']}>
                {/* <h3 className="mobile-only green">EMERGENCE</h3> */}
                <div
                // className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 4')}
                    fontVars={fontVars}
                    fontSize={92}
                  >
                    EMERGENCE
                  </WebGLText>
                </div>
              </div>
            </div>
            <Description
              className={cn('ps-text', s.description)}
              theaterConfig={{
                sheet: sheetId,
                object: 'sceneADescription',
              }}
            >
              Nature instantiated in code. Not symbolically, but functionally.
              Forests become executable agents, regulating supply through
              verifiable presence. A new kind of ontology: biological reality
              expressed as autonomous economic logic. No override; just forests,
              self-enforcing, onchain.
            </Description>
          </div>
        </div>

        <WebGLTunnel>
          <SheetProvider
            id={sheetId}
            ref={(node) => {
              setSheet(node)
            }}
          >
            <ChapterContext
              value={{
                getRender: () => renderRef.current,
                index: 3,
              }}
            >
              <WebGLChapter4 rect={rect} />
            </ChapterContext>
          </SheetProvider>
        </WebGLTunnel>
      </div>
      <CursorTunnel>
        <CursorButton
          sheetId={sheetId}
          onEnter={() => {
            sceneRef.current?.style?.setProperty('cursor', 'pointer')
          }}
          onLeave={() => {
            sceneRef.current?.style?.setProperty('cursor', 'auto')
          }}
        >
          CLICK TO SCAN THE GRID
        </CursorButton>
      </CursorTunnel>
    </ScrollSpy.Section>
  )
}
