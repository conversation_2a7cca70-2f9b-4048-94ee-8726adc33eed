import { Color, ShaderMaterial } from 'three'

const vertexShader = /* glsl */ `
varying vec2 vUv;
uniform float uCurvature;

void main() {
  vUv = uv;
  vec3 transformedPosition = position;

  // transformedPosition.z += sin(uv.x * 3.1415 * 10.);
  transformedPosition.z = -sin(uv.x * 3.1415) * uCurvature;


  gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(transformedPosition, 1.0);
}
`

const fragmentShader = /* glsl */ `
    varying vec2 vUv;
    uniform vec3 uColor1;
    uniform vec3 uColor2;
    uniform float uEasing;

    void main() {
        float y = vUv.y;
        y = smoothstep(0.0, uEasing, y);
        vec3 color = mix(uColor1, uColor2, y);
        gl_FragColor = vec4(color, 1.0);
    }
`

export class SkyMaterial extends ShaderMaterial {
  constructor() {
    super({
      vertexShader,
      fragmentShader,
      uniforms: {
        uCurvature: { value: 0 },
        uColor1: { value: new Color(0, 0, 0) },
        uColor2: { value: new Color(0, 0, 0) },
        uEasing: { value: 1 },
      },
    })
  }

  get color1() {
    return this.uniforms.uColor1.value
  }

  set color1(value) {
    this.uniforms.uColor1.value.set(value)
  }

  get color2() {
    return this.uniforms.uColor2.value
  }

  set color2(value) {
    this.uniforms.uColor2.value.set(value)
  }

  get easing() {
    return this.uniforms.uEasing.value
  }

  set easing(value) {
    this.uniforms.uEasing.value = value
  }

  get curvature() {
    return this.uniforms.uCurvature.value
  }

  set curvature(value) {
    this.uniforms.uCurvature.value = value
  }
}
