import { types } from '@theatre/core'
import { useEffect, useRef, useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import {
  useStudio,
  useStudioCurrentObject,
} from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useControlsStore } from '~/libs/webgl/components/camera-spline/controls'
import { SkyMaterial } from './material'

export function Sky() {
  const setObject = useControlsStore((state) => state.setObject)
  const object = useControlsStore((state) => state.object)
  const setNextMode = useControlsStore((state) => state.setNextMode)

  const sheet = useCurrentSheet()

  const ref = useRef()

  const [material] = useState(() => new SkyMaterial())

  const { set, object: objectTheatre } = useTheatre(
    sheet,
    'sky / matrix',
    {
      position: {
        x: types.number(-300, { nudgeMultiplier: 0.01 }),
        y: types.number(40, { nudgeMultiplier: 0.01 }),
        z: types.number(0, { nudgeMultiplier: 0.01 }),
      },
      rotation: {
        x: types.number(0, { nudgeMultiplier: 0.01 }),
        y: types.number(Math.PI / 2, {
          nudgeMultiplier: 0.01,
          range: [-Math.PI / 2, Math.PI / 2],
        }),
        z: types.number(0, { nudgeMultiplier: 0.01 }),
      },
      scale: {
        x: types.number(300, { nudgeMultiplier: 0.01 }),
        y: types.number(150, { nudgeMultiplier: 0.01 }),
        z: types.number(1, { nudgeMultiplier: 0.01 }),
      },
    },
    {
      onValuesChange: ({ position, scale, rotation }) => {
        ref.current.scale.set(scale.x, scale.y, 1)
        ref.current.position.set(position.x, position.y, position.z)
        ref.current.rotation.set(rotation.x, rotation.y, rotation.z)
        ref.current.updateMatrix()
      },
    }
  )

  useTheatre(
    sheet,
    'sky',
    {
      color1: types.rgba({ r: 24 / 255, g: 30 / 255, b: 62 / 255, a: 1 }),
      color2: types.rgba({ r: 0, g: 0, b: 0, a: 1 }),
      easing: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
        label: 'Easing',
      }),
      curvature: types.number(200, {
        range: [0, 500],
        nudgeMultiplier: 1,
        label: 'Curvature',
      }),
    },
    {
      onValuesChange: ({ color1, color2, easing, curvature }) => {
        material.color1 = color1.toString()
        material.color2 = color2.toString()
        material.easing = easing
        material.curvature = curvature
      },
    }
  )

  const studio = useStudio()
  const currentObject = useStudioCurrentObject()

  useEffect(() => {
    if (
      currentObject?.objectKey === 'stars' &&
      sheet.address.sheetId === currentObject?.sheetId
    ) {
      setObject(ref.current?.id)
    }
  }, [currentObject, setObject, sheet])

  useEffect(() => {
    if (object === ref.current?.id) {
      studio?.setSelection([objectTheatre])
    }
  }, [object, studio, objectTheatre])

  return (
    <>
      {/* biome-ignore lint/a11y/useKeyWithClickEvents: we need to trigger on click */}
      <mesh
        matrixAutoUpdate={false}
        ref={ref}
        onClick={(e) => {
          e.stopPropagation()
          setObject(e.object.id)
        }}
        onPointerMissed={(e) => {
          e.type === 'click' && setObject(null)
        }}
        // onPointerOver={(e) => {
        //   e.stopPropagation()
        // }}
        onContextMenu={(e) => {
          const isCurrentObject = object === ref.current?.id
          if (isCurrentObject) {
            setNextMode()
          }
        }}
        onTransformControlsMove={(e) => {
          if (!ref.current) return

          ref.current?.updateMatrix()
        }}
        onTransformControlsRelease={(e) => {
          if (!ref.current) return

          set({
            position: {
              x: ref.current.position.x,
              y: ref.current.position.y,
              z: ref.current.position.z,
            },
            scale: {
              x: ref.current.scale.x,
              y: ref.current.scale.y,
              z: ref.current.scale.z,
            },
            rotation: {
              x: ref.current.rotation.x,
              y: ref.current.rotation.y,
              z: ref.current.rotation.z,
            },
          })
        }}
      >
        <planeGeometry args={[1, 1, 16, 1]} />
        <primitive object={material} />
      </mesh>
    </>
  )
}
