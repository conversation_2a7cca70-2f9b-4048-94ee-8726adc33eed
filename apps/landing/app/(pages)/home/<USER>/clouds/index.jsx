import { useFrame } from '@react-three/fiber'
import { types } from '@theatre/core'
import { useContext, useEffect, useState } from 'react'
import { MirroredRepeatWrapping } from 'three'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { IntroContext } from '../intro'
import { CloudsMaterial } from './material'

export default function Clouds({
  theatreKey = 'clouds',
  opacity = 0.2,
  speed = 5,
  frequency = 5,
  alphaMap = false,
  ...props
}) {
  // const noiseTexture = useTexture('/textures/noise/noise.png')
  const noiseTexture = useProgressiveKTX2('/textures/noise/noise.ktx2')
  const alphaTexture = useProgressiveKTX2('/textures/sceneA/alphamap.ktx2')

  const [material] = useState(
    () =>
      new CloudsMaterial({
        opacity: opacity,
        fade: !alphaMap,
        alphaTexture: alphaMap ? alphaTexture : undefined,
      })
  )

  useEffect(() => {
    if (alphaMap) {
      material.alphaTexture = alphaTexture
    }
  }, [alphaTexture, material, alphaMap])

  useEffect(() => {
    noiseTexture.wrapS = noiseTexture.wrapT = MirroredRepeatWrapping
    noiseTexture.needsUpdate = true
    material.noiseTexture = noiseTexture
  }, [noiseTexture, material])

  useFrame((_, deltaTime) => {
    material.raf(deltaTime)
  })

  const sheet = useCurrentSheet()

  const { sheet: introSheet } = useContext(IntroContext)

  const config = {
    opacity: types.number(material.opacity, {
      range: [0, 1],
    }),
    frequency: types.number(frequency, {
      range: [0, 10],
      nudgeMultiplier: 0.01,
    }),
    speed: types.number(speed, {
      range: [-10, 10],
      nudgeMultiplier: 0.01,
    }),
  }

  const onValuesChange = ({ opacity, frequency, speed }) => {
    material.opacity = opacity
    material.frequency = frequency * 0.001
    material.speed = speed * 0.001
  }

  useTheatre(sheet, theatreKey, config, {
    onValuesChange,
  })

  useTheatre(introSheet, theatreKey, config, {
    onValuesChange,
  })

  return (
    <mesh
      {...props}
      rotation={[Math.PI / 2, 0, -Math.PI / 2]}
      scale={alphaMap ? 312 : 1000}
      matrixAutoUpdate={false}
      ref={(node) => {
        if (node) {
          node.updateMatrix()
        }
      }}
    >
      <planeGeometry />
      <primitive object={material} attach="material" />
    </mesh>
  )
}
