import { DoubleSide, MeshBasicMaterial, Vector3 } from 'three'
import {
  getShadowsShader,
  getTimeShader,
  getWorldPositionShader,
} from '~/libs/webgl/utils/extend-shader'

const RANDOM = Math.random()

export class CloudsMaterial extends MeshBasicMaterial {
  constructor({
    world = true,
    shadows = true,
    fade = false,
    alphaTexture,
    maskPoint = new Vector3(1000, 1000, 1000),
    ...props
  }) {
    super({
      transparent: true,
      side: DoubleSide,
      depthWrite: false,
      ...props,
    })

    this.options = {
      world: <PERSON><PERSON>an(world),
      fade: <PERSON><PERSON><PERSON>(fade),
      alphaTexture: <PERSON><PERSON><PERSON>(alphaTexture),
      maskPoint: maskPoint.toArray(),
    }

    this.time = getTimeShader()
    this.shadows = shadows ? getShadowsShader() : null
    this.worldPosition = getWorldPositionShader()

    this.uniforms = {
      ...this.uniforms,
      ...this.time.uniforms,
      ...this.shadows?.uniforms,
      ...this.worldPosition.uniforms,
      uNoiseTexture: { value: null },
      uSpeed: { value: 0.1 },
      uFrequency: { value: 0.01 },
      uRandom: { value: Math.random() * 1000 },
      uAlphaTexture: { value: alphaTexture },
      uMaskPoint: { value: maskPoint },
    }

    this.defines = {
      ...this.defines,
      ...this.time.defines,
      ...this.shadows?.defines,
      ...this.worldPosition.defines,
      MAP_UV: 'uv',
      USE_MAP: '',
    }

    this.customProgramCacheKey = () =>
      this.constructor.name +
      JSON.stringify(this.options) +
      (process.env.NODE_ENV === 'development' && RANDOM) // allow HMR
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    this.time.extend(shader)
    this.shadows?.extend(shader)
    this.worldPosition.extend(shader)

    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      'void main() {',
      /*glsl*/ `
      uniform float uSpeed;
      uniform sampler2D uNoiseTexture;
      uniform float uFrequency;
      uniform float uRandom;
      uniform sampler2D uAlphaTexture;
      uniform vec3 uMaskPoint;

      void main() {
      `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      'vec4 diffuseColor = vec4( diffuse, opacity );',
      /*glsl*/ `
      float noise = texture2D( uNoiseTexture, (${this.options.world ? '(vWorldPosition.xz + uRandom)' : '(vMapUv + uRandom)'}  * uFrequency) + vec2((uTime + uRandom) * uSpeed,0.) ).r;
      float mask = 1. - distance(vec2(.5), vMapUv);
      mask = smoothstep(0.5, 1., mask);

      ${!this.options.fade ? 'mask = 1.;' : ''}


      float distanceToCamera = length(vWorldPosition.xyz - cameraPosition);
      float distanceToCameraNormalized = distanceToCamera / 10.;

      float alpha = texture2D( uAlphaTexture, vMapUv ).r;
      alpha = smoothstep(0.01, 0.1, alpha);
      alpha = clamp(alpha, 0., 1.);

      alpha = ${this.options.alphaTexture ? 'alpha;' : '1.'};
      // float linearMask = smoothstep(0.25, 1., 1. - vMapUv.y);

      vec3 maskPoint = uMaskPoint;
      float maskPointDistance = length(vWorldPosition.xyz - maskPoint);
      maskPointDistance = maskPointDistance / 20.;
      maskPointDistance = smoothstep(0.0, 1., maskPointDistance);
      maskPointDistance = clamp(maskPointDistance, 0., 1.);


      vec4 diffuseColor = vec4( diffuse, (opacity * noise * mask * (smoothstep(0., 1., distanceToCameraNormalized))) * alpha * maskPointDistance );

    //  diffuseColor = vec4( diffuse, alpha );

      // diffuseColor = vec4(vec3(alpha), 0.5);

      

      // diffuseColor = vec4(vec3(1.,0.,0.), hideCenterDistanceNormalized);
        
      `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      '#include <map_fragment>',
      ''
    )
  }

  set noiseTexture(value) {
    this.uniforms.uNoiseTexture.value = value
  }

  raf(deltaTime) {
    this.time.raf(deltaTime)
  }

  set frequency(value) {
    this.uniforms.uFrequency.value = value
  }

  get frequency() {
    return this.uniforms.uFrequency.value
  }

  set speed(value) {
    this.uniforms.uSpeed.value = value
  }

  get speed() {
    return this.uniforms.uSpeed.value
  }

  set alphaTexture(value) {
    this.uniforms.uAlphaTexture.value = value
  }

  get alphaTexture() {
    return this.uniforms.uAlphaTexture.value
  }
}
