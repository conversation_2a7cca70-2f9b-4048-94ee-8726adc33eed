.chapter2 {
  .top-text,
  .bottom-text {
    position: relative;
    width: fit-content;

    .frame {
      position: absolute;

      @include-media ('mobile') {
        display: none;
      }
    }
  }

  .sceneA {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: desktop-vw(145px);

    @include-media ('mobile') {
      row-gap: mobile-vw(64px);
      padding-top: mobile-vw(88px);
    }

    .top-text {
      margin-left: calc(columns(1) + var(--layout-margin));

      @include-media ('desktop') {
        margin-top: desktop-vw(-25px);
        margin-left: calc(columns(3) + var(--layout-margin));
      }

      .frame {
        top: desktop-vw(-32px);
        right: desktop-vw(-54px);
        transform: rotate(180deg);
      }
    }

    .bottom-text {
      margin-left: columns(4);

      @include-media ('desktop') {
        margin-left: calc(columns(4) + 2.5 * var(--layout-margin));
      }

      .frame {
        left: desktop-vw(-48px);
        top: desktop-vw(32px);
      }
    }

    .description {
      margin-left: calc(columns(7) + 3.25 * var(--layout-margin));
      max-width: desktop-vw(302px);

      @include-media ('mobile') {
        margin-left: calc(columns(2) + var(--layout-margin));
        max-width: mobile-vw(218px);
        margin-bottom: mobile-vw(110px);
      }
    }
  }

  .sceneB {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: desktop-vw(150px);

    @include-media ('mobile') {
      row-gap: mobile-vw(64px);
      padding-top: mobile-vw(88px);
    }

    .top-text {
      margin-left: calc(columns(1) + var(--layout-margin));

      @include-media ('desktop') {
        margin-top: desktop-vw(-10px);
        column-gap: desktop-vw(40px);
        margin-left: calc(columns(5) + 2 * var(--layout-margin));
      }

      > div {
        display: flex;
        align-items: center;
        column-gap: columns(1);

        @include-media ('desktop') {
          column-gap: desktop-vw(35px);
        }
      }
    }

    .bottom-text {
      margin-left: calc(columns(3) - 0.7 * var(--layout-margin));

      @include-media ('desktop') {
        margin-left: calc(columns(7) + 2 * var(--layout-margin));
      }

      .frame {
        left: desktop-vw(-48px);
        top: desktop-vw(42px);
      }
    }

    .description {
      margin-left: calc(columns(2) + 3.25 * var(--layout-margin));
      max-width: desktop-vw(300px);

      @include-media ('mobile') {
        margin-bottom: mobile-vw(112px);
        max-width: mobile-vw(217px);
        margin-left: calc(columns(2) + var(--layout-margin));
      }
    }
  }
}
