'use client'

import { types, val } from '@theatre/core'
import cn from 'clsx'
import { useRect } from 'hamo'
import dynamic from 'next/dynamic'
import { useRef, useState } from 'react'
import * as ScrollSpy from '~/app/(pages)/(components)/scroll-spy'
import { CursorButton, CursorTunnel } from '~/components/cursor'
import { Description } from '~/components/description'
import { TextFrame } from '~/components/text-frame'
import { useScrollTrigger } from '~/hooks/use-scroll-trigger'
import { SheetProvider, useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import WebGLText from '~/libs/webgl/components/text'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import { scenePointerEvent } from '../../page'
import s from './chapter2.module.css'

const WebGLChapter2 = dynamic(
  () => import('./webgl').then(({ WebGLChapter2 }) => WebGLChapter2),
  {
    ssr: false,
  }
)

const fontVars = {
  letterSpacing: -0.06,
  lineHeight: 0.9,
  anchorY: 'middle',
  anchorX: 'center',
  color: '#ACFF46',
  font: '/fonts/Denim/DenimINK-Regular.woff',
}

const sheetId = 'chapter2'

export function Chapter2() {
  const [sheet, setSheet] = useState()
  const [setRectRef, rect] = useRect()
  const renderRef = useRef(false)
  const sceneARef = useRef()
  const sceneBRef = useRef()
  const { getChapterScrollOffset } = ScrollSpy.useScrollSpy()

  useScrollTrigger(
    {
      rect,
      start: 'top bottom',
      end: 'bottom top',
      debug: true,

      onProgress: ({ progress, isActive }) => {
        renderRef.current = isActive

        if (sheet) {
          sheet.sequence.position =
            progress * val(sheet.sequence.pointer.length)

          scenePointerEvent(
            sceneARef.current,
            sceneBRef.current,
            progress,
            0.55
          )
        }
      },
    },
    [sheet]
  )

  return (
    <ScrollSpy.Section label="Chapter 2" sheet={sheet}>
      <div
        className={cn('chapter', s.chapter2)}
        ref={(node) => {
          setRectRef(node)
        }}
      >
        <div className="wrapper">
          <div
            className={cn(
              'scene',
              // "mobile-bkg-pattern",
              // "animation-glitch-sideway",
              s.sceneA
            )}
            ref={sceneARef}
          >
            <div className="h2">
              <div className={s['top-text']}>
                {/* <h3 className="mobile-only green">FORESTS</h3> */}
                <div
                //  className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopText',
                    }}
                    fontVars={fontVars}
                    fontSize={92}
                    verticalOffset={getChapterScrollOffset('Chapter 2')}
                  >
                    FOREST
                  </WebGLText>
                </div>
              </div>

              <div className={s['bottom-text']}>
                {/* <h3 className="mobile-only green">LABOR</h3> */}
                <div
                // className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    size={'m'}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomText',
                    }}
                    fontVars={fontVars}
                    fontSize={92}
                    verticalOffset={getChapterScrollOffset('Chapter 2')}
                  >
                    LABOR
                  </WebGLText>
                </div>
              </div>
            </div>
            <Description
              className={cn('ps-text', s.description)}
              theaterConfig={{
                sheet: sheetId,
                object: 'sceneADescription',
              }}
            >
              Forests sustain terrestrial life. Fueled by the sun, their ongoing
              work creates abundance for humanity and countless other organisms
              to flourish. From habitat services to oxygen production, from soil
              conservation to carbon sequestration, forests provide the means of
              production for life.
            </Description>
          </div>

          {/* <div className="mobile-bkg-media">
            <Image
              src="/images/mobile-tmp/chapter2-sceneA.jpg"
              alt="Chapter 2 Scene A"
              fill
              objectFit="cover"
            />
          </div> */}

          <div
            className={cn(
              'scene',
              // 'mobile-bkg-pattern',
              // 'animation-glitch-vertical',
              s.sceneB
            )}
            ref={sceneBRef}
          >
            <div className="h2">
              <div className={s['top-text']}>
                {/* <div className="mobile-only"> */}
                {/* <h3 className="green">MEASURING</h3> */}
                {/* <p className={cn('pxs', s.auxiliaryText)}>
                    [ PROOF OF WORK ]
                  </p> */}
                {/* </div> */}
                <div
                // className="desktop-only"
                >
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBTopText',
                    }}
                    fontVars={fontVars}
                    fontSize={92}
                    verticalOffset={getChapterScrollOffset('Chapter 2')}
                  >
                    MEASURING
                  </WebGLText>
                  {/* <AuxiliaryText
                    className={cn('ps desktop-only', s.auxiliaryText)}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBAuxiliaryText',
                    }}
                  >
                    [ PROOF OF WORK ]
                  </AuxiliaryText> */}
                </div>
              </div>
              <div className={s['bottom-text']}>
                {/* <h3 className="mobile-only green">FORESTS</h3> */}
                <div
                // className="desktop-only"
                >
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBBottomText',
                    }}
                    fontVars={fontVars}
                    fontSize={92}
                    verticalOffset={getChapterScrollOffset('Chapter 2')}
                  >
                    FORESTS
                  </WebGLText>
                  <TextFrame
                    className={s.frame}
                    size={'l'}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBBottomFrame',
                    }}
                  />
                </div>
              </div>
            </div>
            <Description
              className={cn('ps-text', s.description)}
              theaterConfig={{
                sheet: sheetId,
                object: 'sceneBDescription',
              }}
            >
              While digital information is infinitely replicable, reality is
              bound by thermodynamic and ecological limits. ibiCash encodes
              forests' irreproducible work into provably limited issuance.
              Through cryptography and economic incentives, nature's processes
              become the substrate of digital scarcity.
            </Description>
          </div>
        </div>
        {/* <div className="mobile-bkg-media">
          <Image
            src="/images/mobile-tmp/chapter2-sceneB.jpg"
            alt="Chapter 2 Scene B"
            fill
            objectFit="cover"
          />
        </div> */}
        <WebGLTunnel>
          <SheetProvider
            id={sheetId}
            ref={(node) => {
              setSheet(node)
            }}
          >
            <ChapterContext
              value={{
                getRender: () => renderRef.current,
                index: 1,
              }}
            >
              <WebGLChapter2 rect={rect} />
            </ChapterContext>
          </SheetProvider>
        </WebGLTunnel>
      </div>
      <CursorTunnel>
        <CursorButton
          sheetId={sheetId}
          onEnter={() => {
            sceneBRef.current.style.setProperty('cursor', 'pointer')
          }}
          onLeave={() => {
            sceneBRef.current.style.setProperty('cursor', 'auto')
          }}
        >
          CLICK TO SCAN THE FOREST
        </CursorButton>
      </CursorTunnel>
    </ScrollSpy.Section>
  )
}

function AuxiliaryText({ className, children, theaterConfig }) {
  const auxiliaryRef = useRef()
  const sheet = useSheet(theaterConfig?.sheet)

  useTheatre(
    sheet,
    theaterConfig?.object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        auxiliaryRef.current.style.setProperty('opacity', progress)
      },
    }
  )

  return (
    <p className={className} ref={auxiliaryRef}>
      {children}
    </p>
  )
}
