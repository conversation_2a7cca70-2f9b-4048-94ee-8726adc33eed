'use client'

import { val } from '@theatre/core'
import cn from 'clsx'
import { useRect } from 'hamo'
import dynamic from 'next/dynamic'
import { useRef, useState } from 'react'
import * as ScrollSpy from '~/app/(pages)/(components)/scroll-spy'
import { Description } from '~/components/description'
import { TextFrame } from '~/components/text-frame'
import { useScrollTrigger } from '~/hooks/use-scroll-trigger'
import { useStore } from '~/libs/store'
import { SheetProvider } from '~/libs/theatre'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import WebGLText from '~/libs/webgl/components/text'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import { scenePointerEvent } from '../../page'
import s from './chapter1.module.css'

const WebGLChapter1 = dynamic(
  () => import('./webgl').then(({ WebGLChapter1 }) => WebGLChapter1),
  {
    ssr: false,
  }
)

const fontVars = {
  letterSpacing: -0.06,
  lineHeight: 1,
  anchorY: 'middle',
  anchorX: 'center',
  color: '#ACFF46',
  font: '/fonts/Denim/DenimINK-Regular.woff',
}

const sheetId = 'chapter1'

export function Chapter1() {
  const [sheet, setSheet] = useState()
  const [setRectRef, rect] = useRect()
  const { getChapterScrollOffset } = ScrollSpy.useScrollSpy()
  const renderRef = useRef(false)
  const progressRef = useRef(0)
  const sceneARef = useRef()
  const sceneBRef = useRef()

  useScrollTrigger(
    {
      debug: true,
      rect,
      start: 'top bottom',
      end: 'bottom top',
      onProgress: ({ progress, isActive }) => {
        renderRef.current = isActive
        progressRef.current = progress

        if (!useStore.getState().introCompleted) return

        if (sheet) {
          sheet.sequence.position =
            progress * val(sheet.sequence.pointer.length)

          scenePointerEvent(
            sceneARef.current,
            sceneBRef.current,
            progress,
            0.62
          )
        }
      },
    },
    [sheet]
  )

  return (
    <ScrollSpy.Section label="Chapter 1" sheet={sheet}>
      <div
        className={cn('chapter', s.chapter1)}
        ref={(node) => {
          setRectRef(node)
        }}
      >
        <div className="wrapper">
          <div
            className={cn(
              s.sceneA,
              'scene'
              // 'top-bottom-gradients'
            )}
            ref={sceneARef}
          >
            {/* <div className="mobile-only">
              <Image
                src="/images/mobile-tmp/chapter1-sceneA.jpg"
                alt="Chapter 1 Scene A"
                fill
                priority
              />
            </div> */}
            <div className="h1">
              <div className={s['top-text']}>
                {/* <h1 className="mobile-only green">FORESTS</h1> */}
                <div
                // className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneATopText',
                    }}
                    fontVars={fontVars}
                    fontSize={128}
                    verticalOffset={getChapterScrollOffset('Chapter 1')}
                    tag="h1"
                  >
                    FORESTS
                  </WebGLText>
                </div>
              </div>
              <div className={s['bottom-text']}>
                {/* <h1 className="mobile-only green">ONCHAIN</h1> */}
                <div
                //  className="desktop-only"
                >
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneABottomFrame',
                    }}
                  />
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneAbottomText',
                    }}
                    fontVars={fontVars}
                    fontSize={128}
                    verticalOffset={getChapterScrollOffset('Chapter 1')}
                    tag="h1"
                  >
                    ONCHAIN
                  </WebGLText>
                </div>
              </div>
            </div>
            <Description
              className={cn('p', s.description)}
              squareSize="large"
              theaterConfig={{
                sheet: sheetId,
                object: 'sceneADescription',
              }}
            >
              Ibicash Powers forest conservation at internet scale
            </Description>
          </div>
          <div
            className={cn(
              s.sceneB,
              'scene'
              // 'mobile-bkg-pattern',
              // 'top-bottom-gradients'
            )}
            ref={sceneBRef}
          >
            <div className="h2">
              <div className={s['top-text']}>
                {/* <h2 className="mobile-only green">REWARDING</h2> */}
                <div
                // className="desktop-only"
                >
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBtopText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 1')}
                    fontVars={fontVars}
                    fontSize={92}
                    tag="h2"
                  >
                    REWARDING
                  </WebGLText>
                </div>
              </div>
              <div className={cn('h2', s['bottom-text'])}>
                {/* <h2 className="mobile-only green">CONSERVATION</h2> */}
                <div
                // className="desktop-only"
                >
                  <WebGLText
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBbottomText',
                    }}
                    verticalOffset={getChapterScrollOffset('Chapter 1')}
                    fontVars={fontVars}
                    fontSize={92}
                    tag="h2"
                  >
                    CONSERVATION
                  </WebGLText>
                  <TextFrame
                    className={s.frame}
                    theaterConfig={{
                      sheet: sheetId,
                      object: 'sceneBbottomFrame',
                    }}
                  />
                </div>
              </div>
            </div>

            <Description
              className={cn('ps-text', s.description)}
              theaterConfig={{
                sheet: sheetId,
                object: 'sceneBDescription',
              }}
            >
              Conservation scales only if stewards are rewarded. ibiCash creates
              the infrastructure to turn living forests into an income stream -
              productive assets on nature's balance sheet.
            </Description>
          </div>
        </div>
        {/* <div className="mobile-bkg-media">
          <Image
            src="/images/mobile-tmp/chapter1-sceneB.jpg"
            alt="Chapter 1 Scene A"
            fill
            objectFit="cover"
          />
        </div> */}
        <WebGLTunnel>
          <SheetProvider
            id={sheetId}
            ref={(node) => {
              setSheet(node)
            }}
          >
            <ChapterContext
              value={{
                getRender: () => renderRef.current,
                getProgress: () => progressRef.current,
                index: 0,
              }}
            >
              <WebGLChapter1 rect={rect} />
            </ChapterContext>
          </SheetProvider>
        </WebGLTunnel>
      </div>
    </ScrollSpy.Section>
  )
}
