.chapter1 {
  position: relative;

  .top-text,
  .bottom-text {
    position: relative;
    width: fit-content;

    .frame {
      position: absolute;

      @include-media ('mobile') {
        display: none;
      }
    }
  }

  .sceneA,
  .sceneB {
    pointer-events: none;
  }

  .sceneA {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: desktop-vw(95px);

    @include-media ('mobile') {
      /* height: 100svh; */
      row-gap: mobile-vw(48px);
    }

    .top-text {
      margin-left: calc(columns(1) + 0.25 * var(--layout-margin));

      @include-media ('desktop') {
        margin-left: desktop-vw(365px);
        margin-top: desktop-vw(40px);
      }

      .frame {
        top: desktop-vw(-30px);
        right: desktop-vw(-48px);
        transform: rotate(180deg);
      }
    }

    .bottom-text {
      margin-left: columns(2);

      @include-media ('desktop') {
        margin-left: desktop-vw(644px);
        /* manual fix font */
        top: desktop-vw(-15px);
      }

      .frame {
        bottom: desktop-vw(-32px);
        left: desktop-vw(-48px);
      }
    }

    .description {
      margin-left: calc(columns(3) - var(--layout-margin));
      max-width: mobile-vw(178px);

      @include-media ('desktop') {
        margin-left: calc(columns(7) + 2 * var(--layout-margin));
        margin-block: desktop-vw(0);
        max-width: desktop-vw(178px);
      }
    }
  }

  .sceneB {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: desktop-vw(200px);

    @include-media ('mobile') {
      padding-block: mobile-vw(88px) mobile-vw(112px);
      row-gap: mobile-vw(64px);
      align-items: center;
    }

    .top-text,
    .bottom-text {
      @include-media ('mobile') {
        text-align: center;
      }
    }

    .top-text {
      @include-media ('mobile') {
        width: 100%;
      }

      @include-media ('desktop') {
        margin-left: calc(columns(1) + 2 * var(--layout-margin));
      }
    }

    .bottom-text {
      @include-media ('desktop') {
        margin-left: calc(columns(2) + 2 * var(--layout-margin));
      }

      .frame {
        bottom: desktop-vw(-32px);
        left: desktop-vw(-24px);
      }
    }

    .description {
      max-width: mobile-vw(217px);

      @include-media ('mobile') {
        margin-left: calc(columns(1) + var(--layout-margin));
      }

      @include-media ('desktop') {
        max-width: desktop-vw(256px);
        margin-left: calc(columns(8) + 2 * var(--layout-margin));
      }
    }
  }
}
