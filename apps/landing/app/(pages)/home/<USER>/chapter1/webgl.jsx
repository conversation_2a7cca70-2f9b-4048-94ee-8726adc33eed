'use client'

import { types } from '@theatre/core'
import { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useAudio } from '~/hooks/use-audio'
import { useCurrentSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { CameraSpline } from '~/libs/webgl/components/camera-spline'
import { Controls } from '~/libs/webgl/components/camera-spline/controls'
import {
  ChapterContext,
  useChaptersStore,
} from '~/libs/webgl/components/postprocessing/use-chapters'
import { RenderTexture } from '~/libs/webgl/components/render-texture'
import { IntroContext } from '../intro'
import { SceneA } from '../sceneA'
import { SceneB } from '../sceneB'

export function WebGLChapter1({ rect }) {
  const [texture, setTexture] = useState(null)
  const set = useChaptersStore((state) => state.set)
  const { getRender, getProgress } = useContext(ChapterContext)

  useEffect(() => {
    set(0, { texture, rect, getRender, getProgress })
  }, [set, texture, rect, getRender, getProgress])

  const isStudio = useStudio()

  const howl = useAudio()

  const sound1Ref = useRef(null)
  const sound2Ref = useRef(null)

  useEffect(() => {
    sound1Ref.current = howl.play('Sfx_Amb_Chap1_Early_Loop')
    sound2Ref.current = howl.play('Sfx_Amb_Chap1_Hero_Loop')
    howl.volume(0, sound1Ref.current)
    howl.volume(0, sound2Ref.current)
    howl.loop(true, sound1Ref.current)
    howl.loop(true, sound2Ref.current)
    return () => {
      howl.stop(sound1Ref.current)
      howl.stop(sound2Ref.current)
    }
  }, [howl])

  const sheet = useCurrentSheet()

  const config = {
    Sfx_Amb_Chap1_Early_Loop: types.number(0, {
      range: [0, 1],
      nudgeMultiplier: 0.01,
      label: 'Early Loop',
    }),
    Sfx_Amb_Chap1_Hero_Loop: types.number(0, {
      range: [0, 1],
      nudgeMultiplier: 0.01,
      label: 'Hero Loop',
    }),
  }

  const onValuesChange = useCallback(
    ({ Sfx_Amb_Chap1_Early_Loop, Sfx_Amb_Chap1_Hero_Loop }) => {
      howl.volume(Sfx_Amb_Chap1_Early_Loop, sound1Ref.current)
      howl.volume(Sfx_Amb_Chap1_Hero_Loop, sound2Ref.current)
    },
    [howl]
  )

  const { sheet: introSheet } = useContext(IntroContext)

  useTheatre(sheet, 'sounds', config, {
    onValuesChange: onValuesChange,
  })

  useTheatre(introSheet, 'sounds', config, {
    onValuesChange: onValuesChange,
  })

  return (
    <>
      <RenderTexture
        ref={(node) => {
          setTexture(node)
        }}
      >
        <Controls enabled={isStudio}>
          <CameraSpline origin={[104, 10, -23]} intro={true} />
          <SceneA />
          <SceneB />
        </Controls>
      </RenderTexture>
    </>
  )
}
