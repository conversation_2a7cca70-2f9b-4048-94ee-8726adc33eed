'use client'

import { Environment, useGLTF, useHelper } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import { types } from '@theatre/core'
import { useRef, useState } from 'react'
import { DirectionalLightHelper, Object3D } from 'three'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useCurrentSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { updateTextures } from '~/libs/webgl/utils/textures'
import Clouds from '../clouds'
import { Fog } from '../fog'
import { FogMaskMaterial } from '../fog/material'
import Mist from '../mist'
import Rain from '../rain'
import { Shadows } from '../shadows/index.jsx'
import { Water } from '../water'
import { TerrainMaterial } from './material'

export function SceneA() {
  const {
    nodes: {
      Landscape_Rework001: terrainNode,
      Landscape_Mesa001: terrainNode2,
    },
  } = useGLTF('/models/Landscape_Rework009.glb')

  const terrainDiffuse = useProgressiveKTX2(
    '/textures/sceneA/Landscape_COMBINED001.ktx2'
  )

  updateTextures([terrainDiffuse])

  const [terrainMaterial] = useState(
    () =>
      new TerrainMaterial({
        map: terrainDiffuse,
      })
  )

  const ambientLightRef = useRef()

  const directionalLightRef = useRef()
  const directionalLightOrthographicCameraRef = useRef()
  const directinalLightTargetRef = useRef(new Object3D())

  const sheet = useCurrentSheet()

  const isStudio = useStudio()

  useHelper(
    false && isStudio && directionalLightRef,
    DirectionalLightHelper,
    '5'
  )

  // useTheatre(
  //   sheet,
  //   'terrain',
  //   {
  //     roughness: types.number(0.5, {
  //       range: [0, 1],
  //       nudgeMultiplier: 0.01,
  //     }),
  //     metalness: types.number(0.5, {
  //       range: [0, 1],
  //       nudgeMultiplier: 0.01,
  //     }),
  //   },
  //   {
  //     onValuesChange: ({ roughness, metalness }) => {
  //       closeMaterial.roughness = roughness
  //       closeMaterial.metalness = metalness
  //     },
  //   }
  // )

  useTheatre(
    sheet,
    'ambientLight',
    {
      intensity: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      color: types.rgba({
        r: 1,
        g: 1,
        b: 1,
        a: 1,
      }),
    },
    {
      onValuesChange: ({ intensity, color }) => {
        ambientLightRef.current.intensity = intensity
        ambientLightRef.current.color.set(color.toString())
      },
    }
  )

  useTheatre(
    sheet,
    'directionalLight',
    {
      intensity: types.number(1, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
      }),
      position: {
        x: types.number(-50),
        y: types.number(10),
        z: types.number(10),
      },
      target: {
        x: types.number(0),
        y: types.number(0),
        z: types.number(0),
      },
      color: types.rgba({
        r: 1,
        g: 1,
        b: 1,
        a: 1,
      }),
    },
    {
      onValuesChange: ({ intensity, position, target, color }) => {
        directionalLightRef.current.intensity = intensity
        directionalLightRef.current.position.set(
          position.x,
          position.y,
          position.z
        )

        directinalLightTargetRef.current.position.set(
          target.x,
          target.y,
          target.z
        )

        directionalLightRef.current.target = directinalLightTargetRef.current

        directionalLightRef.current.color.set(color.toString())
        directionalLightRef.current.updateMatrix()
      },
    }
  )

  useFrame((_, deltaTime) => {
    terrainMaterial.raf(deltaTime)
  })

  const [fogMaskMaterial] = useState(
    () => new FogMaskMaterial({ color: '#7f7f7f' })
  )

  return (
    <>
      <Environment
        files={[
          '/textures/cubemap/nz.png',
          '/textures/cubemap/px.png',
          '/textures/cubemap/px.png',
          '/textures/cubemap/px.png',
          '/textures/cubemap/px.png',
          '/textures/cubemap/px.png',
        ]}
        // files="/textures/overcast_soil_puresky_2k.png"
        background="only"
        // ground={{ height: 0 }}
      />
      <ambientLight intensity={1} ref={ambientLightRef} />
      <directionalLight
        ref={directionalLightRef}
        intensity={1}
        castShadow
        shadow-mapSize={[1024, 1024]}
        matrixAutoUpdate={false}
      >
        {/* <orthographicCamera
          ref={directionalLightOrthographicCameraRef}
          attach="shadow-camera"
          args={[-50, 50, 50, -50]}
        /> */}
      </directionalLight>
      <Shadows />
      <Mist />
      <Clouds
        position={[0, 30, 0]}
        theatreKey="sky"
        speed={-5}
        frequency={0.5}
        opacity={1}
      />
      <Clouds
        position={[0, 25, 0]}
        theatreKey="sky"
        speed={-5}
        frequency={0.5}
        opacity={1}
      />
      <Clouds
        position={[0, 20, 0]}
        theatreKey="sky"
        speed={-5}
        frequency={0.5}
        opacity={1}
      />
      <Clouds
        position={[-13, 3, -45]}
        theatreKey="clouds"
        opacity={0.5}
        frequency={5}
        speed={-5}
        alphaMap={true}
      />
      <Fog theatreKey="fog" far={250} near={0} />
      <mesh matrixAutoUpdate={false}>
        <primitive object={terrainNode.geometry} attach="geometry" />
        <primitive object={terrainMaterial} attach="material" />
      </mesh>
      <mesh matrixAutoUpdate={false}>
        <primitive object={terrainNode2.geometry} attach="geometry" />
        <primitive object={terrainMaterial} attach="material" />
      </mesh>
      <mesh
        matrixAutoUpdate={false}
        scale={[400, 15, 100]}
        rotation-y={Math.PI / 2}
        position={[-130, 3, 0]}
        ref={(node) => {
          node?.updateMatrix()
        }}
      >
        <planeGeometry />
        <primitive object={fogMaskMaterial} attach="material" />
      </mesh>
      <Water />
      <Rain />
    </>
  )
}
