import { MeshBasicMaterial } from 'three'
import {
  getNorm<PERSON>Shader,
  getShadowsShader,
  getTimeShader,
  getWorldPositionShader,
} from '~/libs/webgl/utils/extend-shader'

const RANDOM = Math.random()

export class TerrainMaterial extends MeshBasicMaterial {
  constructor({ ...props } = {}) {
    super({ ...props })

    this.shadows = getShadowsShader()
    this.normals = getNormalsShader()
    this.worldPosition = getWorldPositionShader()
    this.time = getTimeShader()

    this.uniforms = {
      ...this.uniforms,
      ...this.time.uniforms,
      // Shadows
      ...this.shadows.uniforms,
    }

    this.defines = {
      ...this.defines,
      ...this.shadows.defines,
      ...this.normals.defines,
      ...this.worldPosition.defines,
    }

    this.customProgramCacheKey = () =>
      this.constructor.name + (process.env.NODE_ENV === 'development' && RANDOM)
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    this.worldPosition.extend(shader)
    this.shadows.extend(shader)
    this.normals.extend(shader)
    this.time.extend(shader)

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }
  }

  raf(deltaTime) {
    this.time.raf(deltaTime)
  }
}
