import { useGLTF } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import { types } from '@theatre/core'
import { useContext, useEffect, useState } from 'react'
import { BatchedMesh, MirroredRepeatWrapping, Vector3 } from 'three'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { CloudsMaterial } from '../clouds/material'
import { IntroContext } from '../intro'

export default function Mist() {
  const {
    nodes: { cloud_cards: cloudCardsNode },
  } = useGLTF('/models/sceneA002.glb')

  // const noiseTexture = useTexture('/textures/noise/noise.png')
  const noiseTexture = useProgressiveKTX2('/textures/noise/noise.ktx2')

  const [material] = useState(
    () =>
      new CloudsMaterial({
        opacity: 0.8,
        world: false,
        shadows: false,
        fade: true,
        maskPoint: new Vector3(87, 3, -3.5),
      })
  )

  useEffect(() => {
    noiseTexture.wrapS = noiseTexture.wrapT = MirroredRepeatWrapping
    noiseTexture.needsUpdate = true
    material.noiseTexture = noiseTexture
  }, [noiseTexture, material])

  useFrame((_, deltaTime) => {
    material.raf(deltaTime)
  })

  const sheet = useCurrentSheet()
  const { sheet: introSheet } = useContext(IntroContext)

  const config = {
    opacity: types.number(material.opacity, {
      range: [0, 1],
    }),
    frequency: types.number(100, {
      range: [0, 1000],
      nudgeMultiplier: 1,
    }),
    speed: types.number(5, {
      range: [-10, 10],
      nudgeMultiplier: 0.01,
    }),
  }

  const onValuesChange = ({ opacity, frequency, speed }) => {
    material.opacity = opacity
    material.frequency = frequency * 0.001
    material.speed = speed * 0.001
  }

  useTheatre(sheet, 'mist', config, {
    onValuesChange,
  })

  useTheatre(introSheet, 'mist', config, {
    onValuesChange,
  })

  const [batchedMesh] = useState(() => {
    const batchedMesh = new BatchedMesh(
      cloudCardsNode.children.length,
      500,
      1000,
      material
    )

    for (const child of cloudCardsNode.children) {
      const id = batchedMesh.addGeometry(child.geometry)
      const instanceId = batchedMesh.addInstance(id)
      batchedMesh.setMatrixAt(instanceId, child.matrix)
    }

    return batchedMesh
  })

  return <primitive object={batchedMesh} />

  // return cloudCardsNode.children.map((child) => (
  //   <batchedMesh
  //     args={[cloudCardsNode.children.length, 5000, 10000]}
  //     key={child.id}
  //     matrixAutoUpdate={false}
  //   >
  //     <primitive object={child.geometry} attach="geometry" />
  //     <primitive object={material} attach="material" />
  //   </batchedMesh>
  // ))
}
