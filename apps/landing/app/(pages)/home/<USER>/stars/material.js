import { ShaderMaterial, Vector2 } from 'three'

const vertexShader = /* glsl */ `
    varying vec2 vUv;
    uniform float uCurvature;
    uniform vec2 uAspect;
    void main() {
      vUv = uv;
      vUv += (uAspect - 1.) * 0.5;
      vUv /= uAspect;
      
      vec3 transformedPosition = position;

      // transformedPosition.z += sin(uv.x * 3.1415 * 10.);
      transformedPosition.z = -sin(uv.x * 3.1415) * uCurvature;


      gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(transformedPosition, 1.0);
    }
`

const fragmentShader = /* glsl */ `
    varying vec2 vUv;
    uniform float uProgress;
    uniform float uRotationProgress;
    uniform vec2 uOrigin;
    uniform float uThickness;
    uniform vec2 uResolution;
    // uniform sampler2D uTexture;

    float random (vec2 st) {
        return fract(sin(dot(st.xy,
                            vec2(12.9898,78.233)))*
            43758.5453123);
    }
  
    float drawCone(vec2 uv, vec2 center, float angle, float spread, float radius) {
        vec2 dir = uv - center;
        float dist = length(dir);
        if (dist > radius) return 0.0;
    
        float theta = atan(dir.y, dir.x); // angle to current fragment
        float delta = abs(mod(theta - angle + 3.141592, 6.283185) - 3.141592); // shortest angle difference
    
        return step(delta, spread); // inside cone if within spread
    }

    void main() {
        // vec4 color = texture2D(uTexture, vUv);

        vec2 center = uOrigin;
        float count = uResolution.x / uThickness;

        float distanceFromCenter = distance(vUv, center);
        distanceFromCenter = floor(distanceFromCenter * count) / count;

        // float circle = random(vec2(distanceFromCenter));
        float alpha = random(vec2(distanceFromCenter));
        float visible = random(vec2(distanceFromCenter - 1.));

        float angleDegree = (random(vec2(distanceFromCenter + 1.)) * -360.) - (uProgress * 180.) - (uRotationProgress * 360.);
        float angle = radians(angleDegree);
    
        float spread = radians(max(0.1,uProgress * 180.));
        
        float radius = 1.;

        // circle = smoothstep(0.5, 0.0, circle);
        
        
        // if(circle < 0.5) {
        //  circle = 0.;
        // }

        float circle = drawCone(vUv, center, angle, spread, radius);

        if(visible < 0.5) {
         circle = 0.;
        }

        
        

        // vec4 color = vec4(1.0, 0.0, 0.0, 1.0);

        gl_FragColor = vec4(vec3(1.), circle * alpha);
    }
`

export class StarsMaterial extends ShaderMaterial {
  constructor({ ...props } = {}) {
    super({
      vertexShader,
      fragmentShader,
      uniforms: {
        uCurvature: { value: 0 },
        uAspect: { value: new Vector2(1, 1) },
        uProgress: { value: 0 },
        uRotationProgress: { value: 0 },
        uOrigin: { value: new Vector2(0.5, 0.5) },
        uThickness: { value: 0.1 },
        uResolution: { value: new Vector2(1, 1) },
        // uTexture: { value: null },
      },
      ...props,
    })
  }

  // get map() {
  //   return this.uniforms.uTexture.value
  // }

  // set map(value) {
  //   this.uniforms.uTexture.value = value
  // }

  get aspect() {
    return this.uniforms.uAspect.value
  }

  setAspect(x, y) {
    this.uniforms.uAspect.value.set(x, y)
  }

  get curvature() {
    return this.uniforms.uCurvature.value
  }

  set curvature(value) {
    this.uniforms.uCurvature.value = value
  }

  get progress() {
    return this.uniforms.uProgress.value
  }

  set progress(value) {
    this.uniforms.uProgress.value = value
  }

  get rotationProgress() {
    return this.uniforms.uRotationProgress.value
  }

  set rotationProgress(value) {
    this.uniforms.uRotationProgress.value = value
  }

  get origin() {
    return this.uniforms.uOrigin.value
  }

  setOrigin(x, y) {
    this.uniforms.uOrigin.value.set(x, y)
  }

  get thickness() {
    return this.uniforms.uThickness.value
  }

  set thickness(value) {
    this.uniforms.uThickness.value = value
  }

  get resolution() {
    return this.uniforms.uResolution.value
  }

  setResolution(x, y) {
    this.uniforms.uResolution.value.set(x, y)
  }
}
