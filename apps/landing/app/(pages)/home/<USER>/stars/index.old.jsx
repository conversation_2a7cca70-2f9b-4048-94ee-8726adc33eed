import { types } from '@theatre/core'
import { useCallback, useEffect, useRef, useState } from 'react'
import { CanvasTexture, DoubleSide } from 'three'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useCurrentSheet } from '~/libs/theatre'
import {
  useStudio,
  useStudioCurrentObject,
} from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useControlsStore } from '~/libs/webgl/components/camera-spline/controls'
import { StarsMaterial } from './material'

export function Stars({ index = 0 }) {
  const setObject = useControlsStore((state) => state.setObject)
  const object = useControlsStore((state) => state.object)
  const setNextMode = useControlsStore((state) => state.setNextMode)

  const sheet = useCurrentSheet()

  const ref = useRef()
  const meshRef = useRef()
  const [canvas] = useState(() => new OffscreenCanvas(1024, 1024))
  const [ctx] = useState(() => canvas.getContext('2d'))
  const [canvasTexture, setCanvasTexture] = useState(
    () => new CanvasTexture(canvas)
  )

  const { isMobile } = useDeviceDetection()

  const [material] = useState(
    () =>
      new StarsMaterial({
        side: DoubleSide,
        transparent: true,
        fog: false,
        depthWrite: true,
        depthTest: true,
        // wireframe: true,
      })
  )

  const [width, setWidth] = useState(1024)
  const [height, setHeight] = useState(1024)

  useEffect(() => {
    console.log('WebGL: Stars', width, height)

    canvas.width = width
    canvas.height = height
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    setCanvasTexture(new CanvasTexture(canvas))
  }, [width, height])

  const stars = useRef([])

  const isEven = index % 2 === 0

  const frameCountRef = useRef(0)

  const draw = useCallback(
    (x, y, progress, lineWidth = 1, rotationProgress = 0) => {
      if (frameCountRef.current % 2 === (isEven ? 0 : 1)) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.strokeStyle = 'white'

        for (const [radius, angle, alpha, thickness] of stars.current) {
          ctx.globalAlpha = alpha

          ctx.lineWidth = lineWidth - thickness

          // if (progress === 0) {
          //   ctx.lineWidth += 1
          // }

          ctx.beginPath()
          ctx.arc(
            canvas.width * x,
            canvas.height * y,
            radius * (canvas.width * 0.5),
            angle + rotationProgress * 2 * Math.PI,
            angle +
              rotationProgress * 2 * Math.PI +
              1 / (radius * canvas.width * 0.5) +
              progress * 2 * Math.PI
          )
          ctx.stroke()
        }

        //   for (let i = 0; i < count; i++) {
        //     //   const x = Math.random() * canvas.width
        //     //   const y = Math.random() * canvas.height
        //     //   ctx.fillRect(x, y, 1, 1)
        //     ctx.beginPath()
        //     ctx.arc(
        //       canvas.width * x,
        //       canvas.height * y,
        //       Math.random() * canvas.width,
        //       Math.random() * 2 * Math.PI,
        //       Math.random() * 2 * Math.PI
        //     )
        //     ctx.stroke()
        //   }

        // const canvasTexture = new CanvasTexture(canvas)
        // material.map = canvasTexture
        // material.needsUpdate = true

        canvasTexture.needsUpdate = true
      }

      frameCountRef.current++
    },
    [canvas, canvasTexture, isMobile]
  )

  useEffect(() => {
    material.map = canvasTexture
    material.needsUpdate = true
  }, [material, canvasTexture])

  const [count, setCount] = useState(100)

  useEffect(() => {
    stars.current = []

    for (let i = 0; i < count; i++) {
      stars.current.push([
        Math.random(),
        Math.random() * Math.PI * 2,
        Math.random(),
        Math.random(),
      ])
    }
  }, [count])

  const { set, object: objectTheatre } = useTheatre(
    sheet,
    'stars / matrix',
    {
      count: types.number(100, {
        range: [0, 1000],
        nudgeMultiplier: 1,
        label: 'Count',
      }),
      position: {
        x: types.number(-250, { nudgeMultiplier: 0.01 }),
        y: types.number(40, { nudgeMultiplier: 0.01 }),
        z: types.number(0, { nudgeMultiplier: 0.01 }),
      },
      rotation: {
        x: types.number(0, {
          nudgeMultiplier: 0.01,
          range: [-Math.PI / 2, Math.PI / 2],
        }),
        y: types.number(Math.PI / 2, {
          nudgeMultiplier: 0.01,
          range: [-Math.PI / 2, Math.PI / 2],
        }),
        z: types.number(0, {
          nudgeMultiplier: 0.01,
          range: [-Math.PI / 2, Math.PI / 2],
        }),
      },
      scale: {
        x: types.number(300, { nudgeMultiplier: 1 }),
        y: types.number(150, { nudgeMultiplier: 1 }),
        z: types.number(1, { nudgeMultiplier: 1 }),
      },
    },
    {
      onValuesChange: ({ position, scale, rotation, count }) => {
        ref.current.scale.set(scale.x, scale.y, 1)
        ref.current.position.set(position.x, position.y, position.z)
        ref.current.rotation.set(rotation.x, rotation.y, rotation.z)
        ref.current.updateMatrix()

        if (isMobile) {
          setCount(count * 0.1)
        } else {
          setCount(count * 0.5)
        }

        const aspect = scale.x / scale.y

        let width = Math.min(4096, 1024 * aspect)

        if (isMobile) {
          width = width * 0.5
        }

        const height = width / aspect

        setWidth(width)
        setHeight(height)
      },
      deps: [isMobile],
    }
  )

  useTheatre(
    sheet,
    'stars',
    {
      origin: {
        x: types.number(0.5, { label: 'Origin X', nudgeMultiplier: 0.01 }),
        y: types.number(0.5, { label: 'Origin Y', nudgeMultiplier: 0.01 }),
      },
      progress: types.number(0, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
        label: 'Progress',
      }),
      rotationProgress: types.number(0, {
        range: [-1, 1],
        nudgeMultiplier: 0.01,
        label: 'Rotation Progress',
      }),
      thickness: types.number(2, {
        range: [0.1, 10],
        nudgeMultiplier: 0.1,
        label: 'Thickness',
      }),
      curvature: types.number(200, {
        range: [0, 500],
        nudgeMultiplier: 1,
        label: 'Curvature',
      }),
    },
    {
      onValuesChange: ({
        origin,
        progress,
        thickness,
        rotationProgress,
        curvature,
      }) => {
        material.curvature = curvature
        draw(
          origin.x,
          origin.y,
          progress,
          thickness,
          rotationProgress,
          curvature
        )
      },
      deps: [draw],
    }
  )

  const studio = useStudio()
  const currentObject = useStudioCurrentObject()

  useEffect(() => {
    if (
      currentObject?.objectKey === 'stars' &&
      sheet.address.sheetId === currentObject?.sheetId
    ) {
      setObject(ref.current?.id)
    }
  }, [currentObject, setObject, sheet])

  useEffect(() => {
    if (object === ref.current?.id) {
      studio?.setSelection([objectTheatre])
    }
  }, [object, studio, objectTheatre])

  return (
    <>
      <group matrixAutoUpdate={false} ref={ref}>
        {/* biome-ignore lint/a11y/useKeyWithClickEvents: we need to trigger on click */}
        <mesh
          renderOrder={-1000}
          ref={meshRef}
          onClick={(e) => {
            e.stopPropagation()
            setObject(e.object.id)
          }}
          onPointerMissed={(e) => {
            e.type === 'click' && setObject(null)
          }}
          // onPointerOver={(e) => {
          //   e.stopPropagation()
          // }}
          onContextMenu={(e) => {
            const isCurrentObject = object === ref.current?.id
            if (isCurrentObject) {
              setNextMode()
            }
          }}
          onTransformControlsMove={(e) => {
            if (!ref.current) return

            ref.current?.updateMatrix()
          }}
          onTransformControlsRelease={(e) => {
            if (!ref.current) return

            set({
              position: {
                x: ref.current.position.x,
                y: ref.current.position.y,
                z: ref.current.position.z,
              },
              scale: {
                x: ref.current.scale.x,
                y: ref.current.scale.y,
                z: ref.current.scale.z,
              },
              rotation: {
                x: ref.current.rotation.x,
                y: ref.current.rotation.y,
                z: ref.current.rotation.z,
              },
            })
          }}
        >
          <planeGeometry args={[1, 1, 16, 1]} />
          <primitive object={material} />
        </mesh>
      </group>
    </>
  )
}
