import { ShaderMaterial } from 'three'

const vertexShader = /* glsl */ `
    varying vec2 vUv;
    uniform float uCurvature;

    void main() {
      vUv = uv;
      vec3 transformedPosition = position;

      // transformedPosition.z += sin(uv.x * 3.1415 * 10.);
      transformedPosition.z = -sin(uv.x * 3.1415) * uCurvature;


      gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(transformedPosition, 1.0);
    }
`

const fragmentShader = /* glsl */ `
    varying vec2 vUv;
    uniform sampler2D uTexture;

    void main() {
        vec4 color = texture2D(uTexture, vUv);
        gl_FragColor = color;
    }
`

export class StarsMaterial extends ShaderMaterial {
  constructor({ ...props } = {}) {
    super({
      vertexShader,
      fragmentShader,
      uniforms: {
        uCurvature: { value: 0 },
        uTexture: { value: null },
      },
      ...props,
    })
  }

  get map() {
    return this.uniforms.uTexture.value
  }

  set map(value) {
    this.uniforms.uTexture.value = value
  }

  get curvature() {
    return this.uniforms.uCurvature.value
  }

  set curvature(value) {
    this.uniforms.uCurvature.value = value
  }
}
