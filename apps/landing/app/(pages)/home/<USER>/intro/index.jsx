'use client'

import { types, val } from '@theatre/core'
import { useWindowSize } from 'hamo'
import { useLenis } from 'lenis/react'
import { createContext, useEffect, useState } from 'react'
import { useAudio } from '~/hooks/use-audio'
import { mapRange } from '~/libs/maths'
import { useStore } from '~/libs/store'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export const IntroContext = createContext({})
const sheetId = 'intro'

export function Intro({ children }) {
  const sheet = useSheet(sheetId)
  const loaderLoaded = useStore((state) => state.loaderLoaded)
  const introCompleted = useStore((state) => state.introCompleted)
  const setIntroCompleted = useStore((state) => state.setIntroCompleted)
  const chapter1Sheet = useSheet('chapter1')
  const [end, setEnd] = useState(0.15)
  const lenis = useLenis()

  useTheatre(
    sheet,
    'intro',
    {
      end: types.number(0.21, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ end, enabled }) => {
        setEnd(end)
      },
    }
  )

  const { height: windowHeight } = useWindowSize()
  const howl = useAudio()

  useEffect(() => {
    if (introCompleted) return

    if (chapter1Sheet && lenis && loaderLoaded && sheet) {
      const length = val(chapter1Sheet.sequence.pointer.length)
      const value = mapRange(0, 1, end, -windowHeight, length * windowHeight)

      lenis.scrollTo(value, { immediate: true, force: true })

      setTimeout(() => {
        const sound = howl.play('Sfx_Intro_Whoosh')
        howl.volume(1, sound)
      }, 2000)

      sheet.sequence?.play().then(() => {
        setIntroCompleted(true)
        chapter1Sheet.sequence.position = end * length
      })
    }
  }, [
    chapter1Sheet,
    end,
    windowHeight,
    lenis,
    loaderLoaded,
    sheet,
    introCompleted,
    howl,
  ])

  return (
    <IntroContext.Provider value={{ sheet, end }}>
      {children}
    </IntroContext.Provider>
  )
}

export function useIntroTheatre({ theaterConfig, onValuesChange }) {
  const introSheet = useSheet(sheetId)
  const { object, sheet } = theaterConfig

  useTheatre(
    object.includes('sceneA') && sheet.includes('chapter1') ? introSheet : null,
    object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange,
    }
  )
}
