import { Color, MeshBasicMaterial, Vector3 } from 'three'
import { getWorldPositionShader } from '~/libs/webgl/utils/extend-shader'

const RANDOM = Math.random()

export class FloraMaterial extends MeshBasicMaterial {
  constructor({ instances = false, ...props } = {}) {
    super({ ...props })

    this.worldPosition = getWorldPositionShader()

    this.uniforms = {
      ...this.uniforms,
      ...this.worldPosition.uniforms,
      uDistance: { value: 0 },
      uBlendOpacity: { value: 1 },
      uLineColor: { value: new Color(1, 0, 0) },
      uLineWidth: { value: 0.01 },
      uFadeOpacity: { value: 0.1 },
      uFadeWidth: { value: 0.3 },
      uCameraPosition: { value: new Vector3() },
    }

    this.defines = {
      ...this.defines,
      ...this.worldPosition.defines,
      MAP_UV: 'uv',
      USE_MAP: '',
      ...(instances ? { USE_INSTANCING: '' } : {}),
    }

    this.customProgramCacheKey = () =>
      this.constructor.name + JSON.stringify({ instances }) + RANDOM // allow HMR
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling ${this.constructor.name}`)

    this.worldPosition.extend(shader)

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      'void main() {',
      /*glsl*/ `
      uniform float uDistance;
      uniform float uBlendOpacity;
      uniform vec3 uLineColor;
      uniform float uLineWidth;
      uniform float uFadeOpacity;
      uniform float uFadeWidth;
      uniform vec3 uCameraPosition;
      void main() {
    `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      '#include <fog_fragment>',
      /*glsl*/ `
      #include <fog_fragment>


      float distanceToCamera = distance( vWorldPosition.xyz, uCameraPosition ) / uDistance;

      float gradient = 1.-smoothstep(0.0, uFadeWidth, abs(1. - (distanceToCamera)));
      if(distanceToCamera > 1.0) {
        gradient = 0.;
      }
      gradient *= uFadeOpacity;

     
      float line = 1.-smoothstep(0.0, uLineWidth, abs(1. - distanceToCamera));
          gl_FragColor.rgb = mix(gl_FragColor.rgb, uLineColor, uBlendOpacity * max(line, gradient));
        
        `
    )
  }

  raf(deltaTime) {
    this.uniforms.uTime.value += deltaTime * this.speed
  }

  set cameraPosition(value) {
    this.uniforms.uCameraPosition.value.copy(value)
  }

  get cameraPosition() {
    return this.uniforms.uCameraPosition.value
  }

  set distance(value) {
    this.uniforms.uDistance.value = value
  }

  get distance() {
    return this.uniforms.uDistance.value
  }

  set blendOpacity(value) {
    this.uniforms.uBlendOpacity.value = value
  }

  get blendOpacity() {
    return this.uniforms.uBlendOpacity.value
  }

  set lineColor(value) {
    this.uniforms.uLineColor.value.set(value)
  }

  get lineColor() {
    return this.uniforms.uLineColor.value
  }

  set lineWidth(value) {
    this.uniforms.uLineWidth.value = value
  }

  get lineWidth() {
    return this.uniforms.uLineWidth.value
  }

  set fadeOpacity(value) {
    this.uniforms.uFadeOpacity.value = value
  }

  get fadeOpacity() {
    return this.uniforms.uFadeOpacity.value
  }

  set fadeWidth(value) {
    this.uniforms.uFadeWidth.value = value
  }

  get fadeWidth() {
    return this.uniforms.uFadeWidth.value
  }
}
