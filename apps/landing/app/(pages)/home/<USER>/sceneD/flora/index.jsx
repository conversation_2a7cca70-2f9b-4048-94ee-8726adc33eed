import { types } from '@theatre/core'
import { useEffect, useRef, useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import {
  useStudio,
  useStudioCurrentObject,
} from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useControlsStore } from '~/libs/webgl/components/camera-spline/controls'
import { Instance, Instances } from '~/libs/webgl/components/instances'

function Item({ theatreKey = '', index = 0 }) {
  const ref = useRef()
  const setObject = useControlsStore((state) => state.setObject)
  const object = useControlsStore((state) => state.object)
  const setNextMode = useControlsStore((state) => state.setNextMode)

  const positionRef = useRef({
    x: -10,
    y: 0.5,
    z: 5,
  })

  const sheet = useCurrentSheet()
  const { set, object: objectTheatre } = useTheatre(
    sheet,
    theatreKey,
    {
      position: {
        x: types.number(positionRef.current.x, {
          nudgeMultiplier: 0.01,
        }),
        y: types.number(positionRef.current.y, {
          nudgeMultiplier: 0.01,
        }),
        z: types.number(positionRef.current.z, {
          nudgeMultiplier: 0.01,
        }),
      },
      scale: types.number(0.25, {
        nudgeMultiplier: 0.01,
      }),
      rotation: types.number(0, {
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ position, scale, rotation }) => {
        ref.current.position.set(position.x, position.y, position.z)
        ref.current.scale.set(scale, scale, scale)
        ref.current.rotation.set(0, rotation, 0)
        ref.current.updateMatrix()

        ref.current.instance.current.setMatrixAt(index, ref.current.matrix)
        ref.current.instance.current.instanceMatrix.needsUpdate = true
      },
    }
  )

  const studio = useStudio()
  const currentObject = useStudioCurrentObject()

  useEffect(() => {
    if (currentObject?.objectKey === theatreKey) {
      setObject(ref.current?.id)
    }
  }, [currentObject, setObject, theatreKey])

  useEffect(() => {
    if (object === ref.current?.id) {
      studio?.setSelection([objectTheatre])
    }
  }, [object, studio, objectTheatre])

  return (
    <Instance
      ref={ref}
      onClick={(e) => {
        e.stopPropagation()
        setObject(e.object.id)
      }}
      onPointerMissed={(e) => {
        e.type === 'click' && setObject(null)
      }}
      // onPointerOver={(e) => {
      //   e.stopPropagation()
      // }}
      onContextMenu={(e) => {
        const isCurrentObject = object === ref.current?.id
        if (isCurrentObject) {
          setNextMode()
        }
      }}
      onTransformControlsRelease={(e) => {
        if (!ref.current) return

        set({
          position: {
            x: ref.current.position.x,
            y: ref.current.position.y,
            z: ref.current.position.z,
          },
          scale: ref.current.scale.y,
          rotation: ref.current.rotation.y,
        })
      }}
    />
  )
}

export function Flora({ geometry, material, theatreKey = '' }) {
  const [count, setCount] = useState(10)

  const sheet = useCurrentSheet()
  useTheatre(
    sheet,
    theatreKey,
    {
      count: types.number(10, {
        range: [0, 100],
        nudgeMultiplier: 1,
      }),
    },
    {
      onValuesChange: ({ count }) => {
        setCount(count)
      },
    }
  )

  const instancesRef = useRef()

  const isStudio = useStudio()

  return (
    <>
      <Instances
        limit={60}
        range={60}
        ref={instancesRef}
        frustumCulled={false}
        frames={isStudio ? Number.POSITIVE_INFINITY : 1}
        // frames={Number.POSITIVE_INFINITY}
      >
        <primitive object={geometry} attach="geometry" />
        <primitive object={material} attach="material" />
        {Array.from({ length: count }).map((_, index) => (
          <Item
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            key={index}
            theatreKey={`${theatreKey} / ${theatreKey}-${index}`}
            index={index}
            // onUpdate={() => {
            //   console.log(instancesRef.current)
            //   instancesRef.current.instanceMatrix.needsUpdate = true
            //   instancesRef.current.updateMatrix()
            // }}
          />
        ))}
      </Instances>
    </>
  )
}
