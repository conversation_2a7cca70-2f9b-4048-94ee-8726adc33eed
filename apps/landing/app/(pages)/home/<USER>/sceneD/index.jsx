import { useGLTF } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { types, val } from '@theatre/core'
import gsap from 'gsap'
import { useContext, useEffect, useRef, useState } from 'react'
import { DoubleSide } from 'three'
import { useAudio } from '~/hooks/use-audio'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { useSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { Instance, Instances } from '~/libs/webgl/components/instances'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import { updateTextures } from '~/libs/webgl/utils/textures'
import { Flora } from './flora'
import { FloraMaterial } from './flora/material'

export function SceneD() {
  const {
    nodes: { GROUND_TILE: groundNode },
  } = useGLTF('/models/SceneC005.glb')

  const {
    nodes: {
      treeA: treeANode,
      treeB: treeBNode,
      plantA: plantANode,
      plantB: plantBNode,
      plantC: plantCNode,
    },
  } = useGLTF('/models/sceneD.glb')

  const [treeAGeometry] = useState(() => treeANode.geometry.clone())
  const [treeBGeometry] = useState(() => treeBNode.geometry.clone())
  const [plantAGeometry] = useState(() => plantANode.geometry.clone())
  const [plantBGeometry] = useState(() => plantBNode.geometry.clone())
  const [plantCGeometry] = useState(() => plantCNode.geometry.clone())
  const [groundGeometry] = useState(() => groundNode.geometry.clone())

  const groundTexture = useProgressiveKTX2(
    '/textures/sceneC/ground_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const groundAlpha = useProgressiveKTX2(
    '/textures/sceneC/ground_Bake1_PBR_Alpha.ktx2'
  )

  const treeADiffuse = useProgressiveKTX2(
    '/textures/sceneD/treeA_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const treeBDiffuse = useProgressiveKTX2(
    '/textures/sceneD/treeB_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const plantADiffuse = useProgressiveKTX2(
    '/textures/sceneD/plantA_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const plantBDiffuse = useProgressiveKTX2(
    '/textures/sceneD/plantB_Bake1_CyclesBake_COMBINED.ktx2'
  )
  const plantCDiffuse = useProgressiveKTX2(
    '/textures/sceneD/plantC_Bake1_CyclesBake_COMBINED.ktx2'
  )

  const treeAAlpha = useProgressiveKTX2(
    '/textures/sceneD/treeA_Bake1_PBR_Alpha.ktx2'
  )
  const treeBAlpha = useProgressiveKTX2(
    '/textures/sceneD/treeB_Bake1_PBR_Alpha.ktx2'
  )
  const plantAAlpha = useProgressiveKTX2(
    '/textures/sceneD/plantA_Bake1_PBR_Alpha.ktx2'
  )
  const plantBAlpha = useProgressiveKTX2(
    '/textures/sceneD/plantB_Bake1_PBR_Alpha.ktx2'
  )
  const plantCAlpha = useProgressiveKTX2(
    '/textures/sceneD/plantC_Bake1_PBR_Alpha.ktx2'
  )

  updateTextures([
    treeADiffuse,
    treeBDiffuse,
    plantADiffuse,
    plantBDiffuse,
    plantCDiffuse,
    treeAAlpha,
    treeBAlpha,
    plantAAlpha,
    plantBAlpha,
    plantCAlpha,
  ])

  const [treeAInstancesMaterial] = useState(
    () =>
      new FloraMaterial({
        map: treeADiffuse,
        side: DoubleSide,
        alphaMap: treeAAlpha,
        alphaTest: 0.25,
        instances: true,
      })
  )

  const [treeBInstancesMaterial] = useState(
    () =>
      new FloraMaterial({
        map: treeBDiffuse,
        side: DoubleSide,
        alphaMap: treeBAlpha,
        alphaTest: 0.25,
        instances: true,
      })
  )
  const [plantAInstancesMaterial] = useState(
    () =>
      new FloraMaterial({
        map: plantADiffuse,
        side: DoubleSide,
        alphaMap: plantAAlpha,
        alphaTest: 0.25,
        instances: true,
      })
  )
  const [plantBInstancesMaterial] = useState(
    () =>
      new FloraMaterial({
        map: plantBDiffuse,
        side: DoubleSide,
        alphaMap: plantBAlpha,
        alphaTest: 0.25,
        instances: true,
      })
  )
  const [plantCInstancesMaterial] = useState(
    () =>
      new FloraMaterial({
        map: plantCDiffuse,
        side: DoubleSide,
        alphaMap: plantCAlpha,
        alphaTest: 0.25,
        instances: true,
      })
  )

  const [groundInstancesMaterial] = useState(
    () =>
      new FloraMaterial({
        map: groundTexture,
        alphaMap: groundAlpha,
        alphaTest: 0.25,
        side: DoubleSide,
        instances: true,
      })
  )

  const materials = [
    treeAInstancesMaterial,
    treeBInstancesMaterial,
    plantAInstancesMaterial,
    plantBInstancesMaterial,
    plantCInstancesMaterial,
    groundInstancesMaterial,
  ]

  // const [groundGeometry] = useState(() => groundNode.geometry.clone())

  const sheet = useSheet('scanline')

  useTheatre(
    sheet,
    'scanline',
    {
      distance: types.number(0, {
        range: [0, 1000],
        nudgeMultiplier: 0.01,
      }),
      color: types.rgba({
        r: 172 / 255,
        g: 255 / 255,
        b: 70 / 255,
        a: 1,
      }),
      opacity: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      lineWidth: types.number(0.1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      fadeOpacity: types.number(0.1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      fadeWidth: types.number(0.3, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({
        distance,
        color,
        opacity,
        lineWidth,
        fadeOpacity,
        fadeWidth,
      }) => {
        color = color.toString().slice(0, 7)

        for (const material of materials) {
          material.distance = distance
          material.lineColor = color
          material.blendOpacity = opacity
          material.lineWidth = lineWidth * 0.1
          material.fadeOpacity = fadeOpacity
          material.fadeWidth = fadeWidth
        }
      },
    }
  )

  const timelineRef = useRef(null)

  const isStudio = useStudio()

  const { getRender } = useContext(ChapterContext)

  const camera = useThree((state) => state.camera)

  const howl = useAudio()

  useEffect(() => {
    const abortController = new AbortController()
    const mainElement = document.querySelector('main')
    // Revert wtih a better fix
    mainElement?.addEventListener(
      'click',
      (e) => {
        if (!getRender()) return

        if (isStudio) {
          if (!e.shiftKey) return

          e.preventDefault()
          e.stopPropagation()
        }

        if (
          typeof timelineRef.current?.progress() === 'number' &&
          timelineRef.current?.progress() < 1
        )
          return

        const timeline = gsap.timeline({})

        const duration = val(sheet.sequence.pointer.length)

        const proxy = {
          progress: 0,
        }

        for (const material of materials) {
          camera.getWorldPosition(material.cameraPosition)
        }

        const scannerSound = `Sfx_Scanner${Math.floor(Math.random() * 3 + 1)}`

        const sound = howl.play(scannerSound)
        howl.volume(1, sound)

        timeline.to(proxy, {
          progress: 1,
          duration,
          onUpdate: () => {
            sheet.sequence.position = proxy.progress * duration
          },
        })

        timelineRef.current = timeline
      },
      {
        signal: abortController.signal,
        capture: true,
      }
    )

    return () => abortController.abort()
  }, [sheet, isStudio, camera, howl])

  return (
    <>
      <group
        position={[105.6, 0.12, -18.4]}
        matrixAutoUpdate={false}
        ref={(node) => {
          node?.updateMatrix()
        }}
      >
        <group
          rotation={[0, 1.035, 0]}
          matrixAutoUpdate={false}
          ref={(node) => {
            node?.updateMatrix()
          }}
        >
          <Flora
            theatreKey="treeA"
            geometry={treeAGeometry}
            material={treeAInstancesMaterial}
          />
          <Flora
            theatreKey="treeB"
            geometry={treeBGeometry}
            material={treeBInstancesMaterial}
          />
          <Flora
            theatreKey="plantA"
            geometry={plantAGeometry}
            material={plantAInstancesMaterial}
          />
          <Flora
            theatreKey="plantB"
            geometry={plantBGeometry}
            material={plantBInstancesMaterial}
          />
          <Flora
            theatreKey="plantC"
            geometry={plantCGeometry}
            material={plantCInstancesMaterial}
          />
          <Instances limit={25} range={25} frustumCulled={false} frames={1}>
            <primitive object={groundGeometry} />
            <primitive object={groundInstancesMaterial} />
            {Array.from({ length: 25 }).map((_, index) => {
              const x = index % 5
              const y = Math.floor(index / 5)
              return (
                <Instance
                  // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                  key={index}
                  position={[-x * 13.4, 0, y * 13.4]}
                  scale={[x % 2 ? -1 : 1, 1, y % 2 ? -1 : 1]}
                />
              )
            })}
          </Instances>
        </group>
      </group>
    </>
  )
}
