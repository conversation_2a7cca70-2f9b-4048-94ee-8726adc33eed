import { Wrapper } from '~/app/(pages)/(components)/wrapper'
import { Frames } from '~/components/frames'
import { ScrollRestoration } from '~/components/scroll-restoration'
import { TheatreProjectProvider } from '~/libs/theatre'
import * as ScrollSpy from '../(components)/scroll-spy'
import { Chapter1 } from './(components)/chapter1'
import { Chapter2 } from './(components)/chapter2'
import { Chapter3 } from './(components)/chapter3'
import { Chapter4 } from './(components)/chapter4'
import { Intro } from './(components)/intro'
import s from './home.module.css'

export const THEATRE_CONFIG = '/config/Ibicash-2025-04-18T15_48_00.json'

export default function Home() {
  return (
    <TheatreProjectProvider id="Ibicash" config={THEATRE_CONFIG}>
      <ScrollSpy.Root>
        <Wrapper
          theme="dark"
          className={s.page}
          webgl={{
            postprocessing: true,
            orbitControls: true,
          }}
          // loader={process.env.NODE_ENV !== 'development'}
        >
          <Intro>
            <Chapter1 />
          </Intro>
          <Chapter2 />
          <Chapter3 />
          <Chapter4 />
        </Wrapper>
      </ScrollSpy.Root>
      <ScrollRestoration type="manual" />
      <Frames direction="vertical" objectID="vertical frames" />
      <Frames direction="horizontal" objectID="horizontal frames" />
    </TheatreProjectProvider>
  )
}

export function scenePointerEvent(sceneA, sceneB, progress, threshold) {
  if (progress < threshold) {
    if (sceneA) {
      sceneA.style.setProperty('pointer-events', 'auto')
    }
    if (sceneB) {
      sceneB.style.setProperty('pointer-events', 'none')
    }
  } else {
    if (sceneA) {
      sceneA.style.setProperty('pointer-events', 'none')
    }
    if (sceneB) {
      sceneB.style.setProperty('pointer-events', 'auto')
    }
  }
}
