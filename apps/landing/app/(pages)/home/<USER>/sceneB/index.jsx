'use client'

import { useAnimations, useGLTF } from '@react-three/drei'
import { useContext, useEffect, useState } from 'react'
import { DoubleSide, MeshBasicMaterial } from 'three'
import { useAudio } from '~/hooks/use-audio'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'
import { ChapterContext } from '~/libs/webgl/components/postprocessing/use-chapters'
import { updateTextures } from '~/libs/webgl/utils/textures'

export function SceneB() {
  const {
    nodes: {
      tree_main_branch: treeMainBranchNode,
      Trees_foliage: treesFoliageNode,
      vines_plants: vinesPlantsNode,
      // palms_BG: palmsBgNode,
      palm_FG: palmsFgNode,
      palms_BG001: palmsNode1,
      // palms_BG002: palmsNode2,
      // Trees_foliage001: treesFoliageNode1,
      // Trees_foliage002: treesFoliageNode2,
      Trees_foliage004: treesFoliageNode1,
      // Camera_scene_A_to_B: cameraNode,
    },
  } = useGLTF('/models/SceneB006.glb')

  const {
    scene,
    animations,

    nodes: { macaw: macawNode },
  } = useGLTF('/models/sceneB_macaw.glb')

  const { ref, mixer, actions } = useAnimations(animations)

  useEffect(() => {
    actions.ArmatureAction.play()
  }, [actions])

  const howl = useAudio()

  const { getRender, getProgress } = useContext(ChapterContext)

  useEffect(() => {
    if (!mixer || !howl) return

    function onLoop() {
      const render = getRender()
      const progress = getProgress()

      if (render && progress > 0.8 && progress < 0.95) {
        //   console.log('play')
        const vocalSound = `Sfx_Macaw_Vocal${Math.floor(Math.random() * 4 + 1)}`
        const headShakeSound = `Sfx_Macaw_HeadShake_Foley${Math.floor(
          Math.random() * 3 + 1
        )}`
        setTimeout(() => {
          const sound = howl.play(vocalSound)
          howl.volume(1, sound)
        }, Math.random() * 5000)
        setTimeout(() => {
          const sound = howl.play(headShakeSound)
          howl.volume(1, sound)
        }, 5900)
      }
    }

    mixer.addEventListener('loop', onLoop)

    return () => {
      mixer.removeEventListener('loop', onLoop)
    }
  }, [mixer, howl, getRender, getProgress])

  const macawTexture = useProgressiveKTX2(
    '/textures/sceneB/macaw_Bake1_CyclesBake_COMBINED.ktx2'
  )

  const [macawMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        map: macawTexture,
      })
  )

  useEffect(() => {
    macawNode.material = macawMaterial
  }, [macawNode, macawMaterial])

  const palmsFgTexture = useProgressiveKTX2('/textures/sceneB/palms_FG.ktx2')
  const palmsBgTexture = useProgressiveKTX2('/textures/sceneB/palms_BG.ktx2')
  const branchTexture = useProgressiveKTX2('/textures/sceneB/branch.ktx2')
  const foliageTexture = useProgressiveKTX2('/textures/sceneB/foliage.ktx2')
  const vinesTexture = useProgressiveKTX2('/textures/sceneB/vines.ktx2')

  const palmsFgAlphaTexture = useProgressiveKTX2(
    '/textures/sceneB/palms_FG_alpha.ktx2'
  )
  const palmsBgAlphaTexture = useProgressiveKTX2(
    '/textures/sceneB/palms_BG_alpha.ktx2'
  )
  const branchAlphaTexture = useProgressiveKTX2(
    '/textures/sceneB/branch_alpha.ktx2'
  )
  const foliageAlphaTexture = useProgressiveKTX2(
    '/textures/sceneB/foliage_alpha.ktx2'
  )
  const vinesAlphaTexture = useProgressiveKTX2(
    '/textures/sceneB/vines_alpha.ktx2'
  )

  const [palmsFgMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        alphaTest: 0.25,
        map: palmsFgTexture,
        alphaMap: palmsFgAlphaTexture,
        side: DoubleSide,
      })
  )
  const [palmsBgMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        alphaTest: 0.25,
        map: palmsBgTexture,
        alphaMap: palmsBgAlphaTexture,
      })
  )
  const [branchMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        alphaTest: 0.25,
        map: branchTexture,
        alphaMap: branchAlphaTexture,
      })
  )
  const [foliageMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        alphaTest: 0.25,
        map: foliageTexture,
        alphaMap: foliageAlphaTexture,
        side: DoubleSide,
      })
  )
  const [vinesMaterial] = useState(
    () =>
      new MeshBasicMaterial({
        alphaTest: 0.25,
        map: vinesTexture,
        alphaMap: vinesAlphaTexture,
        side: DoubleSide,
      })
  )

  updateTextures([
    macawTexture,
    palmsFgTexture,
    palmsBgTexture,
    branchTexture,
    foliageTexture,
    vinesTexture,
    palmsFgAlphaTexture,
    palmsBgAlphaTexture,
    branchAlphaTexture,
    foliageAlphaTexture,
    vinesAlphaTexture,
  ])

  return (
    <>
      {/* <primitive object={cameraNode} ref={cameraNodeRef} /> */}
      <primitive object={scene} ref={ref} matrixAutoUpdate={false} />
      <mesh matrixAutoUpdate={false}>
        <primitive object={treeMainBranchNode.geometry} attach="geometry" />
        <primitive object={branchMaterial} attach="material" />
      </mesh>
      <mesh matrixAutoUpdate={false}>
        <primitive object={treesFoliageNode.geometry} attach="geometry" />
        <primitive object={foliageMaterial} attach="material" />
      </mesh>
      <mesh matrixAutoUpdate={false}>
        <primitive object={vinesPlantsNode.geometry} attach="geometry" />
        <primitive object={vinesMaterial} attach="material" />
      </mesh>
      <mesh matrixAutoUpdate={false}>
        <primitive object={palmsFgNode.geometry} attach="geometry" />
        <primitive object={palmsFgMaterial} attach="material" />
      </mesh>
      {/* <mesh matrixAutoUpdate={false}>
        <primitive object={palmsBgNode.geometry} attach="geometry" />

        <primitive object={palmsBgMaterial} attach="material" />
      </mesh> */}
      <mesh matrixAutoUpdate={false}>
        <primitive object={palmsNode1.geometry} attach="geometry" />
        <primitive object={palmsBgMaterial} attach="material" />
      </mesh>
      {/* <mesh matrixAutoUpdate={false}>
        <primitive object={palmsNode2.geometry} attach="geometry" />
        <meshNormalMaterial side={DoubleSide} />
      </mesh> */}
      <mesh matrixAutoUpdate={false}>
        <primitive object={treesFoliageNode1.geometry} attach="geometry" />
        <primitive object={foliageMaterial} attach="material" />
        {/* <meshNormalMaterial side={DoubleSide} /> */}
      </mesh>
    </>
  )
}
