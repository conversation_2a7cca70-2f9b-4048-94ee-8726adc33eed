.nav {
  height: 100dvh;
  width: 100%;
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
  padding-block: var(--layout-margin);
  z-index: 2;

  .filled,
  .dashed {
    aspect-ratio: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    @include-media "mobile" {
      display: none;
    }
  }

  .filled {
    width: desktop-vh(1150px);
    height: desktop-vh(1150px);

    @media (min-aspect-ratio: 1.3) {
      width: desktop-vw(1157px);
      height: desktop-vw(1157px);
    }
  }

  .dashed {
    animation-duration: 250s;
    width: desktop-vh(1140px);
    height: desktop-vh(1140px);

    @media (min-aspect-ratio: 1.3) {
      width: desktop-vw(1133px);
      height: desktop-vw(1133px);
    }
  }

  .top,
  .bottom {
    position: relative;
  }

  .bottom {
    @include-media "desktop" {
      align-items: flex-end;
    }
  }

  .ctas {
    grid-column: 2 / -2;
    display: flex;
    pointer-events: auto;
    column-gap: mobile-vw(8px);

    @include-media "mobile" {
      grid-column: 2 / -2;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--layout-columns-gap);
      align-self: flex-end;

      > * {
        width: auto;
      }
    }

    @include-media "desktop" {
      grid-column: -4 / -1;
      justify-self: flex-end;
      column-gap: desktop-vw(8px);

      > :last-child {
        flex: 0;
      }
    }
  }

  .glyphs {
    position: relative;

    width: mobile-vw(20px);
    /* height: mobile-vw(180px); */

    @include-media "desktop" {
      width: desktop-vw(28px);
      height: desktop-vw(302px);
      grid-column: 1 / 3;
    }
  }

  .stats {
    grid-column: 2 / 4;
    display: flex;
    flex-direction: column;
    row-gap: desktop-vw(6px);
    justify-content: center;
    --color: var(--green);

    /* Hide stats until API is ready */
    visibility: hidden;

    @include-media "mobile" {
      grid-column: -4 / -1;
      row-gap: mobile-vw(6px);
      justify-self: flex-end;
    }

    @include-media "desktop" {
      margin-left: calc(-1 * var(--layout-margin));
    }

    .headline {
      @include-media "mobile" {
        text-align: right;
      }
    }

    &.red {
      --color: var(--red);

      .current {
        &::before {
          border-color: transparent transparent var(--red) transparent;
          transform: rotate(180deg);
        }
      }
    }

    .values {
      display: flex;
      align-items: center;
      column-gap: desktop-vw(10px);

      @include-media "mobile" {
        column-gap: mobile-vw(10px);
        justify-content: flex-end;
      }

      .current {
        display: flex;
        align-items: center;
        column-gap: desktop-vw(4px);
        color: var(--color);

        @include-media "mobile" {
          column-gap: mobile-vw(4px);
        }

        &::before {
          content: "";
          display: block;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 0 desktop-vw(3.5px) desktop-vw(4px) desktop-vw(3.5px);
          border-color: transparent transparent var(--color) transparent;

          @include-media "mobile" {
            border-width: 0 mobile-vw(3.5px) mobile-vw(4px) mobile-vw(3.5px);
          }
        }
      }
    }
  }

  .sound {
    justify-self: flex-end;
    align-self: flex-end;
    grid-column: span 1 / -1;
  }
}

.logotype {
  position: relative;
  grid-column: 1 / 3;
  pointer-events: auto;
  cursor: pointer;
  width: mobile-vw(53px);
  height: mobile-vw(48px);

  @include-media "desktop" {
    grid-column: 1 / 2;
    width: desktop-vw(59px);
    height: desktop-vw(54px);
    transition: transform 0.2s var(--gleasing);
  }

  > svg {
    width: 100%;
    height: 100%;
  }

  &:hover {
    transform: scale(1.025);
  }
}
