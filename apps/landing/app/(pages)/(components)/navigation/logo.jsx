'use client'

import { types, val } from '@theatre/core'
import cn from 'clsx'
import { useWindowSize } from 'hamo'
import { useLenis } from 'lenis/react'
import { useRef } from 'react'
import Logotype from '~/assets/svg/logotype.svg'
import { useAudioDebounced } from '~/hooks/use-audio'
import { mapRange } from '~/libs/maths'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import s from './navigation.module.css'

const end = 0.21 // match intro end value

export function Logo({ href = '/', className }) {
  return (
    <>
      <SVGLogo href={href} className={cn(s.logotype, className)} />
    </>
  )
}

function SVGLogo({ href = '/', className, ...props }) {
  const svgRef = useRef()
  const lenis = useLenis()
  const introSheet = useSheet('intro')
  const chapter1Sheet = useSheet('chapter1')
  const { height: windowHeight } = useWindowSize()
  const { play: playSound } = useAudioDebounced(300)

  useTheatre(
    introSheet,
    'nav logo',
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        svgRef.current.style.setProperty('opacity', progress)
      },
    }
  )

  return (
    <button
      href={href}
      className={className}
      type="button"
      onClick={() => {
        if (!chapter1Sheet?.sequence) return
        const length = val(chapter1Sheet.sequence.pointer.length)
        const value = mapRange(0, 1, end, -windowHeight, length * windowHeight)

        lenis.scrollTo(value)
      }}
      onPointerEnter={() => {
        playSound({ soundId: 'Sfx_UI_Click_Generic', volume: 0.4, rate: 1.75 })
      }}
      data-cursor="pointer"
      {...props}
    >
      <Logotype ref={svgRef} style={{ opacity: 0 }} />
    </button>
  )
}
