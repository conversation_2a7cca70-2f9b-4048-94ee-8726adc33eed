.wrapper {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  row-gap: mobile-vw(12px);

  /* @include-media ("desktop") {
    row-gap: desktop-vw(12px);
  } */

  @include-media ("mobile") {
    display: none;
  }

  .container {
    position: relative;
    height: mobile-vw(336px);
    width: fit-content;
    overflow-y: clip;

    @include-media ("desktop") {
      height: desktop-vw(336px);
    }
  }

  .arrow {
    cursor: pointer;
    width: mobile-vw(16px);
    aspect-ratio: 16 / 10;
    justify-self: flex-end;
    align-self: flex-end;
    margin-right: mobile-vw(24px);

    @include-media ("desktop") {
      width: desktop-vw(16px);
      margin-right: desktop-vw(24px);
    }

    &:last-of-type {
      transform: rotate(180deg);
    }

    path {
      transition: fill 150ms var(--gleasing);
    }

    &:hover {
      path {
        fill: var(--green);
      }
    }
  }

  .time {
    position: absolute;
    top: 50%;
    right: mobile-vw(40px);
    transform: translateY(-50%);
    width: 100%;
    text-align: center;

    @include-media ("desktop") {
      right: desktop-vw(40px);
    }
  }

  .spy {
    padding-inline: mobile-vw(24px);
    display: flex;
    flex-direction: column;
    will-change: transform;

    @include-media ("desktop") {
      padding-inline: desktop-vw(24px);
    }

    .item {
      cursor: pointer;
      position: relative;
      height: mobile-vw(16px);
      width: mobile-vw(24px);

      @include-media ("desktop") {
        height: desktop-vw(16px);
        width: desktop-vw(24px);
      }

      &::before,
      &::after {
        content: "";
        width: 100%;
        height: mobile-vw(1px);
        position: absolute;
        top: 50%;
        right: 0;
        transition-timing-function: var(--gleasing);
        transform: translateY(-50%) scaleX(calc(100% * 16 / 24));
        transform-origin: right;

        @include-media ("desktop") {
          height: desktop-vw(1px);
        }
      }

      &::before {
        background-color: rgba(255, 255, 255, 0.25);
        transition-property: transform;
        transition-duration: 150ms;
        will-change: transform;
      }

      &::after {
        background-color: #acff46;
        opacity: 0;
        transition-property: opacity;
        transition-delay: 20ms;
        transition-duration: 250ms;
        will-change: opacity, transform;
      }

      /* First Siblings */
      &:not(:hover) {
        &:has(+ .item.active) {
          &::before,
          &::after {
            transform: scaleX(calc(100% * 20 / 24));
          }
        }
      }

      &.active + .item {
        &::before,
        &::after {
          transform: scaleX(calc(100% * 20 / 24));
        }
      }

      /* Active */
      &.active {
        &::before {
          transform: scaleX(1);
        }

        &::after {
          opacity: 1;
          transform: scaleX(1);
          transition: none !important;
        }
      }

      &:hover {
        &::after {
          opacity: 1;
          transition: none !important;
        }
      }
    }
  }
}
