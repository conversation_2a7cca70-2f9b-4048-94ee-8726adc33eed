'use client'

import { onChange, types } from '@theatre/core'
import cn from 'clsx'
import { useRect, useWindowSize } from 'hamo'
import { useLenis } from 'lenis/react'
import dynamic from 'next/dynamic'
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useAudioDebounced } from '~/hooks/use-audio'
import { useStore } from '~/libs/store'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { desktopVW } from '~/libs/utils'
import s from './scroll-spy.module.css'

const ArrowSVG = dynamic(() => import('~/assets/svg/arrow.svg'), {
  ssr: false,
})

const ScrollSpyContext = createContext({})

export function useScrollSpy() {
  return useContext(ScrollSpyContext)
}

const startTime = 9.3
const linesPerHour = 1
const timeSpan = 24
const totalLines = linesPerHour * timeSpan
const scenes = [
  {
    label: 'Scene 1',
    time: '09:05',
    target: 0.0295,
  },
  {
    label: 'Scene 2',
    time: '14:00',
    target: 0.2377,
  },
  {
    label: 'Scene 3',
    time: '16:20',
    target: 0.3348,
  },
  {
    label: 'Scene 4',
    time: '20:30',
    target: 0.5088,
  },
  {
    label: 'Scene 5',
    time: '00:30',
    target: 0.6754,
  },
  {
    label: 'Scene 6',
    time: '04:45',
    target: 0.8525,
  },
]

function Root({ children }) {
  const listRef = useRef(null)
  const itemsRef = useRef([])
  const currentItemRef = useRef(null)
  const timeRef = useRef(null)
  const scrollRef = useRef(0)
  const [sections, setSections] = useState(new Map())
  const [listRectRef, listRect] = useRect()
  const { height: windowHeight, width: windowWidth } = useWindowSize()
  const { play: playSound } = useAudioDebounced(100)

  const sectionsValues = useMemo(
    () => Array.from(sections.values()),
    [sections]
  )

  const totalHeight = useMemo(
    () => sectionsValues.at(-1)?.totalHeight,
    [sectionsValues]
  )

  const times = useMemo(() => {
    const spaceBetween = totalHeight / totalLines

    const entries = new Array(totalLines).fill(null).map((_, index) => {
      const totalMinutes = startTime * 60 + index * (60 / linesPerHour)
      const normalizedMinutes = totalMinutes % (24 * 60)
      const hours = Math.floor(normalizedMinutes / 60)
      const minutes = normalizedMinutes % 60
      const step = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`

      return {
        step,
        scrollTarget: index * spaceBetween,
        progress: index / totalLines,
      }
    })

    return entries
  }, [totalHeight])

  useLenis(
    ({ scroll }) => {
      // Timeline transform
      const timelineStep = desktopVW(16, windowWidth)
      const siteProgress = scroll / totalHeight
      const timelinePixels = siteProgress * listRect.height
      const delta = Math.abs(timelinePixels - scrollRef.current)
      const current = Math.round(timelinePixels / timelineStep) * timelineStep
      const currentIndex = Math.round(current / timelineStep)

      const introCompleted = useStore.getState().introCompleted

      //Snap to closest item
      if (delta < 0.2 || !introCompleted) {
        listRef.current.style.setProperty(
          'transition',
          'transform 0.5s ease-out'
        )

        listRef.current.style.setProperty(
          'transform',
          `translate3d(0, ${-1 * current}px, 0)`
        )
      } else {
        listRef.current.style.setProperty('transition', 'none')

        //Free scroll
        listRef.current.style.setProperty(
          'transform',
          `translate3d(0, ${-1 * timelinePixels}px, 0)`
        )
      }
      scrollRef.current = timelinePixels

      currentItemRef.current?.deactivate()
      const currentItem = itemsRef.current[currentIndex]
      currentItem?.activate(currentIndex)
      currentItemRef.current = currentItem

      timeRef.current.textContent = progressToTime(siteProgress)
    },
    [windowWidth, totalHeight, listRect]
  )

  const getChapterScrollOffset = useCallback(
    (label) => {
      const current = sectionsValues.findIndex(
        ({ label: currentLabel }) => currentLabel === label
      )

      if (current === -1 || current === 0) return 0

      return sectionsValues.at(current - 1)?.totalHeight
    },
    [sectionsValues, windowHeight]
  )

  const wrapperRef = useRef(null)
  const introSheet = useSheet('intro')
  useTheatre(
    introSheet,
    'timeline',
    { opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }) },
    {
      onValuesChange: ({ opacity }) => {
        wrapperRef.current?.style?.setProperty('opacity', opacity)
      },
    }
  )

  return (
    <ScrollSpyContext.Provider
      value={{
        sectionsValues,
        totalHeight,
        setSections,
        getChapterScrollOffset,
      }}
    >
      {children}
      <div className={s.wrapper} ref={wrapperRef}>
        <ArrowButton
          direction={-1}
          totalHeight={totalHeight}
          data-cursor="pointer"
        />
        <div className={s.container} data-cursor="pointer">
          <p className={cn('pxs', s.time)} ref={timeRef} />
          <ul
            className={s.spy}
            ref={listRef}
            onPointerMove={() => {
              playSound({
                soundId: 'Sfx_UI_Click_Generic',
                volume: 0.4,
                rate: 1.75,
              })
            }}
          >
            {times.slice(-11, -1).map((props) => (
              <ListItem key={`-top-${props.step}`} {...props} />
            ))}
            <div ref={listRectRef}>
              {times.map((props, index) => (
                <ListItem
                  key={props.step}
                  ref={(node) => {
                    itemsRef.current[index] = { ...node, ...props }
                  }}
                  {...props}
                />
              ))}
            </div>
            {times.slice(0, 11).map((props) => (
              <ListItem key={`-bottom-${props.step}`} {...props} />
            ))}
          </ul>
        </div>
        <ArrowButton
          direction={1}
          totalHeight={totalHeight}
          data-cursor="pointer"
        />
      </div>
    </ScrollSpyContext.Provider>
  )
}

function ListItem({ ref, scrollTarget }) {
  const lenis = useLenis()
  const itemRef = useRef(null)

  useImperativeHandle(ref, () => ({
    node: itemRef.current,
    activate: () => itemRef.current?.classList?.add(s.active),
    deactivate: () => itemRef.current?.classList?.remove(s.active),
  }))

  return (
    <li
      className={`${s.item} pxs`}
      ref={itemRef}
      type="button"
      onClick={() => {
        lenis.scrollTo(scrollTarget)
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          lenis.scrollTo(scrollTarget)
        }
      }}
    />
  )
}

function ArrowButton({ direction, totalHeight, ...props }) {
  const [target, setTarget] = useState(0)
  const { play: playSound } = useAudioDebounced(300)

  const lenis = useLenis(
    ({ scroll }) => {
      const scrollProgress = scroll / totalHeight
      const sceneIndex = scenes.findIndex(
        ({ target }) => target >= scrollProgress + 0.01 * direction
      )

      const currentIndex = Math.max(sceneIndex, 0)
      const previousIndex = currentIndex - 1

      const previous = previousIndex < 0 ? scenes.length - 1 : previousIndex
      const next = currentIndex === scenes.length ? 0 : currentIndex

      if (direction === -1) {
        setTarget(scenes[previous].target * totalHeight)
        return
      }

      setTarget(scenes[next].target * totalHeight)
    },
    [totalHeight]
  )

  return (
    <button
      type="button"
      className={s.arrow}
      onClick={() => {
        lenis.scrollTo(target)
      }}
      onPointerEnter={() => {
        playSound({ soundId: 'Sfx_UI_Click_Generic', volume: 0.4, rate: 1.75 })
      }}
      {...props}
    >
      <ArrowSVG />
    </button>
  )
}

function Section({ children, label, sheet, ...props }) {
  const [rectRef, rect] = useRect()
  const { setSections } = useScrollSpy()

  useEffect(() => {
    if (!rect?.height) return

    setSections((prev) => {
      // Create a new Map instance to trigger a re-render
      const newMap = new Map(prev)
      const values = Array.from(newMap.values())
      const currentIndex = values.findIndex(
        ({ label: currentLabel }) => currentLabel === label
      )

      const totalHeight =
        currentIndex === 0 ? 0 : values.at(currentIndex - 1)?.totalHeight || 0

      newMap.set(label, {
        label,
        totalHeight: totalHeight + rect.height,
      })
      return newMap
    })
  }, [rect])

  useEffect(() => {
    if (sheet) {
      onChange(sheet.sequence.pointer.length, (length) => {
        rect.element.style.height = `${length * 100}vh`
        rect?.resize()
      })
    }
  }, [sheet, rect])

  return (
    <section
      className="section"
      ref={(el) => {
        rectRef(el)
      }}
      {...props}
    >
      {children}
    </section>
  )
}

export { Root, Section }

function progressToTime(progress) {
  const startMinutes = startTime * 60
  const totalMinutes = timeSpan * 60
  const currentMinutes = startMinutes + progress * totalMinutes

  const normalizedMinutes = currentMinutes % (24 * 60)
  const hours = Math.floor(normalizedMinutes / 60)
  const minutes = Math.floor(normalizedMinutes % 60)

  // Format as HH:MM
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}
