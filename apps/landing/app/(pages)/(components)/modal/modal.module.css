.modal {
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: var(--black);
  display: flex;
  transition: opacity 600ms var(--gleasing);
  padding: var(--layout-margin);
  z-index: 2;

  &:not(.open) {
    opacity: 0;

    &.pointer-none {
      pointer-events: none;
    }
  }
}

.inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 800ms var(--gleasing);
  z-index: 2;
  display: grid;

  > * {
    grid-column-start: 1;
    grid-row-start: 1;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  inset: 0;
  pointer-events: none;

  @include-media ('desktop') {
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
  }

  .filled,
  .dashed {
    aspect-ratio: 1;
    position: absolute;
  }

  .filled {
    width: desktop-vh(1157px);
    stroke-dasharray: 2 2;

    svg {
      position: relative;
      animation-duration: 75s;

      circle {
        opacity: 0.15;
      }
    }

    @include-media ('mobile') {
      display: none;
    }
  }

  .dashed {
    width: desktop-vh(1133px);

    svg {
      animation-duration: 75s;
      animation-direction: reverse;

      path {
        opacity: 0.15;
      }
    }

    @include-media ('mobile') {
      animation-duration: 100s;
      position: absolute;
      width: mobile-vw(1133px);
      top: -35%;
      left: 40%;

      path {
        opacity: 0.15;
      }
    }
  }
}
