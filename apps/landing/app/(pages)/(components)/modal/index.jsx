'use client'

import Elastica, {
  AxisAlignedBoundaryBox,
  updatePresets,
} from '@darkroom.engineering/elastica/react'
import cn from 'clsx'
import dynamic from 'next/dynamic'
import { Suspense, useCallback, useEffect, useMemo, useRef } from 'react'
import { CodeScreen } from '~/components/code-screen'
import { Waitlist } from '~/components/waitlist'
import { useAudio } from '~/hooks/use-audio'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useKeyboardShortcut } from '~/hooks/use-keyboard-shortcut'
import { useSearchParamsMethods } from '~/hooks/use-url-params'
import { useStore } from '~/libs/store'
import s from './modal.module.css'

export function Modal() {
  return (
    <Suspense>
      <ModalComponent />
    </Suspense>
  )
}

function ModalComponent() {
  const timeoutRef = useRef(null)
  const modalRef = useRef()
  const { currentParams, deleteParams } = useSearchParamsMethods({
    reload: false,
    subscribeTo: ['modal'],
  })
  const { isDesktop } = useDeviceDetection()
  const deleteModal = useCallback(
    () => deleteParams({ key: 'modal' }),
    [deleteParams]
  )

  const currentModal = currentParams?.modal

  useKeyboardShortcut(['Escape', 'Esc'], deleteModal)

  const loaderLoaded = useStore((state) => state.loaderLoaded)

  useEffect(() => {
    modalRef.current.classList.add(s['pointer-none'])

    return () => {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  const howl = useAudio()

  useEffect(() => {
    if (!howl || !loaderLoaded) return

    howl.bend(currentModal && loaderLoaded ? 0.33 : 1)
  }, [currentModal, loaderLoaded, howl])

  return (
    <dialog
      className={cn(
        s.modal,
        !!currentModal &&
          (process.env.NODE_ENV === 'development' || loaderLoaded) &&
          s.open,
        !!currentModal && !isDesktop && s.open
      )}
      ref={modalRef}
      onTransitionEnd={(e) => {
        if (
          e.target === modalRef.current &&
          e.propertyName === 'opacity' &&
          !currentModal
        ) {
          timeoutRef.current = setTimeout(() => {
            //this way delays pointer none for not listening nav
            // buttons on close
            modalRef.current.classList.add(s['pointer-none'])
          }, 300)
        }
      }}
    >
      <div className={s.inner}>
        {/* Define the modal content here */}
        <CodeScreen />
        <Waitlist />
        <Circles />
      </div>
    </dialog>
  )
}

export function useModal() {
  const { currentParams, deleteParams, setParams } = useSearchParamsMethods({
    reload: false,
    subscribeTo: ['modal'],
  })

  return {
    current: currentParams?.modal || null,
    close: () => {
      deleteParams({ key: 'modal' })
    },
    open: (value) => {
      setParams({ key: 'modal', value })
    },
  }
}

const Circle = dynamic(() => import('~/assets/svg/circle.svg'), { ssr: false })
const DashedCircle = dynamic(() => import('~/assets/svg/dashed-circle.svg'), {
  ssr: false,
})

const params = {
  gridSize: 1,
  collisions: false,
  borders: 'rigid',
  speed: 0.025,
}

function Circles({ className }) {
  const elasticaRef = useRef(null)
  const { current: currentModal } = useModal()

  useEffect(() => {
    if (currentModal !== 'code' && currentModal !== 'waitlist') {
      elasticaRef.current?.pause()
      return
    }

    elasticaRef.current?.play()
  }, [currentModal])

  return (
    <div className={cn(s.background, className)}>
      <DashedCircle className={cn(s.dashed, 'rotate-infinite mobile-only')} />
      <Elastica
        ref={elasticaRef}
        config={params}
        update={updatePresets.dvdScreenSaver}
        initialCondition={({ positions, velocities, container }) => {
          positions[0] = [container.width * 0.5, container.height * -0.6]
          positions[1] = [container.width * 0.6, container.height * 1]
          velocities[0] = [params.speed, params.speed]
          velocities[1] = [params.speed * -1, params.speed * -1]
        }}
      >
        <AxisAlignedBoundaryBox className={s.filled}>
          <Circle className="rotate-infinite" />
        </AxisAlignedBoundaryBox>
        <AxisAlignedBoundaryBox className={s.dashed}>
          <DashedCircle className="rotate-infinite" />
        </AxisAlignedBoundaryBox>
      </Elastica>
    </div>
  )
}
