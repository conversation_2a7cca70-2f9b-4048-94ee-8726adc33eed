'use client'

import type { LenisOptions } from 'lenis'
import 'lenis/dist/lenis.css'
import type { LenisRef } from 'lenis/react'
import { React<PERSON>enis, useLenis } from 'lenis/react'
import { useEffect, useRef } from 'react'
import { useTempus } from 'tempus/react'
import { useStore } from '~/libs/store'
import { useModal } from '../modal'

interface LenisProps {
  root?: boolean
  options: LenisOptions
  children?: React.ReactNode
}

export function Lenis({ root, options, children, ...props }: LenisProps) {
  const lenisRef = useRef<LenisRef>(null)
  const introCompleted = useStore((state) => state.introCompleted)
  const lenis = useLenis()
  const { current: currentModal } = useModal()

  useTempus((time: number) => {
    if (lenisRef.current?.lenis) {
      lenisRef.current.lenis.raf(time)
    }
  })

  useEffect(() => {
    if (!introCompleted || currentModal === 'code') {
      lenis?.stop()
    } else {
      lenis?.start()
    }
  }, [lenis, introCompleted, currentModal])

  return (
    <ReactLenis
      ref={lenisRef}
      root={root}
      options={{
        ...options,
        lerp: options?.lerp ?? 0.125,
        autoRaf: false,
        anchors: true,
        infinite: true,
        syncTouch: true,
        prevent: (node: Element | null) =>
          node?.nodeName === 'VERCEL-LIVE-FEEDBACK' ||
          node?.id === 'theatrejs-studio-root' ||
          !!node?.classList.contains('sc-default'),
      }}
      {...props}
    />
  )
}
