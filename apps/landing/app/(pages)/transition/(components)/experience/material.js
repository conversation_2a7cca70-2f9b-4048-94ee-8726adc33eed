import { MeshBasicMaterial, Vector2 } from 'three'
import { BLEND } from '~/libs/webgl/utils/blend'
import { NOISE } from '~/libs/webgl/utils/noise'

export class VoronoiMaterial extends MeshBasicMaterial {
  constructor({ flowmap = null, ...params } = {}) {
    super(params)

    this.uniforms = {
      uTime: { value: 0 },
      uResolution: { value: new Vector2(1, 1) },
      uTexture1: { value: null },
      uTexture2: { value: null },
      uProgress: { value: 0 },
      uVoronoiTexture: { value: null },
    }

    this.defines = {
      USE_UV: '',
    }

    this.speed = 0.0005
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling: ${this.constructor.name}`)
    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      /* glsl */ `void main() {`,
      /* glsl */ `vec2 random2( vec2 p ) {
          return fract(sin(vec2(dot(p,vec2(127.1,311.7)),dot(p,vec2(269.5,183.3))))*43758.5453);
      }



      ${BLEND.COLOR_DODGE}
      ${BLEND.NORMAL}
      ${BLEND.ADD}
      ${NOISE.PERLIN_3D}

      uniform float uTime;
      uniform vec2 uResolution;
      uniform sampler2D uTexture1;
      uniform sampler2D uTexture2;
      uniform float uProgress;
      uniform sampler2D uVoronoiTexture;

      void main() {`
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      /* glsl */ `vec4 diffuseColor = vec4( diffuse, opacity );`,
      /* glsl */ `
      
        vec4 texture1 = texture(uTexture1, vUv);
        vec4 texture2 = texture(uTexture2, vUv);
        vec4 voronoi = texture(uVoronoiTexture, vUv);
      
      vec4 diffuseColor = vec4( diffuse, opacity );

    //   voronoi.r *= 5.;
    //   voronoi.r = clamp(voronoi.r, 0., 1.);

    float voronoiMask = vUv.y + 1. - (uProgress * 2.);
    voronoiMask = smoothstep(0., 1., voronoiMask);

      if(voronoi.r > (1. - uProgress)) {
        voronoi.r = voronoiMask   ;
      }

      diffuseColor.rgb = vec3(voronoi.r);

      

    //   diffuseColor = mix(texture1, texture2, voronoi.r);




    //   diffuseColor.rgb = vec3(voronoiMask);

      `
    )

    // console.log(shader.fragmentShader)
  }

  setResolution(x, y) {
    this.uniforms.uResolution.value.set(x, y)
  }

  raf(time) {
    this.uniforms.uTime.value = time * this.speed
  }

  setCursor(x, y) {
    this.uniforms.uCursor.value.set(x, y)
  }

  get cursor() {
    return this.uniforms.uCursor.value
  }

  set debug(value) {
    this.uniforms.uDebug.value = value
  }

  get debug() {
    return this.uniforms.uDebug.value
  }

  set cellSize(value) {
    this.uniforms.uCellSize.value = value
  }

  get cellSize() {
    return this.uniforms.uCellSize.value
  }

  set gradient(value) {
    this.uniforms.uGradient.value = value
  }

  get gradient() {
    return this.uniforms.uGradient.value
  }

  set background(value) {
    this.uniforms.uBackground.value.set(value.toString())
  }

  get background() {
    return this.uniforms.uBackground.value
  }

  set excitement(value) {
    this.uniforms.uExcitement.value = value
  }

  get excitement() {
    return this.uniforms.uExcitement.value
  }

  set flowmap(flowmap) {
    // this.uniforms.uFlowmap.value = value
    this.uniforms.uFlowmap = flowmap?.uniform || { value: null }
  }

  get flowmap() {
    return this.uniforms.uFlowmap.value
  }

  set haloColor(value) {
    this.uniforms.uHaloColor.value.set(value.toString())
  }

  get haloColor() {
    return this.uniforms.uHaloColor.value
  }

  set deformationFrequency(value) {
    this.uniforms.uDeformationFrequency.value = value
  }

  get deformationFrequency() {
    return this.uniforms.uDeformationFrequency.value
  }

  set deformationAmplitude(value) {
    this.uniforms.uDeformationAmplitude.value = value
  }

  get deformationAmplitude() {
    return this.uniforms.uDeformationAmplitude.value
  }

  set texture1(value) {
    this.uniforms.uTexture1.value = value
  }

  set texture2(value) {
    this.uniforms.uTexture2.value = value
  }

  set progress(value) {
    this.uniforms.uProgress.value = value
  }

  set voronoiTexture(value) {
    this.uniforms.uVoronoiTexture.value = value
  }

  get voronoiTexture() {
    return this.uniforms.uVoronoiTexture.value
  }
}
