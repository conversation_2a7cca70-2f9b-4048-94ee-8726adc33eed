'use client'

import {
  OrthographicCamera,
  RenderTexture,
  useTexture,
} from '@react-three/drei'
import { use<PERSON>rame, useThree } from '@react-three/fiber'
import { useLenis } from 'lenis/react'
import { useEffect, useState } from 'react'
import { LinearFilter, SRGBColorSpace } from 'three'
import { useFlowmap } from '~/libs/webgl/components/flowmap'
import { TransitionMaterial } from './material'

export function WebGLExperience() {
  // const meshRef = useRef()

  // useEffect(() => {
  //   console.log(meshRef.current)
  // }, [])

  const viewport = useThree((state) => state.viewport)

  const scene1Texture = useTexture('/textures/scene1.png')
  const scene2Texture = useTexture('/textures/scene2.png')

  console.log(viewport)

  scene1Texture.colorSpace = SRGBColorSpace
  scene1Texture.minFilter = LinearFilter
  scene1Texture.magFilter = LinearFilter
  scene1Texture.needsUpdate = true
  scene2Texture.colorSpace = SRGBColorSpace
  scene2Texture.minFilter = LinearFilter
  scene2Texture.magFilter = LinearFilter
  scene2Texture.needsUpdate = true

  const [scene1RTT, setScene1RTT] = useState(null)
  const [scene2RTT, setScene2RTT] = useState(null)

  const flowmap = useFlowmap('flowmap')
  const [material] = useState(() => new TransitionMaterial(flowmap))

  material.setResolution(viewport.width, viewport.height)

  useFrame((time) => {
    material.raf(time)
  })

  useEffect(() => {
    material.texture1 = scene1RTT
    material.texture2 = scene2RTT
  }, [material, scene1RTT, scene2RTT])

  useLenis(({ progress }) => {
    material.progress = progress
  })

  return (
    <>
      <color attach="background" args={['black']} />
      <mesh scale={[viewport.width * 0.9, viewport.height * 0.9, 1]}>
        <planeGeometry />
        <primitive object={material} attach="material" />
        {/* <meshBasicMaterial color="red" /> */}
      </mesh>

      <RenderTexture
        ref={(node) => {
          setScene1RTT(node)
        }}
      >
        <OrthographicCamera
          makeDefault
          manual
          aspect={1 / 1}
          left={-0.5}
          right={0.5}
          top={0.5}
          bottom={-0.5}
          position={[0, 0, 5]}
        />
        <mesh>
          <boxGeometry />
          <meshBasicMaterial map={scene1Texture} />
        </mesh>
      </RenderTexture>

      <RenderTexture
        ref={(node) => {
          setScene2RTT(node)
        }}
      >
        <OrthographicCamera
          makeDefault
          manual
          aspect={1 / 1}
          left={-0.5}
          right={0.5}
          top={0.5}
          bottom={-0.5}
          position={[0, 0, 5]}
        />
        <mesh>
          <boxGeometry />
          <meshBasicMaterial map={scene2Texture} />
        </mesh>
      </RenderTexture>
    </>
  )
}
