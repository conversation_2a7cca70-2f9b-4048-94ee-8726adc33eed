'use client'

import {
  OrthographicCamera,
  RenderTexture,
  useTexture,
} from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useLenis } from 'lenis/react'
import { useEffect, useState } from 'react'
import { useTempus } from 'tempus/react'
import { SRGBColorSpace } from 'three'
import { useFlowmap } from '~/libs/webgl/components/flowmap'
import { VoronoiMaterial } from './material'

export function WebGLExperience() {
  const viewport = useThree((state) => state.viewport)

  const flowmap = useFlowmap('flowmap')
  const [material] = useState(
    () =>
      new VoronoiMaterial({
        flowmap,
      })
  )

  material.setResolution(viewport.width, viewport.height)

  useTempus((time) => {
    material.raf(time)
  })

  // const [colors, setColors] = useState(['#010E0D', '#F2F2F2'])

  // const sheet = useCurrentSheet()
  // useTheatre(
  //   sheet,
  //   'voronoi',
  //   {
  //     debug: false,
  //     cellSize: types.number(10, {
  //       range: [3, 100],
  //       nudgeMultiplier: 1,
  //       label: 'cell size',
  //     }),
  //     background: types.rgba(
  //       {
  //         r: 1 / 255,
  //         g: 14 / 255,
  //         b: 13 / 255,
  //         a: 1,
  //       },
  //       {
  //         label: 'background',
  //       }
  //     ),
  //     haloColor: types.rgba({
  //       r: 0,
  //       g: 96 / 255,
  //       b: 48 / 255,
  //       a: 1,
  //     }),
  //     excitement: types.number(0.7, {
  //       range: [0, 1],
  //       nudgeMultiplier: 0.01,
  //       label: 'excitement',
  //     }),
  //     speed: types.number(0.5, {
  //       range: [0, 2],
  //       nudgeMultiplier: 0.01,
  //       label: 'speed',
  //     }),
  //     deformationFrequency: types.number(1, {
  //       range: [0, 10],
  //       nudgeMultiplier: 0.01,
  //       label: 'deformation frequency',
  //     }),
  //     deformationAmplitude: types.number(0.1, {
  //       range: [0, 1],
  //       nudgeMultiplier: 0.01,
  //       label: 'deformation amplitude',
  //     }),
  //   },
  //   {
  //     onValuesChange: ({
  //       debug,
  //       cellSize,
  //       background,
  //       excitement,
  //       haloColor,
  //       speed,
  //       deformationFrequency,
  //       deformationAmplitude,
  //     }) => {
  //       material.debug = debug
  //       material.cellSize = cellSize
  //       material.background = background
  //       material.haloColor = haloColor
  //       material.excitement = excitement
  //       material.speed = speed * 0.001
  //       material.deformationFrequency = deformationFrequency
  //       material.deformationAmplitude = deformationAmplitude
  //     },
  //   }
  // )

  const [scene1RTT, setScene1RTT] = useState(null)
  const [scene2RTT, setScene2RTT] = useState(null)

  const scene1Texture = useTexture('/textures/scene1.png')
  const scene2Texture = useTexture('/textures/scene2.png')
  const voronoiTexture = useTexture('/textures/voronoi2.png')

  useEffect(() => {
    material.texture1 = scene1RTT
    material.texture2 = scene2RTT
    voronoiTexture.colorSpace = SRGBColorSpace
    voronoiTexture.needsUpdate = true
    material.voronoiTexture = voronoiTexture
  }, [material, scene1RTT, scene2RTT, voronoiTexture])

  useLenis(({ progress }) => {
    material.progress = progress
  })

  return (
    <>
      <mesh scale={[viewport.width, viewport.height, 1]}>
        <planeGeometry />
        <primitive object={material} attach="material" />
        {/* <meshBasicMaterial color="red" /> */}
      </mesh>

      <RenderTexture
        ref={(node) => {
          setScene1RTT(node)
        }}
      >
        <OrthographicCamera
          makeDefault
          manual
          aspect={1 / 1}
          left={-0.5}
          right={0.5}
          top={0.5}
          bottom={-0.5}
          position={[0, 0, 5]}
        />
        <mesh>
          <boxGeometry />
          <meshBasicMaterial map={scene1Texture} />
        </mesh>
      </RenderTexture>

      <RenderTexture
        ref={(node) => {
          setScene2RTT(node)
        }}
      >
        <OrthographicCamera
          makeDefault
          manual
          aspect={1 / 1}
          left={-0.5}
          right={0.5}
          top={0.5}
          bottom={-0.5}
          position={[0, 0, 5]}
        />
        <mesh>
          <boxGeometry />
          <meshBasicMaterial map={scene2Texture} />
        </mesh>
      </RenderTexture>
    </>
  )
}
