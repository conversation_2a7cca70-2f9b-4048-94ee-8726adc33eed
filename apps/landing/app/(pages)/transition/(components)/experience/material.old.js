import { Color, MeshBasicMaterial, Vector2 } from 'three'
import { BLEND } from '~/libs/webgl/utils/blend'
import { NOISE } from '~/libs/webgl/utils/noise'

export class VoronoiMaterial extends MeshBasicMaterial {
  constructor({ flowmap = null, ...params } = {}) {
    super(params)

    this.uniforms = {
      uTexture1: { value: null },
      uTexture2: { value: null },
      uProgress: { value: 0 },
      uTime: { value: 0 },
      uDebug: { value: false },
      uResolution: { value: new Vector2(1, 1) },
      uCellSize: { value: 10 },
      uBackground: { value: new Color(1, 0, 0) },
      uExcitement: { value: 0.7 },
      uFlowmap: flowmap?.uniform || { value: null },
      uHaloColor: { value: new Color(0, 1, 0) },
      uDeformationFrequency: { value: 1 },
      uDeformationAmplitude: { value: 0.1 },
    }

    this.defines = {
      USE_UV: '',
    }

    this.speed = 0.0005
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling: ${this.constructor.name}`)
    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.fragmentShader = shader.fragmentShader.replace(
      /* glsl */ `void main() {`,
      /* glsl */ `vec2 random2( vec2 p ) {
          return fract(sin(vec2(dot(p,vec2(127.1,311.7)),dot(p,vec2(269.5,183.3))))*43758.5453);
      }



      ${BLEND.COLOR_DODGE}
      ${BLEND.NORMAL}
      ${BLEND.ADD}
      ${NOISE.PERLIN_3D}

      uniform float uTime;
      uniform bool uDebug;
      uniform vec2 uResolution;
      uniform float uCellSize;
      uniform vec3 uBackground;
      uniform float uExcitement;
      uniform sampler2D uFlowmap;
      uniform vec3 uHaloColor;
      uniform float uDeformationFrequency;
      uniform float uDeformationAmplitude;
      uniform sampler2D uTexture1;
      uniform sampler2D uTexture2;
      uniform float uProgress;

#define ANIMATE

vec3 voronoi( in vec2 x )
{
    vec2 ip = floor(x);
    vec2 fp = fract(x);

    //----------------------------------
    // first pass: regular voronoi
    //----------------------------------
	vec2 mg, mr;

    float md = 8.0;
    for( int j=-1; j<=1; j++ )
    for( int i=-1; i<=1; i++ )
    {
        vec2 g = vec2(float(i),float(j));
		vec2 o = random2( ip + g );
		#ifdef ANIMATE
        o = 0.5 + 0.5*sin( uTime + 6.2831*o );
        #endif	
        vec2 r = g + o - fp;
        float d = dot(r,r);

        if( d<md )
        {
            md = d;
            mr = r;
            mg = g;
        }
    }

    //----------------------------------
    // second pass: distance to borders
    //----------------------------------
    // md = 8.0;
    // for( int j=-2; j<=2; j++ )
    // for( int i=-2; i<=2; i++ )
    // {
    //     vec2 g = mg + vec2(float(i),float(j));
	// 	vec2 o = random2( ip + g );
	// 	#ifdef ANIMATE
    //     o = 0.5 + 0.5*sin( uTime + 6.2831*o );
    //     #endif	
    //     vec2 r = g + o - fp;

    //     if( dot(mr-r,mr-r)>0.00001 )
    //     md = min( md, dot( 0.5*(mr+r), normalize(r-mr) ) );
    // }

    return vec3( md, mr );
}

      void main() {`
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      /* glsl */ `vec4 diffuseColor = vec4( diffuse, opacity );`,
      /* glsl */ `
      

      
      vec4 diffuseColor = vec4( diffuse, opacity );

        vec2 aspect = vec2(uResolution.x / uResolution.y, 1);

        vec2 uv = vUv;
        // uv.y = clamp(uv.y, 0., uProgress + sin(vUv.x * 10.) * 0.1);

        vec2 st = uv * aspect;


        // st.x += perlin_3d(vec3(st.x * uDeformationFrequency, -10., uTime * 0.1)) * uDeformationAmplitude;
        // st.y += perlin_3d(vec3(st.y * uDeformationFrequency, 10., uTime * 0.1)) * uDeformationAmplitude;

        // vec3 color = vec3(.0);
        float value = 0.;

        // Scale
        st *= uCellSize;
      
        vec2 i_st = floor(st);
        vec2 f_st = fract(st);

        float m_dist = 1.;
        vec2 m_point;


        vec4 flowmap = texture2D(uFlowmap, vUv);
        flowmap.b = clamp(flowmap.b, 0., 1.);
        float noise = perlin_3d(vec3(vUv * 10., uTime));
        // float circle = 1.-smoothstep(0., 0.5 + noise*0.2, length((vUv - uCursor) * aspect));



        for (int y= -1; y <= 1; y++) {
            for (int x= -1; x <= 1; x++) {
                // Neighbor place in the grid
                vec2 neighbor = vec2(float(x),float(y));

                // Random position from current + neighbor place in the grid
                vec2 r = random2(i_st + neighbor);
                vec2 point = 0.5 + r;


                // float radius = (r.y - 0.5) * 2.;

                float direction = 1.;

                if(r.y<0.5) {
                  direction = -1.;
                }

                direction *= r.x;

                


                float radius = r.y * uExcitement;
                // float offset = flowmap.b * 0.1;
                // float offset = circle;

                // float cellCircle = 1.-smoothstep(0., 0.5, length((((i_st + point) / uCellSize) - uCursor) * aspect)) * 0.01;
                float time = uTime * direction;
                // offset = clamp(offset, 0., 1.);

                // Animate the point
                // point = 0.5 + vec2(sin(time + circle), cos(time + circle)) * radius;
                point = 0.5 + vec2(sin(time + flowmap.b), cos(time + flowmap.b)) * radius;
                point.x = clamp(point.x, 0., 1.);
                point.y = clamp(point.y, 0., 1.);

                // Vector between the pixel and the point
                vec2 diff = neighbor + point - f_st;

                // Distance to the point
                float dist = length(diff);

                // vec2 c = i_st + neighbor;

                // if(c.x == 5. && c.y == 5.) {
                //     dist = pow(dist, 2.);
                // }
                // if(c.x == 6. && c.y == 6.) {
                //     dist = pow(dist, 10.);
                // }

                // Keep the closer distance
                // dist = pow(dist, 5.);
                m_dist = min(m_dist, dist);
                


                // if( dist < m_dist ) {
                //     m_dist = dist;
                //     m_point = point;
                // }


                // if(r.x > 0.5) {
                //     m_dist =1.;
                // }

                
                // if(vUv.y >= 1. - (uProgress + sin(vUv.x * 10.) * 0.1)) {
                //     m_dist = 1.;
                // }




                
            }
        }



            
        


        // Draw the min distance (distance field)
        value = m_dist;
        // value = pow(value, 5.);
        // if(value>0.1) {
        //   value = 1.;
        // }
        // value = smoothstep(0.4, 3., value);
        // value += m_dist2;
        
        value = clamp(value, 0., 0.99); // avoid white burn



        

        // if(i_st.y / uCellSize >= 1. - (uProgress + sin(vUv.x * 10.) * 0.1)) {
        //     value = 1.;
        // }


        
        // value *= 1.4;
        // value = smoothstep(0.0, 1., value);
        // value = 1.-value;
        // value = smoothstep(0.1, 1., value);

        // vec3 color = vec3(value);






        // vec3 background = texture2D(uGradient, vec2(0., 0.)).rgb;

        

        vec3 base = blendColorDodge(uBackground, vec3(value), 1.);
        // diffuseColor.rgb = blendAdd(base * 0.25, base, circle + (flowmap.b * 0.1));
        // diffuseColor.rgb = blendAdd(base * 0.25, base, circle);
        // diffuseColor.rgb = blendAdd(base * 0.25, base, flowmap.b);

        // // value = pow(value, 10.);
        // value = smoothstep(0.6, 0.6, value);
        diffuseColor.rgb = vec3(value);



        // vec3 border = mix( vec3(1.0,0.0,0.0), vec3(0.0,0.0,0.0), smoothstep( 0.04, 0.07, value ) );

        // diffuseColor.rgb = border;

        // diffuseColor.rgb = texture1.rgb;


        
        // diffuseColor.rgb = mix(texture1.rgb, texture2.rgb, value);

        // diffuseColor.rgb = vec3(i_st / uCellSize, 0.);




        // float halo1 = 1.-smoothstep(0., 1., length((vUv - vec2(0,0)) * aspect));
        // float halo2 = 1.-smoothstep(0., 1., length((vUv - vec2(1,1)) * aspect));


        // diffuseColor.rgb = blendAdd(diffuseColor.rgb, halo1 * uHaloColor,0.5);
        // diffuseColor.rgb = blendAdd(diffuseColor.rgb, halo2 * uHaloColor,0.5);

        // diffuseColor.rgb = vec3(i_st / uCellSize,0.);

        // diffuseColor.rgb = vec3(circle);

        // diffuseColor.rgb = flowmap.rgb;
        // diffuseColor.r = clamp(diffuseColor.r, 0., 1.);
        // diffuseColor.g = clamp(diffuseColor.g, 0., 1.);
        // diffuseColor.b = clamp(diffuseColor.b, 0., 1.);


        // diffuseColor = texture1;

        // if(vUv.y > uProgress) {
        //     diffuseColor = texture2;
        // }

        // value = pow(value, 4.);

        // float textureMask = vUv.y + 1. - (uProgress * 2.);
        // textureMask = smoothstep(0., 1., textureMask);
        // textureMask = 1. - textureMask;
        
        // float voronoiMask = abs(vUv.y - ((uProgress * 3.) - 1.));
        // voronoiMask = smoothstep(0., 1., voronoiMask);
        // voronoiMask = 1. - voronoiMask;


        // float mask = (voronoiMask * value) + textureMask;
        // mask = clamp(mask, 0., 1.);


        // diffuseColor.rgb = mix(texture1.rgb, texture2.rgb, mask);
        // diffuseColor.rgb = blendAdd(diffuseColor.rgb, base, voronoiMask * 10.);

        // diffuseColor.rgb = vec3(mask);


        if (uDebug) {
            // Draw cell center
            diffuseColor.g += 1.-step(.02, m_dist);
  
            // Draw grid
            diffuseColor.r += step(.98, f_st.x) + step(.98, f_st.y);
          }

        //   st.y = min(st.y, uProgress);


        //   vec3 c = voronoi( st );
        //   vec3 borders = mix( vec3(1.0,0.6,0.0), vec3(0.0,0.0,0.0), smoothstep( 0.04, 0.07, c.x ) );

        //   c.r = c.r *= 10.;

        //    if(c.r>1.-uProgress) {
        //     c.r = 1.;
        //    }
          
        //   c.r = clamp(c.r, 0., 1.);
        //   c.g = clamp(c.g, 0., 1.);
        //   c.b = clamp(c.b, 0., 1.);


        //   vec4 texture1 = texture2D(uTexture1, vUv);
        //   vec4 texture2 = texture2D(uTexture2, vUv);

        //   diffuseColor.rgb = vec3(c.r);

        //   diffuseColor.rgb = mix(texture1.rgb, texture2.rgb, c.r);

        //   diffuseColor.rgb = c;
        //   diffuseColor.rgb = blendNormal(diffuseColor.rgb, vec3(1.0,0.6,0.0),   1. - smoothstep( 0.01, 0.02, c.x ));

        //   diffuseColor.rgb = vec3(c.r);
      `
    )

    // console.log(shader.fragmentShader)
  }

  setResolution(x, y) {
    this.uniforms.uResolution.value.set(x, y)
  }

  raf(time) {
    this.uniforms.uTime.value = time * this.speed
  }

  setCursor(x, y) {
    this.uniforms.uCursor.value.set(x, y)
  }

  get cursor() {
    return this.uniforms.uCursor.value
  }

  set debug(value) {
    this.uniforms.uDebug.value = value
  }

  get debug() {
    return this.uniforms.uDebug.value
  }

  set cellSize(value) {
    this.uniforms.uCellSize.value = value
  }

  get cellSize() {
    return this.uniforms.uCellSize.value
  }

  set gradient(value) {
    this.uniforms.uGradient.value = value
  }

  get gradient() {
    return this.uniforms.uGradient.value
  }

  set background(value) {
    this.uniforms.uBackground.value.set(value.toString())
  }

  get background() {
    return this.uniforms.uBackground.value
  }

  set excitement(value) {
    this.uniforms.uExcitement.value = value
  }

  get excitement() {
    return this.uniforms.uExcitement.value
  }

  set flowmap(flowmap) {
    // this.uniforms.uFlowmap.value = value
    this.uniforms.uFlowmap = flowmap?.uniform || { value: null }
  }

  get flowmap() {
    return this.uniforms.uFlowmap.value
  }

  set haloColor(value) {
    this.uniforms.uHaloColor.value.set(value.toString())
  }

  get haloColor() {
    return this.uniforms.uHaloColor.value
  }

  set deformationFrequency(value) {
    this.uniforms.uDeformationFrequency.value = value
  }

  get deformationFrequency() {
    return this.uniforms.uDeformationFrequency.value
  }

  set deformationAmplitude(value) {
    this.uniforms.uDeformationAmplitude.value = value
  }

  get deformationAmplitude() {
    return this.uniforms.uDeformationAmplitude.value
  }

  set texture1(value) {
    this.uniforms.uTexture1.value = value
  }

  set texture2(value) {
    this.uniforms.uTexture2.value = value
  }

  set progress(value) {
    this.uniforms.uProgress.value = value
  }
}
