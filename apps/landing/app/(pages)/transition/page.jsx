import { Wrapper } from '~/app/(pages)/(components)/wrapper'
import { TheatreProjectProvider } from '~/libs/theatre'
import { Experience } from './(components)/experience'
import s from './transition.module.css'

export default function Home() {
  return (
    <TheatreProjectProvider id="Ibicash" config="/config/Ibicash.json">
      <Wrapper
        theme="dark"
        className={s.page}
        webgl={{
          postprocessing: true,
          orbitControls: false,
        }}
      >
        <Experience />
      </Wrapper>
    </TheatreProjectProvider>
  )
}
