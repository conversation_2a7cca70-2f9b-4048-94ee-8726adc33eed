'use client'

import { useEffect } from 'react'
import { Wrapper } from '~/app/(pages)/(components)/wrapper'
import { useStore } from '~/libs/store'
import { TheatreProjectProvider } from '~/libs/theatre'
import * as ScrollSpy from '../(components)/scroll-spy'
import { Chapter1 } from '../home/<USER>/chapter1'
import { Intro } from '../home/<USER>/intro'
import { THEATRE_CONFIG } from '../home/<USER>'

export default function Page() {
  const setIntroCompleted = useStore((state) => state.setIntroCompleted)

  useEffect(() => {
    setIntroCompleted(true)
  }, [setIntroCompleted])

  return (
    <TheatreProjectProvider id="Ibicash" config={THEATRE_CONFIG}>
      <ScrollSpy.Root>
        <Wrapper
          theme="dark"
          webgl={{
            postprocessing: true,
            orbitControls: true,
          }}
          lenis
          loader={false}
        >
          <div
            style={{
              height: '100vh',
              background: 'black',
              zIndex: 1,
              opacity: 0.75,
            }}
          />
          <Intro>
            <Chapter1 />
          </Intro>
          <div
            style={{
              height: '100vh',
              background: 'black',
              zIndex: 1,
              opacity: 0.75,
            }}
          />
        </Wrapper>
      </ScrollSpy.Root>
    </TheatreProjectProvider>
  )
}
