import { OrbitControls, useTexture } from '@react-three/drei'
import { LinearMipmapLinearFilter, SRGBColorSpace } from 'three'
import { useProgressiveKTX2 } from '~/hooks/use-progressive-ktx2'

const url = '/textures/sceneA/Landscape_COMBINED001'

export function WebGL() {
  const ktx = useProgressiveKTX2(`${url}.ktx2`)

  const png = useTexture(`${url}.png`)

  ktx.colorSpace = SRGBColorSpace
  png.colorSpace = SRGBColorSpace

  ktx.generateMipmaps = true
  png.generateMipmaps = true

  ktx.minFilter = LinearMipmapLinearFilter
  ktx.magFilter = LinearMipmapLinearFilter
  ktx.flipY = false
  png.flipY = false

  ktx.needsUpdate = true
  png.needsUpdate = true

  return (
    <>
      <OrbitControls />
      <group scale={200}>
        <mesh position={[-0.6, 0, 0]}>
          <planeGeometry />
          <meshBasicMaterial map={png} />
        </mesh>
        <mesh position={[0.6, 0, 0]}>
          <planeGeometry />
          <meshBasicMaterial map={ktx} />
        </mesh>
      </group>
    </>
  )
}
