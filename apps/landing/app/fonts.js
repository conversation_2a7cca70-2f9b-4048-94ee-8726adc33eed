// https://nextjs.org/docs/pages/building-your-application/optimizing/fonts#local-fonts

import cn from 'clsx'
import localFont from 'next/font/local'

const denim = localFont({
  src: [
    {
      path: '../public/fonts/Denim/DenimINK-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
  ],
  display: 'swap',
  variable: '--font-denim',
  preload: true,
})

const fts = localFont({
  src: '../public/fonts/fts/FTSystemMono-Regular.woff2',
  display: 'swap',
  variable: '--font-fts',
  preload: true,
})

export const fonts = { className: cn(denim.variable, fts.variable) }
