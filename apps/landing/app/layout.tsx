// import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google'
import { Analytics } from '@vercel/analytics/react'
import { ReactTempus } from 'tempus/react'
import { Debug } from '~/components/debug'
import { GSAP } from '~/components/gsap'
import { RealViewport } from '~/components/real-viewport'
import { DeviceDetectionContextProvider } from '~/hooks/use-device-detection'
import { StyleVariables } from '~/libs/style-variables'
import { colors, themes } from '~/styles/config.mjs'
import '~/styles/global.css'
import { fonts } from './fonts'

const APP_NAME = 'Ibicash – The Forest-Powered Economy'
const APP_DEFAULT_TITLE = 'Ibicash – The Forest-Powered Economy'
const APP_TITLE_TEMPLATE = '%s - Ibicash – The Forest-Powered Economy'
const APP_DESCRIPTION =
  'As forests flourish, new tokens emerge—connecting ecological health to digital value in a resilient monetary system.'
const APP_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL
// const GTM_ID = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID || false
// const GA_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS || false

export const metadata = {
  metadataBase: new URL(APP_BASE_URL || 'http://localhost:3000'),
  applicationName: APP_NAME,
  title: {
    default: APP_DEFAULT_TITLE,
    template: APP_TITLE_TEMPLATE,
  },
  icons: {
    icon: [
      {
        rel: 'icon',
        type: 'image/png',
        url: '/images/favicon-light-mode.png',
        media: '(prefers-color-scheme: light)',
      },
      {
        rel: 'icon',
        type: 'image/png',
        url: '/images/favicon-dark-mode.png',
        media: '(prefers-color-scheme: dark)',
      },
    ],
  },
  description: APP_DESCRIPTION,
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: APP_DEFAULT_TITLE,
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: 'website',
    siteName: APP_NAME,
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
    url: APP_BASE_URL,
  },
  twitter: {
    card: 'summary_large_image',
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
  },
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en-US',
    },
  },
}

export const viewport = {
  themeColor: '#000',
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang="en"
      dir="ltr"
      className={fonts?.className}
      suppressHydrationWarning
    >
      <head>
        <StyleVariables colors={colors} themes={themes} />
      </head>
      {/* {GTM_ID && <GoogleTagManager gtmId={GTM_ID} />} */}
      <body>
        <RealViewport />
        <DeviceDetectionContextProvider>
          {children}
        </DeviceDetectionContextProvider>
        <Debug />
        <GSAP />
        {/* @ts-expect-error Server Component */}
        <ReactTempus patch />
        <Analytics />
      </body>
      {/* {GA_ID && <GoogleAnalytics gaId={GA_ID} />} */}
    </html>
  )
}
