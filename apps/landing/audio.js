const fs = require('node:fs')
const path = require('node:path')
const { exec } = require('node:child_process')

// Function to process each PNG file
function processAudioFiles(directory) {
  fs.readdir(directory, (err, files) => {
    if (err) {
      return console.error('Unable to scan directory: ' + err)
    }

    // Filter out PNG files
    const audioFiles = files.filter(
      (file) => path.extname(file).toLowerCase() === '.wav'
    )

    const command = `audiosprite ${audioFiles.map((file) => path.join(directory, file)).join(' ')} --format howler2 --output public/sounds/sprites -c 2`

    // pngFiles.forEach((file) => {
    // const filePath = path.join(directory, file)
    // const outputFilePath = filePath.replace('.png', '.ktx2')

    // // Construct the command
    // const command = `toktx --bcmp --qlevel 256 --assign_oetf srgb --genmipmap ${outputFilePath} ${filePath}`

    // // Execute the command
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error processing file: ${error.message}`)
        return
      }
      if (stderr) {
        console.error(`Error output for file: ${stderr}`)
        return
      }
      console.log(`Successfully processed: ${stdout}`)
    })
    // })
  })
}

// Run the function
processAudioFiles('./public/sounds')
