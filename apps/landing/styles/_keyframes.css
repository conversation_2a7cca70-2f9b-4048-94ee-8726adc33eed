@keyframes rotate-centered {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.rotate-centered-infinite {
  animation-name: rotate-centered;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-infinite {
  animation-name: rotate;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes progress-in {
  0% {
    transform: scaleX(0.5);
  }
  100% {
    transform: scaleX(1);
  }
}

.animation-progress-in {
  animation-name: progress-in;
  animation-fill-mode: forwards;
  animation-timing-function: var(--gleasing);
}

@keyframes colorCascade {
  0%,
  100% {
    background-color: var(--darkGrey);
  }
  50% {
    background-color: var(--green);
  }
}

.animation-colorCascade {
  animation-name: colorCascade;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
}

@keyframes badge1 {
  0%,
  10% {
    opacity: 1;
  }
  11%,
  100% {
    opacity: 0;
  }
}

@keyframes badge2 {
  0%,
  9% {
    opacity: 0;
  }
  10%,
  20% {
    opacity: 1;
  }
  21%,
  100% {
    opacity: 0;
  }
}

@keyframes badge3 {
  0%,
  19% {
    opacity: 0;
  }
  20%,
  30% {
    opacity: 1;
  }
  31%,
  100% {
    opacity: 0;
  }
}

@keyframes badge4 {
  0%,
  29% {
    opacity: 0;
  }
  30%,
  40% {
    opacity: 1;
  }
  41%,
  100% {
    opacity: 0;
  }
}

@keyframes badge5 {
  0%,
  39% {
    opacity: 0;
  }
  40%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

@keyframes badge6 {
  0%,
  49% {
    opacity: 0;
  }
  50%,
  60% {
    opacity: 1;
  }
  61%,
  100% {
    opacity: 0;
  }
}

@keyframes badge7 {
  0%,
  59% {
    opacity: 0;
  }
  60%,
  70% {
    opacity: 1;
  }
  71%,
  100% {
    opacity: 0;
  }
}

@keyframes badge8 {
  0%,
  69% {
    opacity: 0;
  }
  70%,
  80% {
    opacity: 1;
  }
  81%,
  100% {
    opacity: 0;
  }
}

@keyframes badge9 {
  0%,
  79% {
    opacity: 0;
  }
  80%,
  90% {
    opacity: 1;
  }
  91%,
  100% {
    opacity: 0;
  }
}

@keyframes badge10 {
  0%,
  89% {
    opacity: 0;
  }
  90%,
  100% {
    opacity: 1;
  }
}

.badge1,
.badge2,
.badge3,
.badge4,
.badge5,
.badge6,
.badge7,
.badge8,
.badge9,
.badge10 {
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
}

.badge1 {
  animation-name: badge1;
}

.badge2 {
  animation-name: badge2;
}

.badge3 {
  animation-name: badge3;
}

.badge4 {
  animation-name: badge4;
}

.badge5 {
  animation-name: badge5;
}

.badge6 {
  animation-name: badge6;
}

.badge7 {
  animation-name: badge7;
}

.badge8 {
  animation-name: badge8;
}

.badge9 {
  animation-name: badge9;
}

.badge10 {
  animation-name: badge10;
}

@keyframes glitch-ring {
  0% {
    --size: 10%;
  }
  12.5% {
    --size: 30%;
  }
  25% {
    --size: 50%;
  }
  37.5% {
    --size: 70%;
  }
  50% {
    --size: 50%;
  }
  62.5% {
    --size: 30%;
  }
  100% {
    --size: 10%;
  }
}

.animation-glitch-ring {
  &::before {
    --gray-alpha: rgba(143, 143, 143, 0.25);
    --green-alpha: rgba(172, 255, 70, 0.75);
    --border-width: 10%;
    --size: 10%;
    background-image: linear-gradient(
        to right,
        var(--gray-alpha) 0%,
        var(--gray-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size) + var(--border-width)),
        var(--gray-alpha) calc(50% - var(--size) + var(--border-width)),
        var(--gray-alpha) calc(50% + var(--size) - var(--border-width)),
        var(--green-alpha) calc(50% + var(--size) - var(--border-width)),
        var(--green-alpha) calc(50% + var(--size)),
        var(--gray-alpha) calc(50% + var(--size)),
        var(--gray-alpha) 100%
      ),
      linear-gradient(
        to bottom,
        var(--gray-alpha) 0%,
        var(--gray-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size) + var(--border-width)),
        var(--gray-alpha) calc(50% - var(--size) + var(--border-width)),
        var(--gray-alpha) calc(50% + var(--size) - var(--border-width)),
        var(--green-alpha) calc(50% + var(--size) - var(--border-width)),
        var(--green-alpha) calc(50% + var(--size)),
        var(--gray-alpha) calc(50% + var(--size)),
        var(--gray-alpha) 100%
      );
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    animation: glitch-ring 15s infinite linear;
  }
}

@keyframes glitch-sideway {
  0% {
    background-position: -0% -100%;
  }
  100% {
    background-position: 0% 100%;
  }
}

.animation-glitch-sideway {
  &::before {
    animation: glitch-sideway 10s infinite linear;
    --middle: 46%;
    --gray-alpha: rgba(143, 143, 143, 0.25);
    --green-alpha: rgba(172, 255, 70, 0.75);
    background: linear-gradient(
      45deg,
      var(--gray-alpha) 0%,
      var(--gray-alpha) var(--middle),
      var(--green-alpha) var(--middle),
      var(--green-alpha) calc(100% - var(--middle)),
      var(--gray-alpha) calc(100% - var(--middle)),
      var(--gray-alpha) 100%
    );
    background-size: 100% 200%;
  }
}

@keyframes glitch-horizontal {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 110% 0%;
  }
}

.animation-glitch-vertical {
  &::before {
    animation: glitch-horizontal 10s infinite linear;
    --middle: 45%;
    --gray-alpha: rgba(143, 143, 143, 0.25);
    background: linear-gradient(
      90deg,
      var(--gray-alpha) 0%,
      var(--gray-alpha) var(--middle),
      var(--green) var(--middle),
      var(--green) calc(100% - var(--middle)),
      var(--gray-alpha) calc(100% - var(--middle)),
      var(--gray-alpha) 100%
    );
    background-size: 200% 100%;
  }
}

@keyframes glitch-radar {
  0% {
    --sweep: -25deg;
  }
  33% {
    --sweep: 65deg;
  }
  66% {
    --sweep: 155deg;
  }
  100% {
    --sweep: 245deg;
  }
}

.animation-glitch-radar {
  &::before {
    --sweep: -25deg;
    --width: 50deg;
    --gray-alpha: rgba(143, 143, 143, 0.25);
    --green-alpha: rgba(172, 255, 70, 0.75);
    background: conic-gradient(
      from var(--sweep) at center,
      var(--green-alpha) 0deg,
      var(--green-alpha) var(--width),
      var(--gray-alpha) var(--width)
    );
    animation: glitch-radar 10s infinite linear;
  }
}

@keyframes glitch-borders {
  0% {
    --size: 0%;
  }
  10% {
    --size: 10%;
  }
  20% {
    --size: 15%;
  }
  30% {
    --size: 30%;
  }
  40% {
    --size: 40%;
  }
  50% {
    --size: 50%;
  }
  60% {
    --size: 40%;
  }
  70% {
    --size: 30%;
  }
  80% {
    --size: 15%;
  }
  90% {
    --size: 10%;
  }
  100% {
    --size: 0%;
  }
}

.animation-glitch-borders {
  &::before {
    --gray-alpha: rgba(143, 143, 143, 0.25);
    --green-alpha: rgba(172, 255, 70, 0.75);
    --border-width: 10%;
    --size: 0%;
    background: var(--gray-alpha);
    /* Top border */
    background-image: linear-gradient(
        to right,
        transparent calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% + var(--size)),
        transparent calc(50% + var(--size))
      ), /* Bottom border */
      linear-gradient(
        to right,
        transparent calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% + var(--size)),
        transparent calc(50% + var(--size))
      ), /* Left border */
      linear-gradient(
        to bottom,
        transparent calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% + var(--size)),
        transparent calc(50% + var(--size))
      ), /* Right border */
      linear-gradient(
        to bottom,
        transparent calc(50% - var(--size)),
        var(--green-alpha) calc(50% - var(--size)),
        var(--green-alpha) calc(50% + var(--size)),
        transparent calc(50% + var(--size))
      );
    background-size: 100% var(--border-width), 100% var(--border-width),
      var(--border-width) 100%, var(--border-width) 100%;
    background-position: center top, center bottom, left center, right center;
    background-repeat: no-repeat;
    animation: glitch-borders 20s infinite linear;
  }
}
