/* TODO: Add font styles */
.h1-base {
  font-family: var(--font-denim);
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  letter-spacing: -0.06em;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .h1-mobile {
    @extend .h1-base;
    line-height: 90%;
    --size-vw: mobile-vw(64px);
    --size-vh: mobile-vh(64px);
  }
}

@include-media ('desktop') {
  .h1-desktop {
    @extend .h1-base;
    --size-vw: desktop-vw(128px);
    --size-vh: desktop-vh(128px);
  }
}

.h1 {
  @include-media ('mobile') {
    @extend .h1-mobile;
  }

  @include-media ('desktop') {
    @extend .h1-desktop;
  }
}

.h2-base {
  font-family: var(--font-denim);
  font-style: normal;
  font-weight: 400;
  line-height: 90%;
  letter-spacing: -0.06em;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .h2-mobile {
    @extend .h2-base;
    --size-vw: mobile-vw(48px);
    --size-vh: mobile-vh(48px);
  }
}

@include-media ('desktop') {
  .h2-desktop {
    @extend .h2-base;
    --size-vw: desktop-vw(92px);
    --size-vh: desktop-vh(92px);
  }
}

.h2 {
  @include-media ('mobile') {
    @extend .h2-mobile;
  }

  @include-media ('desktop') {
    @extend .h2-desktop;
  }
}

.h3-base {
  font-family: var(--font-denim);
  font-style: normal;
  font-weight: 400;
  line-height: 90%;
  letter-spacing: -0.04em;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .h3-mobile {
    @extend .h3-base;
    --size-vw: mobile-vw(34px);
    --size-vh: mobile-vh(34px);
  }
}

@include-media ('desktop') {
  .h3-desktop {
    @extend .h3-base;
    --size-vw: desktop-vw(34px);
    --size-vh: desktop-vh(34px);
  }
}

.h3 {
  @include-media ('mobile') {
    @extend .h3-mobile;
  }

  @include-media ('desktop') {
    @extend .h3-desktop;
  }
}

.h4-base {
  font-family: var(--font-fts);
  font-style: normal;
  font-weight: 400;
  line-height: 130%;
  letter-spacing: 0;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .h4-mobile {
    @extend .h4-base;
    --size-vw: mobile-vw(18px);
    --size-vh: mobile-vh(18px);
  }
}

@include-media ('desktop') {
  .h4-desktop {
    @extend .h4-base;
    --size-vw: desktop-vw(18px);
    --size-vh: desktop-vh(18px);
  }
}

.h4 {
  @include-media ('mobile') {
    @extend .h4-mobile;
  }

  @include-media ('desktop') {
    @extend .h4-desktop;
  }
}

.p-base {
  font-family: var(--font-fts);
  font-style: normal;
  line-height: 130%;
  letter-spacing: 0;
  font-weight: 400;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .p-mobile {
    @extend .p-base;
    --size-vw: mobile-vw(15px);
    --size-vh: mobile-vh(15px);
  }
}

@include-media ('desktop') {
  .p-desktop {
    @extend .p-base;
    --size-vw: desktop-vw(15px);
    --size-vh: desktop-vh(15px);
  }
}

.p {
  @include-media ('mobile') {
    @extend .p-mobile;
  }

  @include-media ('desktop') {
    @extend .p-desktop;
  }
}

.ps-base {
  font-family: var(--font-fts);
  font-style: normal;
  line-height: 120%;
  letter-spacing: 0;
  font-weight: 400;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .ps-mobile {
    @extend .ps-base;
    --size-vw: mobile-vw(13px);
    --size-vh: mobile-vh(13px);
  }
}

@include-media ('desktop') {
  .ps-desktop {
    @extend .ps-base;
    --size-vw: desktop-vw(13px);
    --size-vh: desktop-vh(13px);
  }
}

.ps {
  @include-media ('mobile') {
    @extend .ps-mobile;
  }

  @include-media ('desktop') {
    @extend .ps-desktop;
  }
}

.ps-text-base {
  font-family: var(--font-fts);
  font-style: normal;
  line-height: 135%;
  letter-spacing: 0;
  font-weight: 400;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .ps-text-mobile {
    @extend .ps-text-base;
    --size-vw: mobile-vw(12px);
    --size-vh: mobile-vh(12px);
  }
}

@include-media ('desktop') {
  .ps-text-desktop {
    @extend .ps-text-base;
    --size-vw: desktop-vw(12px);
    --size-vh: desktop-vh(12px);
  }
}

.ps-text {
  @include-media ('mobile') {
    @extend .ps-text-mobile;
  }

  @include-media ('desktop') {
    @extend .ps-text-desktop;
  }
}

.pxs-base {
  font-family: var(--font-fts);
  font-style: normal;
  line-height: 120%;
  letter-spacing: 0;
  font-weight: 400;
  font-size: var(--size-vw);

  &.vh {
    font-size: var(--size-vh);
  }
}

@include-media ('mobile') {
  .pxs-mobile {
    @extend .pxs-base;
    --size-vw: mobile-vw(10px);
    --size-vh: mobile-vh(10px);
  }
}

@include-media ('desktop') {
  .pxs-desktop {
    @extend .pxs-base;
    --size-vw: desktop-vw(10px);
    --size-vh: desktop-vh(10px);
  }
}

.pxs {
  @include-media ('mobile') {
    @extend .pxs-mobile;
  }

  @include-media ('desktop') {
    @extend .pxs-desktop;
  }
}

.uppercase {
  text-transform: uppercase;
}

.center {
  text-align: center;
}

.grey {
  color: var(--grey);
}

.green {
  color: var(--green);
}

.black {
  color: var(--black);
}

.white {
  color: var(--white);
}
