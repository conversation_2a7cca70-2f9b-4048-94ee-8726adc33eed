@import "./_reset";
@import "./_easings";
@import "./_utils";
@import "./_font-style";
@import "./_layout";
@import "./_keyframes";

:root {
  --header-height: mobile-vw(58px);

  @include-media ('desktop') {
    --header-height: desktop-vw(98px);
  }
}

html {
  --scrollbar-gutter: 0px;

  &.lenis-stopped {
    --scrollbar-gutter: var(--scrollbar-width);
  }
}

html:not(.dev) {
  &,
  * {
    scrollbar-width: thin;

    @include-media ('desktop') {
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}

html.cinematic {
  *:not(:has(#webgl, #orchestra), #webgl, #webgl *, #orchestra, #orchestra *) {
    visibility: hidden;
  }
}

/* Core styles that shouldn't be reset */
body {
  min-height: 100vh;
  overscroll-behavior: none;
  background-color: var(--theme-primary);
  color: var(--theme-secondary);
  display: flex;
  flex-direction: column;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* TODO: Enable cursor */
  /* cursor: none; */
}

/* Selection styling */
*::selection {
  background-color: var(--theme-contrast);
  color: var(--theme-primary);
}

/* SVG icon colors */
svg.icon {
  path[fill],
  rect[fill],
  circle[fill] {
    fill: currentColor;
  }
  path[stroke],
  rect[stroke],
  circle[stroke] {
    stroke: currentColor;
  }
}

/* Hover states */
.link {
  @include-media ('hover') {
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Focus states */
*:focus-visible {
  outline: 2px solid var(--theme-contrast);
}

dialog {
  border: none;
  padding: 0;
}

/* Shared scenes styles */

/* will be merged into chapter */
/* .section {
  @include-media ('mobile') {
    height: 100% !important;
  }
} */

.chapter {
  position: relative;
  height: 100%;

  @include-media ('desktop') {
    height: 100%;
  }
}

.wrapper {
  position: relative;

  /* @include-media ('desktop') { */
  display: grid;
  grid-template-columns: 1fr;
  height: calc(100% + 100vh);
  /* } */
}

.scene {
  /* position: relative; */

  @include-media ('mobile') {
    padding-inline: var(--layout-margin);
  }

  /* @include-media ('desktop') { */
  height: 100svh;
  grid-column-start: 1;
  grid-row-start: 1;
  position: sticky;
  top: 0;
  /* } */
}

/* .mobile-bkg-pattern {
  @include-media ('mobile') {
    display: flex;
    flex-direction: column;

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      width: calc(100% - 2 * mobile-vw(23px));
      height: 100%;
      margin-inline: mobile-vw(23px);
      mask-image: url("/images/mobile-bkg-pattern.svg");
      mask-size: fill;
      mask-position: center;
      mask-repeat: repeat;
    }
  }
} */

.top-bottom-gradients {
  @include-media ('mobile') {
    position: relative;

    &::before,
    &::after {
      content: "";
      position: absolute;
      left: 0;
      width: 100%;
      height: mobile-vw(100px);
      z-index: 1;
    }

    &::before {
      top: 0;
      background: linear-gradient(
        0deg,
        rgba(255, 255, 252, 0) 0%,
        rgba(0, 0, 0, 1) 100%
      );
    }

    &::after {
      bottom: 0;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 252, 0) 0%,
        rgba(0, 0, 0, 1) 100%
      );
    }
  }
}

/* .mobile-bkg-media {
  position: relative;
  width: 100%;
  height: mobile-vw(560px);

  @extend .top-bottom-gradients;

  @include-media ('desktop') {
    display: none;
  }
} */
