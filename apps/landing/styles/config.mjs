// Constant declarations
const colors = {
  black: '#000000',
  white: '#ffffff',
  grey: '#8F8F8F',
  darkGrey: '#4A4A4A',
  red: '#FF6363',
  green: '#ACFF46',
  purple: '#768FFF',
}

const themes = {
  light: {
    primary: colors.white,
    secondary: colors.black,
    contrast: colors.green,
  },
  dark: {
    primary: colors.black,
    secondary: colors.white,
    contrast: colors.green,
  },
}

const breakpoints = {
  dt: 800,
}

const screens = {
  mobile: { width: 390, height: 650 },
  desktop: { width: 1440, height: 816 },
}

/** @type {(keyof typeof themes)[]} */
const themeNames = Object.keys(themes)

const config = {
  themes,
  columns: {
    mobile: 8,
    desktop: 12,
  },
  gaps: {
    mobile: 16,
    desktop: 24,
  },
  margins: {
    mobile: 16,
    desktop: 24,
  },
}

export { breakpoints, colors, config, screens, themeNames, themes }
