const fs = require('node:fs')
const path = require('node:path')
const { exec } = require('node:child_process')
const sharp = require('sharp')

const SIZE = 512

async function resizePng(inputPath, outputPath, maxSize = 2048) {
  try {
    const image = sharp(inputPath)

    const metadata = await image.metadata()

    const { width, height } = metadata

    // Check if resizing is needed
    // if (width <= maxSize && height <= maxSize) {
    //   console.log('Image is within limits. No resizing needed.')
    //   // await image.toFile(outputPath)
    //   return inputPath
    // }

    // Calculate scale factor to ensure neither width nor height exceeds 2048
    const scaleFactor = Math.min(maxSize / width, maxSize / height)

    const newWidth = Math.floor(width * scaleFactor)
    const newHeight = Math.floor(height * scaleFactor)

    await image.resize(newWidth, newHeight).toFile(outputPath)

    console.log(
      `Image resized to ${newWidth}x${newHeight} and saved to ${outputPath}`
    )

    return outputPath
  } catch (error) {
    console.error('Error resizing image:', error)
    return inputPath
  }
}

// Function to process each PNG file
function processPngFiles(directory) {
  fs.readdir(directory, async (err, files) => {
    if (err) {
      return console.error(`Unable to scan directory: ${err}`)
    }

    const pngFiles = files.filter(
      (file) =>
        path.extname(file).toLowerCase() === '.png' && !file.includes(`_SD.png`)
    )

    for (const file of pngFiles) {
      const filePath = path.join(directory, file)
      const resizedFilePath = await resizePng(
        filePath,
        filePath.replace('.png', `_SD.png`),
        SIZE
      )

      // const outputFilePath = filePath.replace('.png', '.ktx2')

      const commands = [
        `toktx --bcmp --qlevel 256 --assign_oetf srgb --genmipmap ${resizedFilePath.replace('.png', '.ktx2')} ${resizedFilePath}`,
        `toktx --bcmp --qlevel 256 --assign_oetf srgb --genmipmap ${filePath.replace('.png', '.ktx2')} ${filePath}`,
      ]

      // Construct the command
      // const command = `toktx --bcmp --qlevel 256 --assign_oetf srgb --genmipmap ${outputFilePath} ${resizedFilePath}`
      // const command = `toktx --encode uastc --uastc_quality 4 --zcmp --assign_oetf srgb --genmipmap ${outputFilePath} ${filePath}`

      // const commands = [
      //   `toktx --encode uastc --uastc_quality 4 --zcmp --assign_oetf srgb --genmipmap ${resizedFilePath.replace('.png', '.ktx2')} ${resizedFilePath}`,
      //   `toktx --encode uastc --uastc_quality 4 --zcmp --assign_oetf srgb --genmipmap ${filePath.replace('.png', '.ktx2')} ${filePath}`,
      // ]

      for (const command of commands) {
        // Execute the command
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error(`Error processing file ${file}: ${error.message}`)
            return
          }
          if (stderr) {
            console.error(`Error output for file ${file}: ${stderr}`)
            return
          }
          console.log(`Successfully processed ${file}: ${stdout}`)
        })
      }
    }
  })
}

// Run the function
// processPngFiles('./public/textures/noise')
processPngFiles('./public/textures/sceneA')
processPngFiles('./public/textures/sceneB')
processPngFiles('./public/textures/sceneC')
processPngFiles('./public/textures/sceneD')
processPngFiles('./public/textures/sceneE')
processPngFiles('./public/textures/sceneF')
