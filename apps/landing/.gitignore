# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.turbo

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/public/sw.js

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

.eslintcache

.env*.local

.npmrc

*storybook.log
certificates

next-env.d.ts
.vercel
