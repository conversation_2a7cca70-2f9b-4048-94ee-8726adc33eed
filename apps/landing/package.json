{"name": "ibicash-landing", "description": "As forests flourish, new tokens emerge.", "version": "1.0.0", "scripts": {"dev": "next dev --turbo", "dev:https": "next dev --turbo --experimental-https", "build": "next build", "start": "next start", "lint": "biome lint", "analyze": "cross-env ANALYZE=true next build", "storybook:dev": "storybook dev -p 6006", "storybook:build": "storybook build --quiet", "typecheck": "tsc --noEmit --incremental false"}, "dependencies": {"@darkroom.engineering/elastica": "^0.0.14", "@hubspot/api-client": "^12.0.1", "@jimbly/howler": "0.1.8", "@next/third-parties": "^15.3.1", "@react-three/drei": "^10.0.6", "@react-three/fiber": "9.1.2", "@sendgrid/mail": "^8.1.5", "@storyblok/js": "^3.2.2", "@theatre/core": "^0.7.2", "@theatre/studio": "^0.7.2", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "gsap": "^3.12.7", "hamo": "1.0.0-dev.6", "howler": "^2.2.4", "lenis": "1.3.4-dev.2", "next": "15.3.1", "postprocessing": "^6.37.2", "react": "^19.1.0", "react-aria": "^3.39.0", "react-aria-components": "^1.8.0", "react-dom": "^19.1.0", "react-use": "^17.6.0", "sharp": "^0.34.1", "stats-gl": "^3.6.0", "storyblok-js-client": "^6.10.11", "storyblok-rich-text-react-renderer": "^2.9.2", "suspend-react": "^0.1.3", "tempus": "1.0.0-dev.10", "three": "^0.175.0", "troika-three-text": "^0.52.4", "troika-three-utils": "^0.52.4", "tunnel-rat": "^0.1.2", "zustand": "5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@builder.io/partytown": "^0.10.3", "@chromatic-com/storybook": "3.2.6", "@next/bundle-analyzer": "^15.3.1", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@svgr/webpack": "^8.1.0", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/three": "^0.175.0", "babel-plugin-react-compiler": "19.1.0-rc.1", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "lefthook": "^1.11.11", "postcss": "^8.5.3", "postcss-combine-duplicated-selectors": "^10.0.3", "postcss-extend-rule": "^4.0.0", "postcss-flexbugs-fixes": "^5.0.2", "postcss-functions": "^4.0.2", "postcss-import": "^16.1.0", "postcss-include-media": "^1.1.1", "postcss-nesting": "^13.0.1", "postcss-preset-env": "^10.1.6", "postcss-sort-media-queries": "^5.2.0", "storybook": "^8.6.12", "typescript": "^5.8.3"}, "overrides": {"scheduler": "0.23.2"}, "babel": {"compact": "true"}}