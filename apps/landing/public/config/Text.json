{"sheetsById": {"webgl": {"staticOverrides": {"byObject": {"text": {"duration": 4, "animation": 0, "characterUVs": false, "wordUVs": false, "renderUVs": false, "progress": 1, "fontSize": 180}, "fluid simulation": {}, "flowmap": {"dissipation": 0.99}, "image": {"test": 0, "progress": 1, "sharpness": 7}}}, "sequence": {"subUnitsPerUnit": 30, "length": 5, "type": "PositionalSequence", "tracksByObject": {"text": {"trackData": {}, "trackIdByPropPath": {}}, "image": {"trackData": {"YUgRA7uOSx": {"type": "BasicKeyframedTrack", "__debugName": "image:[\"test\"]", "keyframes": [{"id": "5DM1xx3-iF", "position": 0, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}, {"id": "N2HSPsbiVD", "position": 3, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}]}}, "trackIdByPropPath": {"[\"test\"]": "YUgRA7uOSx"}}}}}, "Choke": {"staticOverrides": {"byObject": {"image": {"progress": 1, "sharpness": 20}}}, "sequence": {"subUnitsPerUnit": 30, "length": 3, "type": "PositionalSequence", "tracksByObject": {"image": {"trackData": {"U84p2gD3Sq": {"type": "BasicKeyframedTrack", "__debugName": "image:[\"progress\"]", "keyframes": [{"id": "yy0emSeGc7", "position": 0, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}, {"id": "GB4iBhtwEH", "position": 1, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}, {"id": "2LlEBoqYy5", "position": 2, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}, {"id": "mP1zVtb527", "position": 2.967, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}, {"id": "CNBCrvP8vi", "position": 3, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}]}}, "trackIdByPropPath": {"[\"progress\"]": "U84p2gD3Sq"}}}}}, "Text": {"staticOverrides": {"byObject": {"text": {"progress": 0.5}}}, "sequence": {"subUnitsPerUnit": 30, "length": 3, "type": "PositionalSequence", "tracksByObject": {"text": {"trackData": {"qUjZVoQY9x": {"type": "BasicKeyframedTrack", "__debugName": "text:[\"progress\"]", "keyframes": [{"id": "0BvZ8eYrNg", "position": 0, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}, {"id": "8AFE10mBzg", "position": 3, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}]}}, "trackIdByPropPath": {"[\"progress\"]": "qUjZVoQY9x"}}}}}, "Navigation": {"staticOverrides": {"byObject": {"image": {"sharpness": 20}}}, "sequence": {"subUnitsPerUnit": 30, "length": 2, "type": "PositionalSequence", "tracksByObject": {"image": {"trackData": {"AGjYhyZMuk": {"type": "BasicKeyframedTrack", "__debugName": "image:[\"progress\"]", "keyframes": [{"id": "bAG1HYV54q", "position": 0, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}, {"id": "qQJKySEyex", "position": 2, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}]}}, "trackIdByPropPath": {"[\"progress\"]": "AGjYhyZMuk"}}}}}, "Loader": {"staticOverrides": {"byObject": {"image": {"progress": 0}}}, "sequence": {"subUnitsPerUnit": 30, "length": 3.02, "type": "PositionalSequence", "tracksByObject": {"image": {"trackData": {"wu7OKopRiX": {"type": "BasicKeyframedTrack", "__debugName": "image:[\"progress\"]", "keyframes": [{"id": "um82z3NojT", "position": 0, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}, {"id": "wO5MclD9ow", "position": 1, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}, {"id": "MQ95PK9WuJ", "position": 2, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 1}, {"id": "gkqW8-PNTa", "position": 3, "connectedRight": true, "handles": [0.5, 1, 0.5, 0], "type": "bezier", "value": 0}]}}, "trackIdByPropPath": {"[\"progress\"]": "wu7OKopRiX"}}}}}}, "definitionVersion": "0.4.0", "revisionHistory": ["1wDcukYhHW4B3vzr", "kYKXQmTHMaWu7TF5", "NtVLc1YpvVdJ0P3b", "UMx6_unoWORPglB2", "G980M0wLK8pFc-Y0", "fTeGVCAd8lX4n-nd", "DvDgUYpEZgI938d8", "FN5MOK4vbptoZiA4", "HUfpxRzgBnrBi25c", "lXJ2OmN3uqYRY7am", "KaGQjvYNiug3ekll", "VdrD-Y62sBqfFx8i", "I-OXpYz2ws43RtvD", "eIt4Qgn0WuMGIFoa", "PwzRTTw3U5z64-ij"]}