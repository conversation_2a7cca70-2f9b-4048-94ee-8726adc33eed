name: Auto Merge Dependabot GitHub Actions Updates

on:
  pull_request:
    types:
      - opened
      - synchronize

jobs:
  auto-merge:
    if: github.actor == 'dependabot[bot]'
    runs-on: ubuntu-latest
    
    permissions:
      pull-requests: write
      contents: write
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Auto-merge Dependabot PRs
        run: |
          gh pr merge ${{ github.event.pull_request.number }} --squash --admin
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}