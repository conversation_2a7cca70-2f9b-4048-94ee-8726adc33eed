import gsap from 'gsap'
import { useLenis } from 'lenis/react'
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'
import { Howler } from '~/libs/howler'
import { clamp, smoothStep } from '~/libs/maths'

const GlobalAudioContext = createContext()
export function useGlobalAudio() {
  return useContext(GlobalAudioContext)
}

const speedStart = 95
const speedEnd = 130

export function GlobalAudio({ children }) {
  const [isPlaying, setIsPlaying] = useState(false)

  useEffect(() => {
    Howler?.volume(0)
  }, [How<PERSON>])

  const mute = useCallback((callback) => {
    setIsPlaying(false)
    gsap.to(Howler, {
      volume: 0,
      duration: 1,
      ease: 'expo.out',
    })
    callback?.()
  }, [])

  const unmute = useCallback((callback) => {
    setIsPlaying(true)
    gsap.to(<PERSON><PERSON>, {
      volume: 1,
      duration: 1,
      ease: 'expo.out',
    })
    callback?.()
  }, [])

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!isPlaying) return

      if (document.hidden) {
        Howler?.volume(0)
      } else {
        Howler?.volume(1)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isPlaying])

  useLenis(
    ({ velocity }) => {
      if (!isPlaying) return

      const scrollModulation = smoothStep(
        speedEnd,
        speedStart,
        Math.abs(velocity)
      )

      Howler.volume(clamp(0.05, scrollModulation, 1))
    },
    [isPlaying]
  )

  return (
    <GlobalAudioContext.Provider value={{ Howler, isPlaying, mute, unmute }}>
      {children}
    </GlobalAudioContext.Provider>
  )
}
