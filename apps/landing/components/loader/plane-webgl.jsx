import { types } from '@theatre/core'
import { useWindowSize } from 'hamo'
import { useEffect, useRef, useState } from 'react'
import { MeshBasicMaterial } from 'three'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export function PlaneWebGL({ theaterConfig }) {
  const meshRef = useRef()
  const [material] = useState(
    () => new MeshBasicMaterial({ color: 'black', transparent: true })
  )
  const { width, height } = useWindowSize()

  useEffect(() => {
    if (!meshRef.current) return

    meshRef.current?.position?.set(0, 0, 0)
    meshRef.current?.scale?.set(width, height, 1)
    meshRef.current?.updateMatrix()
  }, [width, height])

  const { sheet, object } = theaterConfig
  const introSheet = useSheet(sheet)

  useTheatre(
    introSheet,
    object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        if (!material) return
        material.opacity = progress
      },
    }
  )
  return (
    <mesh ref={meshRef} matrixAutoUpdate={false}>
      <planeGeometry />
      <primitive object={material} />
    </mesh>
  )
}
