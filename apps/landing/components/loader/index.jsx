'use client'

import { useProgress } from '@react-three/drei'
import cn from 'clsx'
import gsap from 'gsap'
import { useWindowSize } from 'hamo'
import dynamic from 'next/dynamic'
import { useEffect, useRef, useState } from 'react'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useStore } from '~/libs/store'
import { desktopVW, mobileVW } from '~/libs/utils'
import { Choke } from '~/libs/webgl/components/choke'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import { useGlobalAudio } from '../global-audio'
import { ScrambleText } from '../scramble/'
import { Badges } from './badges'
import s from './loader.module.css'

const PlaneWebGL = dynamic(
  () => import('./plane-webgl').then(({ PlaneWebGL }) => PlaneWebGL),
  {
    ssr: false,
  }
)

export function Loader() {
  const progressRef = useRef()
  const barRef = useRef()
  const badgesRef = useRef()
  const enableSoundRef = useRef()
  const soundEnabledRef = useRef()
  const timeoutRef = useRef()
  const [timeline, setTimeline] = useState()
  const [pointer, setPointer] = useState(false)
  const [assetsLoaded, setAssetsLoaded] = useState(false)
  const setLoaderLoaded = useStore((state) => state.setLoaderLoaded)
  const { width: windowWidth } = useWindowSize()
  const { progress, total } = useProgress()
  const globalAudio = useGlobalAudio()
  const { isMobile } = useDeviceDetection()

  useEffect(() => {
    const tl = gsap.timeline({ paused: true })

    tl.to(badgesRef.current, {
      opacity: 0,
      duration: 1,
      onUpdate: () => {
        progressRef.current.style.setProperty(
          '--progress-out',
          1 - tl?.progress()
        )
      },
      onComplete: () => {
        setAssetsLoaded(true)
        setLoaderLoaded(true)
        timeoutRef.current = setTimeout(() => {
          setPointer(true)
          badgesRef.current.classList.add(s.remove)
        }, 100)
      },
    })

    setTimeline(tl)

    return () => {
      tl.kill()
    }
  }, [])

  useEffect(() => {
    if (timeline?.isActive()) {
      clearTimeout(timeoutRef.current)
    }
  }, [timeline])

  useEffect(() => {
    progressRef.current.style.setProperty(
      '--progress-in',
      Math.min((0.5 * total) / 63, 0.5)
    )

    if (total >= 60 && !timeline?.isActive()) {
      barRef.current.classList.add('animation-progress-in')
    }
  }, [progress, timeline, total])

  if (!globalAudio?.unmute) return null

  return (
    <button
      type="button"
      className={cn(
        s.container,
        assetsLoaded && s.hide,
        pointer && s['pointer-none']
      )}
      onClick={() => {
        globalAudio.unmute()
        if (globalAudio?.isPlaying || timeline?.isActive()) return

        console.log('unmute')

        enableSoundRef.current?.hide({
          duration: 0.5,
          direction: 'random',
          onComplete: () => {
            soundEnabledRef.current?.show({
              duration: 0.5,
              direction: 'random',
            })
          },
        })
      }}
    >
      <Badges className={s.badges} ref={badgesRef} />
      <div
        className={cn(
          s.text,
          'ps grey uppercase center',
          globalAudio?.isPlaying && s.enabled
        )}
      >
        <ScrambleText className={s.enable} ref={enableSoundRef}>
          {'[ click to enable sound ]'}
        </ScrambleText>

        <ScrambleText
          className={s.sound}
          ref={soundEnabledRef}
          initState="hide"
        >
          {'[ sound enabled ]'}
        </ScrambleText>
      </div>
      <div className={s.progress} ref={progressRef}>
        <div
          ref={barRef}
          className={s.bar}
          onAnimationEnd={({ nativeEvent }) => {
            if (nativeEvent.animationName === 'progress-in') {
              timeline.play()
              if (globalAudio?.isPlaying) {
                soundEnabledRef.current?.hide({
                  duration: 1,
                  direction: 'random',
                })
              } else {
                enableSoundRef.current?.hide({
                  duration: 1,
                  direction: 'random',
                })
              }
            }
          }}
        />
      </div>
      <Choke
        className={s.choke}
        msdf="/images/loader-msdf.png"
        theaterConfig={{
          sheet: 'intro',
          object: 'loader logo',
        }}
        scale={{ x: 1, y: 5.2 }}
        verticalOffset={
          isMobile ? mobileVW(40, windowWidth) : desktopVW(56, windowWidth)
        }
        delay={0.5}
      />

      <WebGLTunnel>
        <PlaneWebGL
          theaterConfig={{
            sheet: 'intro',
            object: 'loader bkg',
          }}
        />
      </WebGLTunnel>
    </button>
  )
}
