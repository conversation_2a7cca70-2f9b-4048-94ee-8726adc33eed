.container {
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100svh;
  background-color: var(--black);
  transition: 300ms opacity var(--gleasing);
  z-index: 100;
  row-gap: mobile-vw(28px);
  /* @include-media ('mobile') {
    display: none;
  } */

  @include-media ('desktop') {
    row-gap: desktop-vw(28px);
  }
}

.badges {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;

  &.remove {
    display: none;
  }

  svg {
    grid-column-start: 1;
    grid-row-start: 1;
    width: 100%;
    height: 100%;
    animation-duration: 1s;
    opacity: 0;
  }

  width: mobile-vw(48px);
  aspect-ratio: 1;

  @include-media ('desktop') {
    width: desktop-vw(48px);
  }
}

.progress {
  position: relative;
  background-color: var(--darkGrey);
  height: 1px;
  transform: scaleX(var(--progress-out));
  margin-top: mobile-vw(20px);
  width: mobile-vw(160px);

  @include-media ('desktop') {
    margin-top: desktop-vw(20px);
    width: desktop-vw(160px);
  }

  .bar {
    position: absolute;
    inset: 0;
    background-color: var(--green);
    width: 100%;
    height: 1px;
    transform: scaleX(var(--progress-in, 0));
    z-index: 1;
    animation-duration: 4s;
  }
}

.text {
  display: grid;
  grid-template-columns: 1fr;

  &:not(.enabled) {
    .sound {
      opacity: 0;
    }
  }
}

.enable,
.sound {
  grid-column-start: 1;
  grid-row-start: 1;
  transition: 300ms opacity var(--gleasing);
}

.choke {
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;

  > * {
    position: relative;
    width: mobile-vw(564px);
    height: mobile-vw(80px);

    @include-media ('desktop') {
      width: desktop-vw(564px);
      height: desktop-vw(112px);
    }
  }
}

.hide {
  opacity: 0 !important;
}

.pointer-none {
  pointer-events: none;
}
