import { clamp } from '~/libs/maths'

const directions = {
  RIGHT: 'right',
  LEFT: 'left',
  RANDOM: 'random',
}

const animations = {
  SHOW: 'show',
  HIDE: 'hide',
  STAY: 'stay',
}

/**
 * Starts a text shuffle animation in two tiers.
 * First shuffling through random characters and then resolving into the target text.
 *
 * text 							- target text string
 * duration 					- duration of shuffle/resolve animation in seconds
 * delay 							- delay to start shuffling
 * delayResolve 			- delay to start resolving
 * fps 								- framerate
 * glyphs 						- glyphs to use in the shuffle animation
 * animation 					- possible values: `show`, `hide`, `stay`
 * direction 					- possible values: `left`, `right`, `random`
 * onUpdate 					- callback function, returns the output string
 * onComplete					- callback function, returns the output string
 */

export function shuffle({
  text = '',
  duration = 1,
  delay = 0,
  delayResolve = 0.2,
  fps = 60,
  glyphs = ' !#$&%()*+0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[]^_`abcdefghijklmnopqrstuüvwxyz{|}~',
  animation = animations.SHOW,
  direction = directions.RIGHT,
  onUpdate = () => {},
  onComplete = () => {},
} = {}) {
  const _glyphs = glyphs.split('')
  const _text = text.split('')
  const _delta = 1000 / fps

  let _now = Date.now()
  const _start = Date.now()
  let _animationFrameId = null

  // text indices
  let _tindices = _text.map((t, i) => i)

  // flip direction when hiding
  if (animation === animations.HIDE) {
    if (direction === directions.LEFT) direction = directions.RIGHT
    else if (direction === directions.RIGHT) direction = directions.LEFT
  }

  // reverse text indices
  if (direction === directions.LEFT) _tindices.reverse()
  // randomise text indices
  if (direction === directions.RANDOM) _tindices = shuffleArray(_tindices)

  let uLen
  let vLen
  let glyph
  let output
  let complete
  let tidx
  let t
  let u
  let v

  const _onUpdate = () => {
    // Stop and kill the animation
    if (complete) {
      if (_animationFrameId) {
        cancelAnimationFrame(_animationFrameId)
        _animationFrameId = null
      }
      return
    }

    if (Date.now() - _now < _delta) {
      _animationFrameId = requestAnimationFrame(_onUpdate)
      return
    }

    _now = Date.now()
    output = ''

    // t = linear time
    t = ((_now - _start) * 0.001) / duration
    if (animation === animations.HIDE) t = 1 - t

    // u = shuffle curve
    // u starts at delay
    u = clamp(0, t - delay, 1)
    u = quartOut(u)

    // v = resolve curve
    // v starts at u + it's own delay
    v = clamp(0, t - delay - delayResolve, 1)
    // v duration is deducted from it's delay (increase speed)
    v = v * (1 / (1 - delayResolve))
    v = quadInOut(v)

    uLen = Math.round(u * text.length)
    vLen = Math.round(v * text.length)

    for (let i = 0; i < text.length; i++) {
      tidx = _tindices[i]
      glyph = _text[i]

      if (tidx >= uLen && animation !== animations.STAY) glyph = ' '
      if (glyph !== ' ' && tidx >= vLen) glyph = pickRandom(_glyphs)

      output = `${output}${glyph}`
    }

    // loop until u reaches 0
    if (animation === animations.HIDE) complete = u <= 0
    // loop until u reaches 1
    else complete = u >= 1

    if (!complete) _animationFrameId = requestAnimationFrame(_onUpdate)
    else output = animation === animations.HIDE ? '' : text

    if (onUpdate) onUpdate(output)
    if (complete && onComplete) onComplete(output)
  }

  _onUpdate()
}

function quadInOut(t) {
  let t2 = t / 0.5
  if (t2 < 1) return 0.5 * t2 * t2
  t2--
  return -0.5 * (t2 * (t2 - 2) - 1)
}

function quartOut(t) {
  return t - 1.0 ** 3 * (1.0 - t) + 1.0
}

function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[array[i], array[j]] = [array[j], array[i]]
  }
  return array
}

function pickRandom(array) {
  const randomIndex = Math.floor(Math.random() * array.length)
  return array[randomIndex]
}
