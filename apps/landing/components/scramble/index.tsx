import cn from 'clsx'
import type { ReactNode, Ref } from 'react'
import { useCallback, useEffect, useImperativeHandle, useRef } from 'react'
import s from './scramble.module.css'
// import { shuffle } from 'txt-shuffle'
import { shuffle } from './shuffle'

interface ScrambleTextProps {
  children: ReactNode
  className?: string
  initState?: 'show' | 'hide'
}

interface animationProps {
  duration?: number
  fps?: number
  direction?: 'left' | 'right' | 'random'
  delay?: number
  onComplete?: () => void
  onStart?: () => void
}

export interface ScrambleTextRef {
  stay: (props: animationProps) => void
  show: (props: animationProps) => void
  hide: (props: animationProps) => void
}

export function ScrambleText({
  children,
  className,
  initState = 'show',
  ref,
}: ScrambleTextProps & { ref: Ref<ScrambleTextRef> }) {
  const scrambleRef = useRef<HTMLSpanElement>(null)
  const animationIdRef = useRef<number>(0)
  const animationStateRef = useRef<boolean>(false)

  useEffect(() => {
    if (!scrambleRef.current) return

    if (initState === 'hide') {
      scrambleRef.current.innerText = ''
    }
  }, [initState])

  const animation = useCallback(
    (props: animationProps & { animation: 'hide' | 'show' | 'stay' }) => {
      const {
        animation,
        duration,
        fps,
        direction,
        delay,
        onComplete,
        onStart,
      } = props

      // Increment the animation ID to invalidate previous animations
      const currentAnimationId = ++animationIdRef.current

      return shuffle({
        animation,
        text: children,
        fps: fps ?? 60,
        duration: duration ?? 1,
        delay: delay ?? 0,
        direction: direction ?? 'right',
        glyphs: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890#$%&*+-/:?@[]^~',
        onUpdate: (e: string) => {
          if (!scrambleRef.current) return
          if (currentAnimationId !== animationIdRef.current) return

          if (animationStateRef.current === false) {
            animationStateRef.current = true
            onStart?.()
          }

          scrambleRef.current.innerText = e
        },
        onComplete: () => {
          onComplete?.()
          animationStateRef.current = false
        },
      })
    },
    [children]
  )

  useImperativeHandle(
    ref,
    () => ({
      show: (props: animationProps) =>
        animation({
          ...props,
          animation: 'show',
        }),
      hide: (props: animationProps) =>
        animation({
          ...props,
          animation: 'hide',
        }),
      stay: (props: animationProps) =>
        animation({
          ...props,
          animation: 'stay',
        }),
    }),
    [animation]
  )

  return (
    <span className={cn(s.scramble, className)}>
      <span ref={scrambleRef} className={s.animated}>
        {children}
      </span>
      <span className={s.hidden}>{children}</span>
    </span>
  )
}
