.container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 10000;

  @include-media ("mobile") {
    display: none;
  }

  .cursor {
    display: grid;
    grid-template-columns: 1fr;
    height: mobile-vw(23px);
    aspect-ratio: 1;
    padding-left: mobile-vw(24px);
    margin-top: mobile-vw(-2px);
    transition: opacity 0.15s var(--gleasing);

    @include-media ("desktop") {
      height: desktop-vw(23px);
      margin-top: desktop-vw(-2px);
      padding-left: desktop-vw(24px);
    }

    &.pointer {
      opacity: 0;
    }
  }

  .button {
    display: block;
    grid-column-start: 1;
    grid-row-start: 1;
    text-wrap: nowrap;
    height: fit-content;
    width: fit-content;
    background-color: var(--grey);
    padding-bottom: mobile-vw(1.5px);
    padding-inline: mobile-vw(3px);
    /* Init values */
    clip-path: inset(0 100% 0 0);
    opacity: 0;

    @include-media ("desktop") {
      padding-bottom: desktop-vw(1.5px);
      padding-inline: desktop-vw(3px);
    }

    &.chapters {
      color: var(--black);
      background-color: var(--green);
    }
  }

  .text {
    padding-block: mobile-vw(1px);

    @include-media ("desktop") {
      padding-block: desktop-vw(1px);
    }
  }
}
