import { types } from '@theatre/core'
import cn from 'clsx'
import gsap from 'gsap'
import {
  Fragment,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useId,
  useRef,
  useState,
} from 'react'
import tunnel from 'tunnel-rat'
import { useModal } from '~/app/(pages)/(components)/modal'
import { ScrambleText } from '~/components/scramble'
import { useStore } from '~/libs/store'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import s from './cursor.module.css'

const CursorContext = createContext({})
export function useCursor() {
  return useContext(CursorContext)
}

export function Cursor({ children }) {
  const cursorRef = useRef()
  const [isPointer, setIsPointer] = useState(false)
  const [Tunnel] = useState(() => tunnel())
  const { current: currentModal } = useModal()

  const onMouseMove = useCallback(({ clientX, clientY }) => {
    gsap.to(cursorRef.current, {
      x: clientX,
      y: clientY,
      duration: 1,
      ease: 'expo.out',
    })
  }, [])

  useEffect(() => {
    window.addEventListener('mousemove', onMouseMove, false)

    return () => {
      window.removeEventListener('mousemove', onMouseMove, false)
    }
  }, [])

  useEffect(() => {
    //Forces the call when all children are mounted
    const timer = setTimeout(() => {
      let elements = []

      const onMouseEnter = () => {
        setIsPointer(true)
      }
      const onMouseLeave = () => {
        setIsPointer(false)
      }

      elements = [...document.querySelectorAll("[data-cursor='pointer']")]

      for (const element of elements) {
        element.addEventListener('mouseenter', onMouseEnter, false)
        element.addEventListener('mouseleave', onMouseLeave, false)
      }

      return () => {
        for (const element of elements) {
          element.removeEventListener('mouseenter', onMouseEnter, false)
          element.removeEventListener('mouseleave', onMouseLeave, false)
        }
      }
    }, 0)
    return () => clearTimeout(timer)
  }, [children])

  return (
    <CursorContext.Provider value={{ Tunnel, isPointer }}>
      {children}
      <div className={s.container}>
        <div ref={cursorRef}>
          <div
            className={cn(
              'pxs',
              s.cursor,
              (isPointer || currentModal) && s.pointer
            )}
          >
            <Tunnel.Out />
          </div>
        </div>
      </div>
    </CursorContext.Provider>
  )
}

export function CursorTunnel({ children }) {
  const { Tunnel } = useCursor()
  const uuid = useId()

  if (!Tunnel) return null

  return (
    <Tunnel.In>
      <Fragment key={uuid}>{children}</Fragment>
    </Tunnel.In>
  )
}

export function CursorButton({
  children,
  type = 'chapters',
  sheetId = null,
  className = '',
  onEnter = () => {},
  onLeave = () => {},
}) {
  const hasEntered = useRef(false)
  const scrambleRef = useRef(null)
  const buttonRef = useRef()
  const sheet = useSheet(sheetId)
  const [timeline, setTimeline] = useState()
  const { isPointer } = useCursor()

  useEffect(() => {
    const tl = gsap.timeline({ paused: true })
    setTimeline(tl)

    return () => {
      tl?.kill()
    }
  }, [])

  const overlayAnimation = useCallback(
    (mode) => {
      const proxy = {
        progress: 0,
      }

      timeline?.to(proxy, {
        progress: 1,
        duration: 0.5,
        ease: 'expo.inOut',
        onUpdate: () => {
          if (mode === 'enter') {
            buttonRef.current?.style.setProperty(
              'clip-path',
              `inset(0 ${(1 - proxy.progress) * 100}% 0 0)`
            )
          } else {
            buttonRef.current?.style.setProperty(
              'clip-path',
              `inset(0 0 0 ${proxy.progress * 100}%)`
            )
          }
        },
      })

      timeline?.play()
    },
    [timeline]
  )

  useTheatre(
    sheet ?? null,
    'cursor',
    {
      show: types.boolean(false),
      opacity: types.number(0, { range: [0, 1] }),
    },
    {
      onValuesChange: ({ show, opacity }) => {
        const introCompleted = useStore.getState().introCompleted

        if (!introCompleted) return
        buttonRef.current.style.setProperty('opacity', opacity)

        if (show) {
          if (hasEntered.current || isPointer) return
          hasEntered.current = true
          scrambleRef.current?.stay({
            delay: 0.5,
            duration: 0.4,
            direction: 'random',
          })
          overlayAnimation('enter')
          onEnter()
        } else {
          if (!hasEntered.current) return
          hasEntered.current = false
          scrambleRef.current?.hide({
            duration: 0.5,
            direction: 'random',
          })
          overlayAnimation('leave')
          onLeave()
        }
      },
      deps: [isPointer],
    }
  )

  useEffect(() => {
    // Control label over protected elements
    if (!hasEntered.current) return

    if (isPointer) {
      onLeave()
      return
    }

    onEnter()
  }, [isPointer])

  return (
    <button
      ref={buttonRef}
      type="button"
      className={cn(s.button, s[type], className)}
    >
      <ScrambleText ref={scrambleRef} className={s.text}>
        {children}
      </ScrambleText>
    </button>
  )
}
