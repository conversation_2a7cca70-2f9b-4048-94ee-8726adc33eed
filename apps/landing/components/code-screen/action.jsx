"use server";

const EMPTY_CODE = new Array(6).fill("");
const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export async function codeScreenAction(_, formData) {
  let code = formData?.getAll("code");
  code = code.reduce((acc, curr) => acc + curr, "").toUpperCase();

  const [response] = await Promise.all([
    fetch(`${BASE_URL}api/referrals/check?code=${code}`, {
      headers: {
        "Content-Type": "application/json",
      },
    }),
    // Gives at least 1 second delay for animations
    new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 1000);
    }),
  ]);

  const data = await response.json();
  const success = data?.exists;

  if (success) {
    return {
      success,
      code: EMPTY_CODE,
      redirect: `https://yby.energy/drop?inviteCode=${code}`,
      // redirect: `http://localhost:3001/drop?inviteCode=${code}`, // for local development
    };
  }

  return {
    success: false,
    code: EMPTY_CODE,
  };
}
