.waitlist {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: desktop-vw(33px);
  color: #ffffff;

  @include-media ('mobile') {
    gap: mobile-vw(33px);
    width: columns(8);
    margin-inline: auto;
  }

  &:not([data-modal="waitlist"]) {
    .logo {
      pointer-events: none !important;
    }

    opacity: 0;
    pointer-events: none;
  }

  &[data-modal="waitlist"] {
    .form {
      .input {
        .frame-border {
          path:first-of-type {
            transition-delay: 500ms;
            stroke-dashoffset: 0;
          }
        }
      }
    }
  }

  .logo {
    position: absolute;
    inset: 0;
  }

  .title,
  .description {
    display: grid;
    grid-template-columns: 1fr;

    .enter,
    .access,
    .error {
      grid-column-start: 1;
      grid-row-start: 1;
      text-align: center;
    }
  }

  .title {
    .enter,
    .access,
    .error {
      @include-media ('mobile') {
        width: columns(8);
        margin-block: auto;
      }
    }

    .enter {
      color: var(--green);
    }

    .access {
      color: var(--green);
    }

    .error {
      color: var(--red);
    }
  }

  .description {
    .enter,
    .access,
    .error {
      @include-media ('mobile') {
        width: columns(4);
      }
    }
  }

  .close {
    position: absolute;
    top: 0;
    right: 0;

    svg {
      transition: stroke 300ms var(--gleasing) 250ms;
    }

    &[data-success="false"] {
      svg {
        path {
          stroke: var(--red);
        }
      }
    }

    width: desktop-vw(113px);

    @include-media ('mobile') {
      width: mobile-vw(113px);
    }
  }

  .form {
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: desktop-vw(12px);
    --color: var(--green);

    @include-media ('mobile') {
      width: columns(6);
      flex-wrap: wrap;
      justify-content: center;
      row-gap: mobile-vw(12px);
    }

    &[data-success="false"] {
      --color: var(--red);

      .input {
        path:last-of-type {
          transition-delay: 250ms !important;
        }
      }
    }

    .submit {
      cursor: pointer;
      opacity: 0.5;
      pointer-events: none;
      transition: opacity 0.3s var(--gleasing);
    }

    .submit {
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: 1fr;

      > * {
        grid-column-start: 1;
        grid-row-start: 1;
        text-align: center;
        color: var(--color);
      }
    }

    &:has(input:valid) .submit {
      opacity: 1;
      pointer-events: all;
    }

    .input {
      position: relative;
      width: 100%;
      height: 100%;
      text-align: center;
      outline: none !important;
      width: desktop-vw(350px);
      height: desktop-vw(64px);

      @include-media ('mobile') {
        width: columns(6);
        height: mobile-vw(64px);
        font-size: 16px !important;
      }

      display: grid;
      grid-template-columns: 1fr;

      input {
        &:focus-visible {
          outline: none !important;
        }

        &:-webkit-autofill {
          -webkit-text-fill-color: var(--green) !important;
          transition: background-color 5000s ease-in-out 0s;
        }
      }
    }

    &:has(input:valid) .frame-border {
      path:first-of-type {
        stroke: var(--white) !important;
      }
    }

    .border,
    .field {
      grid-column-start: 1;
      grid-row-start: 1;
    }

    .frame-border {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;

      path {
        transition-property: stroke-dashoffset, stroke-dasharray, stroke;
        transition-duration: 300ms;
        transition-timing-function: var(--gleasing);
      }

      path:first-of-type {
        stroke-dasharray: 820;
        stroke-dashoffset: 820;
      }

      path:not(:first-of-type) {
        stroke: var(--color);
      }

      path:nth-of-type(2),
      path:nth-of-type(4) {
        stroke-dasharray: 36;
        stroke-dashoffset: 36;
      }

      path:nth-of-type(3),
      path:nth-of-type(5) {
        stroke-dasharray: 12;
        stroke-dashoffset: 12;
      }

      &:has(+ input:focus):not(:has(+ input:valid)) {
        path:nth-of-type(2),
        path:nth-of-type(3),
        path:nth-of-type(4),
        path:nth-of-type(5) {
          stroke-dashoffset: 0;
        }
      }
    }
  }
}
