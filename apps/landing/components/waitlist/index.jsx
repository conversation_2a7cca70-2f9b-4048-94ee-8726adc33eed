'use client'

import cn from 'clsx'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/navigation'
import {
  memo,
  useActionState,
  useCallback,
  useEffect,
  useId,
  useRef,
  useState,
} from 'react'
import { useModal } from '~/app/(pages)/(components)/modal'
import { Logo } from '~/app/(pages)/(components)/navigation/logo'
import { useAudioDebounced } from '~/hooks/use-audio'
import { SecondaryButton } from '../button'
import { Frames } from '../frames'
import { ScrambleText } from '../scramble'
import { waitlistAction } from './action'
import s from './waitlist.module.css'

const initialValue = {
  email: '',
  success: null,
}

export const Waitlist = memo(function Waitlist() {
  const { current: currentModal } = useModal()
  const [formKey, setFormKey] = useState(null)

  return (
    <div className={s.waitlist} data-modal={currentModal}>
      <Form key={formKey} setKey={setForm<PERSON><PERSON>} />
      <Frames direction="horizontal" color="darkGrey" />
    </div>
  )
})

function Form({ setKey }) {
  const id = useId()
  const hasOpenedRef = useRef(false)
  // Text refs
  const enterTextRef = useRef(null)
  const enterDescriptionRef = useRef(null)
  const successTextRef = useRef(null)
  const successDescriptionRef = useRef(null)
  const errorTextRef = useRef(null)
  const errorDescriptionRef = useRef(null)
  const submitEnterRef = useRef(null)
  const submitSuccessRef = useRef(null)
  const submitErrorRef = useRef(null)
  const submitPendingRef = useRef(null)
  // Timeouts refs
  const onResponseTimeoutRef = useRef(null)
  const focusTimeoutRef = useRef(null)
  const cleanFormTimeoutRef = useRef(null)
  // Sound
  const loopSoundRef = useRef(null)
  const { play: playSound, stop: stopSound } = useAudioDebounced(0)
  // Form
  const formRef = useRef(null)
  const inputRef = useRef(null)
  const router = useRouter()
  const { current: currentModal, close: closeModal } = useModal()
  const [state, formAction, isPending] = useActionState(
    waitlistAction,
    initialValue
  )

  const onResponse = useCallback(({ targets, soundId }) => {
    enterTextRef.current?.hide({
      duration: 0.25,
      direction: 'left',
      onComplete: () => {
        targets.text?.show({
          duration: 0.5,
          direction: 'right',
          onStart: () => {
            playSound({ soundId })
          },
        })
      },
    })

    enterDescriptionRef.current?.hide({
      duration: 0.25,
      direction: 'left',
      onComplete: () => {
        targets.description?.show({
          duration: 0.5,
          direction: 'right',
        })
      },
    })

    submitEnterRef.current?.hide({
      duration: 0.25,
      direction: 'left',
      onComplete: () => {
        targets.submit?.show({
          direction: 'right',
        })
      },
    })
  }, [])

  const onOpen = useCallback(() => {
    hasOpenedRef.current = true

    enterTextRef.current?.show({
      direction: 'random',
      onStart: () => {
        playSound({
          soundId: 'Sfx_UI_GreenText_Appear',
          rate: 1.75,
        })
      },
    })

    enterDescriptionRef.current?.show({
      direction: 'random',
      onComplete: () => {
        focusTimeoutRef.current = setTimeout(() => {
          inputRef.current?.focus()
          playSound({ soundId: 'Sfx_UI_FirstSquare_Highlight' })
        }, 500)
      },
    })

    submitEnterRef.current?.show({
      direction: 'random',
    })
  }, [])

  const onClose = useCallback(() => {
    hasOpenedRef.current = false

    cleanFormTimeoutRef.current = setTimeout(() => {
      setKey(id)
    }, 1000)

    stopSound({ id: loopSoundRef.current })
    clearTimeout(focusTimeoutRef.current)
    clearTimeout(onResponseTimeoutRef.current)
    focusTimeoutRef.current = null
    onResponseTimeoutRef.current = null
  }, [])

  useEffect(() => {
    // Success
    if (state.success === true) {
      onResponse({
        targets: {
          text: successTextRef.current,
          description: successDescriptionRef.current,
          submit: submitSuccessRef.current,
        },
        soundId: 'Sfx_UI_AccessGranted',
      })

      onResponseTimeoutRef.current = setTimeout(() => {
        router.push(state.redirect)
        setKey(id)
      }, 3000)
    }

    // Error
    if (state.success === false) {
      onResponse({
        targets: {
          text: errorTextRef.current,
          description: errorDescriptionRef.current,
          submit: submitErrorRef.current,
        },
        soundId: 'Sfx_UI_AccessDenied',
      })

      onResponseTimeoutRef.current = setTimeout(() => {
        setKey(id)
        inputRef.current?.focus()
      }, 3000)
    }

    return () => {
      clearTimeout(onResponseTimeoutRef.current)
      onResponseTimeoutRef.current = null
    }
  }, [state])

  useEffect(() => {
    if (hasOpenedRef.current && currentModal !== 'waitlist') {
      onClose()

      return () => {
        clearTimeout(cleanFormTimeoutRef.current)
        cleanFormTimeoutRef.current = null
      }
    }

    if (currentModal === 'waitlist') {
      onOpen()
    }

    return () => {
      clearTimeout(focusTimeoutRef.current)
      focusTimeoutRef.current = null
      cleanFormTimeoutRef.current = null
    }
  }, [currentModal])

  useEffect(() => {
    // On pending
    if (isPending) {
      loopSoundRef.current = playSound({
        soundId: 'Sfx_UI_DataProcessing_Loop',
        loop: true,
      })

      submitEnterRef.current?.hide({
        duration: 0.25,
        direction: 'left',
        onComplete: () => {
          submitPendingRef.current?.show({
            direction: 'random',
          })
        },
      })

      return
    }

    stopSound({ id: loopSoundRef.current })
    submitPendingRef.current?.hide({
      duration: 0.25,
      direction: 'left',
    })
  }, [isPending])

  return (
    <>
      <SecondaryButton
        className={cn('ps uppercase', s.close)}
        onClick={() => {
          closeModal()
          playSound({ soundId: 'Sfx_UI_Close', volume: 0.4 })
        }}
        data-success={state.success}
      >
        {formData.button}
      </SecondaryButton>
      <Logo className={s.logo} />
      <div className={cn('h3', s.title)} data-success={state.success}>
        <ScrambleText className={s.enter} ref={enterTextRef} initState="hide">
          {formData.title.enter}
        </ScrambleText>
        <ScrambleText
          className={s.access}
          ref={successTextRef}
          initState="hide"
        >
          {formData.title.access}
        </ScrambleText>
        <ScrambleText className={s.error} ref={errorTextRef} initState="hide">
          {formData.title.error}
        </ScrambleText>
      </div>
      <div className={cn('ps-text uppercase', s.description)}>
        <ScrambleText
          className={s.enter}
          ref={enterDescriptionRef}
          initState="hide"
        >
          {formData.description.enter}
        </ScrambleText>
        <ScrambleText
          className={s.access}
          ref={successDescriptionRef}
          initState="hide"
        >
          {formData.description.access}
        </ScrambleText>
        <ScrambleText
          className={s.error}
          ref={errorDescriptionRef}
          initState="hide"
        >
          {formData.description.error}
        </ScrambleText>
      </div>
      <form
        className={cn(s.form, isPending && s.pending)}
        data-success={state.success}
        ref={formRef}
      >
        <Input ref={inputRef} />
        <button
          type="submit"
          className={cn('h4 green uppercase', s.submit)}
          formAction={formAction}
        >
          <ScrambleText
            ref={submitEnterRef}
            initState="hide"
            className={s.enter}
          >
            {formData.submit.enter}
          </ScrambleText>
          <ScrambleText
            ref={submitSuccessRef}
            initState="hide"
            className={s.success}
          >
            {formData.submit.success}
          </ScrambleText>
          <ScrambleText
            ref={submitErrorRef}
            initState="hide"
            className={s.error}
          >
            {formData.submit.error}
          </ScrambleText>
          <ScrambleText ref={submitPendingRef} initState="hide">
            {formData.submit.pending}
          </ScrambleText>
        </button>
      </form>
    </>
  )
}

function Input({ ref }) {
  const [active, setActive] = useState(false)
  const { play: playSound } = useAudioDebounced(100)

  return (
    <div className={cn(s.input, active && s.active)}>
      <InputBorderSVG className={s['frame-border']} />
      <input
        ref={ref}
        type="email"
        name="email"
        required
        pattern="[^@\s]+@[^@\s]+\.[^@\s]+"
        className={cn('h4 green uppercase', s.field)}
        defaultValue={''}
        onChange={({ target }) => {
          setActive(target.value.length > 0)

          if (target.value.length > 0) {
            playSound({ soundId: 'Sfx_UI_Type_Character' })
          }
        }}
      />
    </div>
  )
}

const InputBorderSVG = dynamic(() => import('~/assets/svg/input-border.svg'), {
  ssr: false,
})

const formData = {
  button: 'close',
  title: {
    enter: 'ENTER YOUR EMAIL',
    access: 'JOINED THE WAITLIST',
    error: 'SOMETHING WENT WRONG',
  },
  description: {
    enter: 'become a member of the waitlist',
    access: 'Welcome to Ibicash',
    error: 'Please check your email and try again',
  },
  submit: {
    enter: 'JOIN',
    success: 'JOINED',
    error: 'RETRY',
    pending: 'JOINING...',
  },
}
