'use server'

import sgMail from '@sendgrid/mail'

sgMail.setApi<PERSON>ey(process.env.SENDGRID_API_KEY)

export async function waitlistAction(_, formData) {
  const email = formData?.get('email')

  if (!email || !email.includes('@')) {
    return {
      success: false,
      code: 'invalid_email',
    }
  }

  try {
    await sgMail.send({
      to: email,
      from: process.env.SENDGRID_FROM_EMAIL, // must be a verified sender in SendGrid
      subject: 'Thanks for signing up!',
      text: 'You’ve successfully joined the waitlist!',
      html: '<strong>You’ve successfully joined the waitlist!</strong>',
    })

    return {
      success: true,
      code: '',
    }
  } catch (error) {
    console.error('SendGrid error:', error)
    return {
      success: false,
      code: 'send_failed',
    }
  }
}
