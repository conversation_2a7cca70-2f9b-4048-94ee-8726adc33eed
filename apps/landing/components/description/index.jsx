'use client'

import { types } from '@theatre/core'
import cn from 'clsx'
import { useRef } from 'react'
import { useIntroTheatre } from '~/app/(pages)/home/<USER>/intro'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import s from './description.module.css'

export function Description({
  children,
  className,
  squareSize = 'small',
  theaterConfig,
}) {
  const descriptionRef = useRef()
  const sheet = useSheet(theaterConfig?.sheet)

  useTheatre(
    sheet,
    theaterConfig?.object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        descriptionRef.current?.style?.setProperty('opacity', progress)
      },
    }
  )

  useIntroTheatre({
    theaterConfig,
    onValuesChange: ({ progress }) => {
      descriptionRef.current?.style?.setProperty('opacity', progress)
    },
  })

  return (
    <p
      ref={descriptionRef}
      className={cn('uppercase', s.square, s[squareSize], className)}
      data-cursor="pointer"
    >
      {children}
    </p>
  )
}
