.square {
  position: relative;
  --unit: mobile-vw(8px);

  /* @include-media ('mobile') {
    opacity: 1 !important;
  } */

  &.large {
    @include-media ('desktop') {
      --unit: desktop-vw(8px);
    }
  }

  &.small {
    @include-media ('desktop') {
      --unit: desktop-vw(6px);
    }
  }

  &::before {
    top: mobile-vw(4.5px);
    left: calc(-2 * var(--layout-margin) - var(--unit));

    @include-media ('desktop') {
      left: calc(-1 * var(--layout-margin) - var(--unit));
      top: desktop-vw(6px);
    }

    content: "";
    position: absolute;
    width: var(--unit);
    height: var(--unit);
    background: var(--green);
  }
}
