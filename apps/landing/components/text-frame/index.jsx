import { types } from '@theatre/core'
import cn from 'clsx'
import gsap from 'gsap'
import { useEffect, useRef, useState } from 'react'
import { useIntroTheatre } from '~/app/(pages)/home/<USER>/intro'
import TextFrameSVG_L from '~/assets/svg/text-frame-l.svg'
import TextFrameSVG_M from '~/assets/svg/text-frame-m.svg'
import TextFrameSVG from '~/assets/svg/text-frame.svg'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import s from './text-frame.module.css'

export function TextFrame({ className, theaterConfig, size = 's' }) {
  const frameRef = useRef()
  const pathRef = useRef()
  const sheet = useSheet(theaterConfig?.sheet)
  const [timeline, setTimeline] = useState()

  useEffect(() => {
    if (!pathRef.current) return

    const tl = gsap.timeline({ paused: true })
    const path = pathRef.current.children[0]

    if (!path) return
    const length = path.getTotalLength() + 5

    gsap.set(path, {
      strokeDasharray: length,
      strokeDashoffset: length,
    })

    tl.to(path, {
      strokeDashoffset: 0,
      duration: 1,
    })

    setTimeline(tl)

    return () => {
      tl.kill()
    }
  }, [sheet])

  useTheatre(
    sheet,
    theaterConfig?.object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        timeline?.pause()?.progress(progress)
      },
    }
  )

  useIntroTheatre({
    theaterConfig,
    onValuesChange: ({ progress }) => {
      timeline?.pause()?.progress(progress)
    },
  })

  return (
    <div ref={frameRef} className={cn(s.textFrame, className, s[size])}>
      {size === 's' && <TextFrameSVG ref={pathRef} />}
      {size === 'm' && <TextFrameSVG_M ref={pathRef} />}
      {size === 'l' && <TextFrameSVG_L ref={pathRef} />}
    </div>
  )
}
