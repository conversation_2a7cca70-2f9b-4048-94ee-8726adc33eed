'use client'

import { types } from '@theatre/core'
import cn from 'clsx'
import { Suspense, useRef } from 'react'
import { useModal } from '~/app/(pages)/(components)/modal'
import Delimiter from '~/assets/svg/delimiter.svg'
import { useStore } from '~/libs/store'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import s from './frames.module.css'

export function Frames(props) {
  return (
    <Suspense fallback={null}>
      <FramesComponent {...props} />
    </Suspense>
  )
}

function FramesComponent({ direction = 'horizontal', objectID = null }) {
  const framesRef = useRef(null)
  const introSheet = useSheet('intro')
  const loaderLoaded = useStore((state) => state.loaderLoaded)
  const { current: currentModal } = useModal()

  useTheatre(
    objectID ? introSheet : null,
    objectID,
    { opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }) },
    {
      onValuesChange: ({ opacity }) => {
        framesRef.current?.style?.setProperty('opacity', opacity)
      },
    }
  )

  return (
    <div
      className={cn(
        s.frames,
        s[direction],
        loaderLoaded && currentModal === 'code' && s.fill
      )}
      ref={framesRef}
    >
      <div className={s.delimiter}>
        <Delimiter />
      </div>
      <div className={s.delimiter}>
        <Delimiter />
      </div>
    </div>
  )
}
