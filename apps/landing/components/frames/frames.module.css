.frames {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10001;
  width: 100%;
  height: 100%;
  padding: var(--layout-margin);

  @include-media ('mobile') {
    opacity: 1 !important;

    .delimiter {
      path {
        opacity: 1 !important;
      }
    }
  }

  .delimiter {
    display: inline-grid;
    width: mobile-vw(28px);
    height: mobile-vw(8px);

    @include-media "desktop" {
      width: desktop-vw(28px);
      height: desktop-vw(8px);
    }

    transition: opacity 0.3s var(--gleasing);
  }

  &:not(.fill) {
    opacity: 0;
  }

  &.vertical {
    flex-direction: column;

    .delimiter:last-child {
      transform: rotate(180deg);
    }
  }

  &.horizontal {
    flex-direction: row;

    .delimiter:first-child {
      @include-media "desktop" {
        transform: translateX(desktop-vw(-10px)) rotate(-90deg);
      }
    }

    .delimiter:last-child {
      @include-media "desktop" {
        transform: translateX(desktop-vw(10px)) rotate(90deg);
      }
    }

    &.fill {
      .delimiter {
        opacity: 0.3;
      }
    }
  }

  @include-media "mobile" {
    &.vertical {
      flex-direction: row;

      .delimiter:first-child {
        transform: translateX(mobile-vw(-10px)) rotate(-90deg);
      }

      .delimiter:last-child {
        transform: translateX(mobile-vw(10px)) rotate(90deg);
      }
    }

    &.horizontal {
      display: none;
    }
  }
}
