import { type Rect, useWindowSize } from 'hamo'
import {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react'
import { useTempus } from 'tempus/react'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { desktopVW, mobileVW } from '~/libs/utils'

export function DotsGridCanvas({
  rect,
  className,
  waveColor = '172, 255, 70',
  dotsColor = 'rgba(255, 255, 255, 0.08)',
  speed = 1,
  ref,
}: {
  rect: Rect
  className?: string
  waveColor?: string
  dotsColor?: string
  speed?: number
  ref: (instance: { play: () => void } | null) => void
}) {
  const timeRef = useRef(0)
  const animateRef = useRef(false)
  const canvasRef = useRef<HTMLCanvasElement>(null!)
  const { width: windowWidth } = useWindowSize()
  const { isDesktop } = useDeviceDetection()
  const gridSpecs = useMemo(() => {
    if (!rect.width || !rect.height)
      return {
        dotSpacing: 8,
        cols: 0,
        rows: 0,
        marginX: 0,
        marginY: 0,
        dotSize: 2,
      }

    const { width, height } = rect

    const dotSize = isDesktop
      ? desktopVW(2, windowWidth ?? 1)
      : mobileVW(2, windowWidth ?? 1)
    const dotSpacing = isDesktop
      ? desktopVW(8, windowWidth ?? 1)
      : mobileVW(10, windowWidth ?? 1)
    const dotArea = dotSpacing + dotSize

    const exactCols = (width - dotSize) / dotArea
    const exactRows = (height - dotSize) / dotArea
    const cols = Math.floor(exactCols)
    const rows = Math.floor(exactRows)

    const remainderX = width - (cols * dotArea - dotSpacing)
    const remainderY = height - (rows * dotArea - dotSpacing)
    const marginX = remainderX / 2
    const marginY = remainderY / 2

    return {
      dotSpacing,
      cols,
      rows,
      marginX,
      marginY,
      dotSize,
    }
  }, [rect, windowWidth, isDesktop])

  const drawGrid = useCallback(
    (progress: number) => {
      const { dotSize, marginX, marginY, cols, rows, dotSpacing } = gridSpecs
      const { width, height } = rect
      const ctx = canvasRef.current.getContext('2d')

      if (!ctx) return
      ctx.clearRect(0, 0, width, height)
      const current = Math.ceil(progress * (cols + 6)) - 3

      for (let i = 0; i < rows; i++) {
        for (let j = 0; j < cols; j++) {
          const x = marginX + j * (dotSize + dotSpacing)
          const y = marginY + i * (dotSize + dotSpacing)

          const distance = current - j
          const opacity = Math.max(1 - distance / 4, 0) * 0.5

          const color =
            distance < 4 && distance >= 0
              ? `rgba(${waveColor}, ${opacity})`
              : dotsColor

          ctx.beginPath()
          ctx.fillStyle = color
          ctx.fillRect(x - dotSize * 0.5, y - dotSize * 0.5, dotSize, dotSize)
          ctx.fill()
        }
      }
    },
    [gridSpecs, rect]
  )

  useEffect(() => {
    if (!rect.width || !rect.height) return

    const { width, height } = rect
    const ctx = canvasRef.current.getContext('2d')

    if (!ctx) return
    const dpr = window.devicePixelRatio

    canvasRef.current.width = Math.round(width * dpr)
    canvasRef.current.height = Math.round(height * dpr)
    ctx.scale(dpr, dpr)

    drawGrid(-1)
  }, [rect])

  useTempus((_, deltaTime) => {
    if (!animateRef.current) {
      timeRef.current = 0
      drawGrid(-1)
      return
    }

    if (timeRef.current >= 1) {
      animateRef.current = false
      return
    }

    timeRef.current += deltaTime * 0.0016 * speed
    drawGrid(timeRef.current)
  })

  useImperativeHandle(ref, () => ({
    play: () => {
      animateRef.current = true
    },
  }))

  return <canvas ref={canvasRef} className={className} />
}
