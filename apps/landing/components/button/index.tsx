import { types } from '@theatre/core'
import cn from 'clsx'
import gsap from 'gsap'
import { useRect } from 'hamo'
import dynamic from 'next/dynamic'
import {
  type ButtonHTMLAttributes,
  type ReactNode,
  type RefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import CtaBorderLeft from '~/assets/svg/cta-border-left.svg'
import CtaBorderRight from '~/assets/svg/cta-border-right.svg'
import { useAudioDebounced } from '~/hooks/use-audio'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useGlobalAudio } from '../global-audio'
import { Link } from '../link'
import { ScrambleText, type ScrambleTextRef } from '../scramble'
import s from './button.module.css'
import { DotsGridCanvas } from './dots-grid'

const ButtonFrameSVG = dynamic(
  () => import('~/assets/svg/sound-button-frame.svg'),
  {
    ssr: false,
  }
)

type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> & {
  href?: string
  as?: React.ElementType
  className?: string
  children: ReactNode
  ref?: RefObject<HTMLButtonElement>
}

export function Button({
  children,
  className,
  href,
  as,
  ...props
}: ButtonProps) {
  if (href) {
    return (
      <Link
        {...props}
        href={href}
        className={cn(s.button, className)}
        data-cursor="pointer"
      >
        {children}
      </Link>
    )
  }

  return (
    <button
      {...props}
      className={cn(s.button, className)}
      data-cursor="pointer"
    >
      {children}
    </button>
  )
}

export function PrimaryButton({
  children,
  className,
  soundId,
  ...props
}: ButtonHTMLAttributes<HTMLButtonElement> & {
  soundId: string
}) {
  const dotsGridRef = useRef<{ play: () => void }>(null)
  const [rectRef, rect] = useRect()
  const scrambleRef = useRef<ScrambleTextRef>(null)
  const { play: playSound } = useAudioDebounced(300)

  return (
    <Button
      ref={rectRef}
      className={cn(s.primary, className, 'move-gradient')}
      onPointerEnter={() => {
        playSound({ soundId: 'Sfx_UI_Hover', volume: 0.4 })

        scrambleRef.current?.stay({
          duration: 0.5,
          fps: 60,
          direction: 'right',
        })
        dotsGridRef.current?.play()
      }}
      {...props}
    >
      <DotsGridCanvas
        ref={dotsGridRef}
        rect={rect}
        className={s.canvas}
        waveColor="0, 0, 0"
        dotsColor="rgba(0, 0, 0, 0.12)"
      />
      <ScrambleText ref={scrambleRef} className={s.text}>
        {children}
      </ScrambleText>
    </Button>
  )
}

export function SecondaryButton({
  children,
  className,
  soundId,
  ...props
}: ButtonHTMLAttributes<HTMLButtonElement> & {
  soundId: string
}) {
  const dotsGridRef = useRef<{ play: () => void }>(null)
  const scrambleRef = useRef<ScrambleTextRef>(null)
  const [rectRef, rect] = useRect()
  const { play: playSound } = useAudioDebounced(300)

  return (
    <Button
      className={cn(s.secondary, className, 'move-gradient')}
      onPointerEnter={() => {
        playSound({ soundId: 'Sfx_UI_Hover', volume: 0.4 })

        scrambleRef.current?.stay({
          duration: 0.5,
          fps: 60,
          direction: 'right',
        })
        dotsGridRef.current?.play()
      }}
      ref={rectRef}
      {...props}
    >
      <DotsGridCanvas ref={dotsGridRef} rect={rect} className={s.canvas} />
      <div className={s.border}>
        <div className={s.stroke} />
        <CtaBorderRight className={s.right} />
        <CtaBorderLeft className={s.left} />
      </div>
      <ScrambleText ref={scrambleRef} className={s.text}>
        {children}
      </ScrambleText>
    </Button>
  )
}

export function TertiaryButton({
  children,
  className,
  theaterConfig,
  ...props
}: ButtonHTMLAttributes<HTMLButtonElement> & {
  theaterConfig?: {
    sheet?: string
    object?: string
  }
}) {
  const { isDesktop } = useDeviceDetection()
  const scrambleRef = useRef<ScrambleTextRef>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const sheet = useSheet(theaterConfig?.sheet ?? '')
  const [timeline, setTimeline] = useState<ReturnType<typeof gsap.timeline>>()
  const { play: playSound } = useAudioDebounced(300)

  const overlayAnimation = useCallback(
    (onUpdate: (progress: number) => void) => {
      if (!isDesktop) return

      const proxy = {
        progress: 0,
      }

      timeline?.to(proxy, {
        progress: 1,
        duration: 0.5,
        ease: 'expo.inOut',
        onUpdate: () => {
          onUpdate(proxy.progress)
        },
      })
    },
    [isDesktop, timeline]
  )

  useEffect(() => {
    const tl = gsap.timeline()
    setTimeline(tl)

    return () => {
      tl?.kill()
    }
  }, [])

  useTheatre(
    sheet,
    theaterConfig?.object ?? '',
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        buttonRef.current?.style.setProperty('opacity', `${progress}`)
      },
    }
  )

  return (
    <Button
      ref={buttonRef}
      className={cn(s.tertiary, className)}
      onPointerEnter={() => {
        playSound({ soundId: 'Sfx_UI_Click_Generic', volume: 0.4, rate: 1.75 })
        overlayAnimation((progress) => {
          overlayRef.current?.style.setProperty(
            'clip-path',
            `inset(0 ${(1 - progress) * 100}% 0 0)`
          )
        })

        scrambleRef.current?.stay({
          duration: 0.75,
          direction: 'random',
        })
      }}
      onPointerLeave={() => {
        overlayAnimation((progress) => {
          overlayRef.current?.style.setProperty(
            'clip-path',
            `inset(0 0 0 ${progress * 100}%)`
          )
        })
      }}
      {...props}
    >
      <span className={s.text}>{children}</span>
      <div ref={overlayRef} className={s.overlay}>
        <ScrambleText ref={scrambleRef}>{children}</ScrambleText>
      </div>
    </Button>
  )
}

export function SoundButton({
  className,
  ref,
  ...props
}: ButtonProps & {
  ref?: RefObject<HTMLButtonElement>
}) {
  const dotsGridRef = useRef<{ play: () => void }>(null)
  const [rectRef, rect] = useRect()
  const barsRef = useRef<Array<BarInstance | null>>([])
  const { isPlaying, mute, unmute } = useGlobalAudio()
  const { play: playSound } = useAudioDebounced(300)

  useEffect(() => {
    if (isPlaying) {
      for (const bar of barsRef.current) {
        bar?.play()
      }

      return
    }

    for (const [index, bar] of barsRef.current.entries()) {
      bar?.pause(0.1 * index)
    }
  }, [isPlaying])

  return (
    <Button
      className={cn(s.sound, className, isPlaying && s.active)}
      type="button"
      onPointerEnter={() => {
        playSound({ soundId: 'Sfx_UI_Hover', volume: 0.4 })
        dotsGridRef.current?.play()
        for (const [index, bar] of barsRef.current.entries()) {
          bar?.enter(0.05 * index, isPlaying)
        }
      }}
      onPointerLeave={() => {
        for (const bar of barsRef.current) {
          bar?.leave(isPlaying)
        }
      }}
      onClick={() => {
        if (isPlaying) {
          mute()

          return
        }

        unmute()
      }}
      ref={(el: HTMLButtonElement) => {
        rectRef(el)
        ref.current = el
      }}
      {...props}
    >
      <ButtonFrameSVG className={s.border} />
      <DotsGridCanvas
        ref={dotsGridRef}
        rect={rect}
        className={s.canvas}
        speed={1.5}
      />
      <div className={s.bars}>
        {bars.map(({ id, delay }, index) => (
          <Bar
            key={id}
            index={index}
            delay={delay}
            ref={(el: BarInstance | null) => {
              barsRef.current[index] = el
            }}
          />
        ))}
      </div>
    </Button>
  )
}

type BarInstance = {
  play: () => void
  pause: (delay: number) => void
  enter: (delay: number, isPlaying: boolean) => void
  leave: (isPlaying: boolean) => void
}

type BarProps = {
  delay: number
  index: number
  ref: (instance: BarInstance | null) => void
}

function Bar({ delay, ref }: BarProps) {
  const barRef = useRef<HTMLDivElement>(null)
  const [timeline, setTimeline] = useState<ReturnType<typeof gsap.timeline>>()

  useEffect(() => {
    const tl = gsap.timeline({ paused: true })

    tl.fromTo(
      barRef.current,
      {
        transform: 'scaleY(0.25)',
      },
      {
        delay,
        transform: 'scaleY(1)',
        duration: 0.6,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
      }
    )

    setTimeline(tl)

    return () => {
      tl.kill()
    }
  }, [delay])

  useImperativeHandle(ref, () => {
    return {
      play: () => {
        timeline?.progress(0)?.play()
      },
      pause: (delay: number) => {
        timeline?.pause()

        gsap.to(barRef.current, {
          delay,
          transform: 'scaleY(0.25)',
          duration: 0.4,
          ease: 'power2.inOut',
        })
      },
      enter: (delay: number, isPlaying: boolean) => {
        if (isPlaying) {
          timeline?.pause()
          return
        }

        gsap.fromTo(
          barRef.current,
          {
            transform: 'scaleY(0.25)',
          },
          {
            delay,
            transform: 'scaleY(0.75)',
            duration: 0.1,
            ease: 'power2.inOut',
            yoyo: true,
            repeat: 1,
          }
        )
      },
      leave: (isPlaying: boolean) => {
        if (isPlaying) {
          timeline?.play()
          return
        }
      },
    }
  })

  return <div ref={barRef} className={s.bar} />
}

const bars = [
  {
    id: 1,
    delay: 0.6,
  },
  {
    id: 2,
    delay: 0.3,
  },
  {
    id: 3,
    delay: 0,
  },
  {
    id: 4,
    delay: 0.6,
  },
  {
    id: 5,
    delay: 0.3,
  },
]
