.primary,
.secondary {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  cursor: pointer;
  height: mobile-vw(50px);

  @include-media 'desktop' {
    height: desktop-vw(50px);
  }

  .border,
  .text,
  .canvas {
    grid-row-start: 1;
    grid-column-start: 1;
  }

  .canvas {
    margin-top: mobile-vw(1px);
    width: 100%;
    height: 100%;

    @include-media 'desktop' {
      margin-top: desktop-vw(1px);
    }
  }

  .text {
    position: relative;
    z-index: 1;
    align-self: center;
    justify-self: center;
  }
}

.primary {
  background-color: var(--green);
  --clip-cut-size: desktop-vw(8px);
  width: desktop-vw(130px);

  @include-media 'mobile' {
    width: mobile-vw(130px);

    --clip-cut-size: mobile-vw(12px);
  }

  clip-path: polygon(
    var(--clip-cut-size) 0,
    100% 0,
    100% calc(100% - var(--clip-cut-size)),
    calc(100% - var(--clip-cut-size)) 100%,
    0 100%,
    0 var(--clip-cut-size)
  );
}

.secondary {
  .canvas,
  .border {
    width: fit-content;
    height: mobile-vw(50px);

    @include-media 'desktop' {
      height: desktop-vw(50px);
    }
  }

  .canvas {
    width: 100%;
    aspect-ratio: none !important;
  }

  .border {
    position: relative;
    z-index: 3;
    width: 100%;
    --clip-cut-size: desktop-vw(8px);

    @include-media 'mobile' {
      --clip-cut-size: mobile-vw(8px);
    }

    .left,
    .right,
    .stroke {
      height: 100%;
    }

    .stroke {
      position: absolute;
      inset: 0;
      border: desktop-vw(1px) solid rgba(74, 74, 74, 0.25);
      background-color: rgba(0, 0, 0, 0.15);

      @include-media 'mobile' {
        border: mobile-vw(1px) solid var(--darkGrey);
      }
    }

    .left {
      position: absolute;
      left: 0;
    }

    .right {
      position: absolute;
      right: 0;
    }

    clip-path: polygon(
      var(--clip-cut-size) 0,
      100% 0,
      100% calc(100% - var(--clip-cut-size)),
      calc(100% - var(--clip-cut-size)) 100%,
      0 100%,
      0 var(--clip-cut-size)
    );
  }

  .text {
    padding-inline: desktop-vw(20px);

    @include-media 'mobile' {
      padding-inline: mobile-vw(12px);
      text-align: center;
    }
  }
}

.tertiary {
  position: relative;
  text-transform: uppercase;
  border-bottom: desktop-vw(1px) dashed rgba(255, 255, 255, 0.35);
  color: var(--green);
  display: grid;
  grid-template-columns: 1fr;
  width: fit-content;
  --duration: 0.5s;

  .text {
    text-align: center;
    transition: opacity var(--duration) var(--gleasing);
  }

  .overlay {
    height: calc(100% + desktop-vw(1px));
    padding-inline: desktop-vw(1px);
    background-color: var(--green);
    color: var(--black);
    clip-path: inset(0 100% 0 0);
  }

  &:hover {
    .text {
      opacity: 0.25;
    }
  }

  .text,
  .overlay {
    grid-column-start: 1;
    grid-row-start: 1;
    pointer-events: none;
    padding-block: desktop-vw(1px);
  }
}

.sound {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  cursor: pointer;
  pointer-events: all;

  /* @include-media 'mobile' {
    display: none;
  } */

  .border,
  .bars,
  .canvas {
    grid-row-start: 1;
    grid-column-start: 1;
  }

  .border {
    pointer-events: none;

    path {
      transition-property: stroke-dashoffset, stroke-dasharray;
      transition-duration: 500ms;
      transition-timing-function: var(--gleasing);
    }

    g:last-of-type {
      path:nth-of-type(1),
      path:nth-of-type(2) {
        stroke-dasharray: 40;
        stroke-dashoffset: 40;
      }

      path:nth-of-type(3),
      path:nth-of-type(4) {
        stroke-dasharray: 12;
        stroke-dashoffset: 12;
      }
    }
  }

  &.active {
    .border {
      g:last-of-type {
        path {
          stroke-dashoffset: 0;
        }
      }
    }
  }

  .canvas {
    padding: desktop-vw(1px);
    width: 100%;
    height: 100%;

    @include-media 'desktop' {
      width: desktop-vw(48px);
      height: desktop-vw(48px);
    }
  }

  .bars {
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: mobile-vw(4px);
    margin-top: -2px;

    @include-media "desktop" {
      column-gap: desktop-vw(4px);
    }

    .bar {
      width: mobile-vw(1px);
      height: mobile-vw(16px);
      background-color: var(--white);
      transform-origin: center;

      @include-media "desktop" {
        width: desktop-vw(1px);
        height: desktop-vw(16px);
      }

      @include-media "mobile" {
        &:nth-of-type(2n + 0) {
          display: none;
        }
      }
    }
  }
}
