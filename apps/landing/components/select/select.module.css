.wrapper {
  color: var(--text-color);

  .button {
    box-shadow: 0 1px 2px rgba(0 0 0 / 0.1);
    border-radius: 6px;
    font-size: 1.072rem;
    padding: 0.286rem 0.286rem 0.286rem 0.571rem;
    display: flex;
    align-items: center;
    max-width: 250px;

    &[data-focus-visible] {
      outline: 2px solid var(--focus-ring-color);
      outline-offset: -1px;
    }
  }

  .value {
    &[data-placeholder] {
      opacity: 0.8;
    }
  }

  span[aria-hidden] {
    width: 1.5rem;
    line-height: 1.375rem;
    margin-left: 1rem;
    padding: 1px;
    background: var(--highlight-background);
    color: var(--highlight-foreground);
    forced-color-adjust: none;
    border-radius: 4px;
    font-size: 0.857rem;
  }
}

.popover[data-trigger="Select"] {
  cursor: pointer;
  min-width: var(--trigger-width);

  .list {
    display: block;
    width: unset;
    max-height: inherit;
    min-height: unset;
    border: none;

    .react-aria-Header {
      padding-left: 1.571rem;
    }
  }

  .option {
    padding: 0.286rem 0.571rem 0.286rem 1.571rem;

    &[data-focus-visible] {
      outline: none;
    }

    &[data-selected] {
      position: relative;
      font-weight: 600;
      background: unset;
      color: var(--text-color);

      &::before {
        content: "✓";
        content: "✓" / "";
        alt: " ";
        position: absolute;
        top: 4px;
        left: 4px;
      }
    }

    &[data-focused],
    &[data-pressed] {
      background: var(--highlight-background);
      color: var(--highlight-foreground);
    }
  }
}
