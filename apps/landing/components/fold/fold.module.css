.fold {
  &:not(.isDisabled) {
    position: relative;

    &.isOverlay {
      .overlay {
        position: absolute;
        inset: 0;
        pointer-events: none;
        background-color: var(--theme-primary);
        opacity: var(--progress, 0);
      }
    }

    .sticky {
      min-height: 100svh;
      position: sticky;
    }

    &.isParallax {
      &.isBottom {
        .sticky {
          transform: translate3d(0, calc(-5svh * var(--progress, 0)), 0);
        }
      }

      &.isTop {
        .sticky {
          transform: translate3d(0, calc(5svh * var(--progress, 0)), 0);
        }
      }
    }

    &.isBottom {
      margin-bottom: -100svh;

      &::before {
        content: "";
        display: block;
        min-height: 100svh;
        visibility: hidden;
      }

      .sticky {
        bottom: 0;
      }
    }

    &.isTop {
      margin-top: -100svh;

      &::after {
        content: "";
        display: block;
        min-height: 100svh;
        visibility: hidden;
      }

      .sticky {
        top: 0;
      }
    }
  }
}
