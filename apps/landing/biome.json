{"$schema": "node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", "**/.next/**", "**/dist/**", "**/public/**", ".github/**", ".vercel/**", ".husky/**", "pnpm-lock.yaml", "bun.lockb", "**/*.md", "**/*.mdx", "**/public/*.js", "**/.storybook/**"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineEnding": "lf", "indentWidth": 2, "lineWidth": 80}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "warn"}, "correctness": {"useExhaustiveDependencies": "warn"}, "a11y": {"useKeyWithClickEvents": "warn", "useValidAnchor": "warn"}, "style": {"noNonNullAssertion": "off", "noUnusedTemplateLiteral": "off"}}}, "css": {"linter": {"enabled": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "parser": {"cssModules": true}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded", "trailingCommas": "es5"}}, "json": {"parser": {"allowComments": true}}, "overrides": [{"include": ["**/*.css"], "linter": {"rules": {"correctness": {"noUnknownFunction": "off"}}}}]}