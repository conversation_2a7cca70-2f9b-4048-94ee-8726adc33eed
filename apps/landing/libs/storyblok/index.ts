// https://github.com/storyblok/storyblok-js-client
'server only'

import { draftMode } from 'next/headers'
import StoryblokClient, {
  type ISbCustomFetch,
  type ISbStoriesParams,
} from 'storyblok-js-client'

interface StoryblokConfig {
  draft?: boolean
  cache?: {
    clear: 'auto' | 'manual'
    type: 'memory'
  }
}

class <PERSON>blokApi extends StoryblokClient {
  private draft: boolean
  private version: ISbStoriesParams['version']

  constructor({ draft = false, ...props }: StoryblokConfig = {}) {
    super({
      accessToken: draft
        ? process.env.STORYBLOK_PREVIEW_ACCESS_TOKEN
        : process.env.STORYBLOK_PUBLIC_ACCESS_TOKEN,
      region: 'us',
      ...props,
    })

    this.draft = draft
    this.version = draft ? 'draft' : 'published'
  }

  async get(
    path: string,
    params: ISbStoriesParams = {},
    options: ISbCustomFetch = {}
  ) {
    params.version = this.version

    if (this.draft) {
      options.cache = 'no-store'
    }
    return await super.get(path, params, options)
  }

  async getAll(
    slug: string,
    params: ISbStoriesParams = {},
    entity = 'stories',
    options: ISbCustomFetch = {}
  ) {
    params.version = this.version

    if (this.draft) {
      options.cache = 'no-store'
    }
    return await super.getAll(slug, params, entity, options)
  }

  async cacheFlush() {
    await super.flushCache()
    console.log('Storyblok Cache flushed')
  }
}

export { StoryblokApi }

export async function fetchStoryblokStory(
  slug: string,
  options: ISbStoriesParams
) {
  const _draftMode = await draftMode()
  const isDraftMode =
    _draftMode.isEnabled || process.env.NODE_ENV === 'development'

  try {
    const client = new StoryblokApi({
      draft: isDraftMode,
      cache: {
        clear: 'auto',
        type: 'memory',
      },
    })

    const storyblokStory = await client.get(slug, options)

    return storyblokStory
  } catch (error) {
    console.error('fetchStoryblokStory error', error)
    return { data: null, error }
  }
}

export async function fetchAll(slug = 'cdn/stories', options = {}) {
  const _draftMode = await draftMode()
  const isDraftMode =
    _draftMode.isEnabled || process.env.NODE_ENV === 'development'

  try {
    const client = new StoryblokApi({
      draft: isDraftMode,
      cache: {
        clear: 'auto',
        type: 'memory',
      },
    })

    const response = await client.getAll(slug, options)

    if (!response) {
      return { data: [], error: 'No data found' }
    }

    return { data: response }
  } catch (error) {
    console.error('fetchAll error', error)
    return { data: [], error }
  }
}

interface DatasourceEntry {
  id: number
  name: string
  value: string
  dimension_value?: string
}

export async function fetchDataSources(slug: string) {
  const storyblokClient = new StoryblokApi({
    draft: false,
  })

  let page = 1
  let allEntries: DatasourceEntry[] = []
  let hasMoreEntries = true

  // Fetch all pages
  while (hasMoreEntries) {
    const { data } = await storyblokClient.get(
      'cdn/datasource_entries/',
      {
        datasource: slug,
        cv: new Date().getTime(),
        page,
        per_page: 100, // Maximum allowed per page
      },
      {
        cache: 'no-cache',
      }
    )

    if (data?.datasource_entries?.length) {
      allEntries = [...allEntries, ...data.datasource_entries]
      page++
    } else {
      hasMoreEntries = false
    }
  }

  return allEntries
}

interface GraphQLResponse<T = []> {
  data?: T
  errors?: Array<{
    message: string
    locations?: Array<{ line: number; column: number }>
    path?: string[]
    [key: string]: unknown
  }>
}

export async function fetchStoryblokStoriesGraphQL<T = []>(
  query: string
): Promise<T | []> {
  const _draftMode = await draftMode()
  const isDraftMode =
    _draftMode.isEnabled || process.env.NODE_ENV === 'development'

  try {
    const response = await fetch('https://gapi-us.storyblok.com/v1/api', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Token: isDraftMode
          ? process.env.STORYBLOK_PREVIEW_ACCESS_TOKEN!
          : process.env.STORYBLOK_PUBLIC_ACCESS_TOKEN!,
        Version: isDraftMode ? 'draft' : 'published',
      },
      body: JSON.stringify({ query }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('Storyblok GraphQL Error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
      })
      return []
    }

    const { data, errors }: GraphQLResponse<T> = await response.json()

    if (errors) {
      console.error('Storyblok GraphQL Errors:', errors)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Storyblok GraphQL Request Failed:', error)
    return []
  }
}
