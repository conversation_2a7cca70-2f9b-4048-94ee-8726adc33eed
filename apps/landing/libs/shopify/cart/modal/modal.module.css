.modal {
  position: fixed;
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 2;

  &:not(.open) {
    pointer-events: none;

    .catch-click {
      opacity: 0;
    }

    .inner {
      transform: translateX(100%);
    }
  }
}

.catch-click {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: 500ms opacity var(--gleasing);
  background: rgba(0, 0, 0, 0.7);
}

.inner {
  position: relative;
  left: 25%;
  width: 75%;
  height: 100%;
  padding: mobile-vw(12px);
  background-color: var(--black);
  color: var(--red);
  display: flex;
  flex-direction: column;
  text-transform: uppercase;
  transition: 800ms transform var(--gleasing);

  @include-media ('desktop') {
    width: 50%;
    left: 50%;
    padding: desktop-vw(8px);
    padding-top: desktop-vw(56px);
  }

  .heading {
    border-bottom: 1px solid var(--red);
    padding-bottom: mobile-vw(16px);

    @include-media ('desktop') {
      padding-bottom: desktop-vw(16px);
    }
  }
}

.close {
  position: absolute;
  right: mobile-vw(12px);
  top: mobile-vw(12px);

  @include-media ('desktop') {
    position: absolute;
    right: desktop-vw(8px);
    top: desktop-vw(8px);
  }

  &.active {
    background: var(--white);
  }
}

.lines {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: auto;
  overscroll-behavior: contain;

  @include-media ('desktop') {
    max-height: desktop-vw(600px);
  }

  .line {
    display: grid;
    grid-template-columns: repeat(6, minmax(0, 1fr));
    grid-gap: var(--layout-columns-gap);
    padding: mobile-vw(16px) 0;
    border-bottom: 1px solid var(--red);
    grid-template-areas:
      "media media info info . remove"
      "media media quantity quantity price price";

    @include-media ('desktop') {
      grid-template-areas:
        "media media info info . remove"
        "media media quantity . .  price";

      padding: desktop-vw(16px) 0;
    }

    .media {
      position: relative;
      grid-area: media;
      width: 100%;
      height: mobile-vw(150px);

      img {
        height: 100%;
        object-fit: contain !important;
      }

      @include-media ('desktop') {
        height: desktop-vw(250px);
      }
    }

    .info {
      grid-area: info;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      @include-media ('desktop') {
        padding-left: desktop-vw(8px);
      }

      .details {
        display: flex;
        flex-direction: column;

        @include-media ('mobile') {
          font-size: mobile-vw(10px);
        }

        .title {
          margin-bottom: desktop-vw(8px);
          font-size: mobile-vw(18px);

          @include-media ('desktop') {
            font-size: desktop-vw(24px);
          }
        }
      }
    }

    .remove {
      grid-area: remove;
      justify-self: flex-end;

      button {
        &.disable {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }

    .quantity {
      grid-area: quantity;
      display: flex;
      height: fit-content;
      background-color: var(--red);
      color: var(--black);
      border-radius: mobile-vw(100px);
      padding: mobile-vw(8px) mobile-vw(16px);

      @include-media ('mobile') {
        font-size: mobile-vw(12px);
        align-self: center;
      }

      @include-media ('desktop') {
        align-self: flex-end;
        border-radius: desktop-vw(100px);
        padding: desktop-vw(12px) desktop-vw(16px);
      }

      button {
        text-align: center;

        &.disable {
          pointer-events: none;
          opacity: 0.5;
        }

        @include hover {
          &:hover {
            color: var(--white);
          }
        }
      }

      span {
        padding: 0 mobile-vw(8px);

        @include-media ('desktop') {
          padding: 0 desktop-vw(16px);
        }
      }
    }

    .price {
      grid-area: price;
      height: fit-content;
      align-self: center;

      @include-media ('desktop') {
        align-self: flex-end;
      }
    }
  }
}

.checkout {
  position: relative;
  width: 100%;
  align-self: flex-end;

  &::before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: desktop-vw(-50px);
    background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 1) 55%,
      rgba(12, 0, 36, 0) 100%
    );
    pointer-events: none;

    @include-media ('mobile') {
      top: mobile-vw(-50px);
    }
  }

  .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    margin-bottom: desktop-vw(16px);
  }

  .action {
    position: relative;
    display: block;
    width: 100%;
    padding: desktop-vw(25px) 0;
    text-align: center;
    border: 2px solid red;
    border-radius: desktop-vw(8px);

    span {
      position: relative;
    }

    &::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      background-color: var(--red);
      clip-path: circle(0%);
    }

    @include hover {
      &:hover {
        color: var(--black);

        &::before {
          transition: 1000ms clip-path var(--gleasing);
          clip-path: circle(100%);
        }
      }
    }
  }
}
