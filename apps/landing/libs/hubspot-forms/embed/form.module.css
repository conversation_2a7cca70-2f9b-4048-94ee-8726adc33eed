.hubspot-form {
  --gray: rgba(0, 0, 0, 0.25);
  padding: 0 mobile-vw(24px);

  @include-media ('desktop') {
    padding: 0 desktop-vw(24px);
  }

  > div:first-child {
    font-family: var(--font-mono);
    text-align: center;
    font-size: mobile-vw(25px);

    @include-media ('desktop') {
      font-size: desktop-vw(25px);
    }
  }

  :global(.hs-email),
  :global(.hs-phone),
  :global(.hs-fieldtype-number),
  :global(.hs-fieldtype-text) {
    @extend .input-field;
  }

  :global(.hs-fieldtype-date) {
    @extend .input-field;

    > div:last-of-type {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: mobile-vw(10px);
        width: 5px;
        height: 5px;
        border-left: mobile-vw(6px) solid transparent;
        border-right: mobile-vw(6px) solid transparent;
        border-top: mobile-vw(6px) solid var(--black);

        @include-media ('desktop') {
          right: desktop-vw(15px);
          border-left: desktop-vw(6px) solid transparent;
          border-right: desktop-vw(6px) solid transparent;
          border-top: desktop-vw(6px) solid var(--black);
        }
      }
    }
  }

  :global(.hs-fieldtype-textarea) {
    width: 100%;
    border-radius: mobile-vw(8px);
    @extend .field;
    min-height: mobile-vw(100px);

    @include-media ('desktop') {
      border-radius: desktop-vw(8px);
      min-height: desktop-vw(100px);
    }

    textarea {
      border: 2px solid var(--black);
      padding: mobile-vw(10px);
      margin-bottom: 0;

      @include-media ('desktop') {
        padding: desktop-vw(6px) desktop-vw(16px);
      }
    }
  }

  :global(.hs-fieldtype-select) {
    @extend .input-field;

    select {
      width: 100% !important;
    }

    > div:last-of-type {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: mobile-vw(10px);
        width: 5px;
        height: 5px;
        border-left: mobile-vw(6px) solid transparent;
        border-right: mobile-vw(6px) solid transparent;
        border-top: mobile-vw(6px) solid var(--black);

        @include-media ('desktop') {
          right: desktop-vw(15px);
          border-left: desktop-vw(6px) solid transparent;
          border-right: desktop-vw(6px) solid transparent;
          border-top: desktop-vw(6px) solid var(--black);
        }
      }
    }
  }

  :global(.hs-fieldtype-checkbox) {
    width: 100%;

    div > ul {
      color: var(--black);
      display: flex;
      flex-direction: column;
      row-gap: mobile-vw(10px);

      @include-media ('desktop') {
        row-gap: desktop-vw(10px);
      }

      > li {
        position: relative;

        @include hover {
          &:hover {
            input {
              background-color: var(--green);
              color: var(--white);
            }
          }
        }

        > label {
          @extend .check-labels;

          input {
            position: relative;
            width: mobile-vw(30px);
            aspect-ratio: 1 / 1;
            border: 2px solid var(--gray);
            border-radius: mobile-vw(4px);
            overflow: hidden;
            flex-shrink: 0;
            transition: 500ms background-color var(--gleasing), 500ms color
              var(--gleasing);

            @include-media ('desktop') {
              width: desktop-vw(30px);
              border-radius: desktop-vw(4px);
            }

            &::after {
              content: "✓";
              position: absolute;
              top: -5%;
              left: 12%;
              width: 100%;
              height: 100%;
              font-size: mobile-vw(20px);

              @include-media ('desktop') {
                font-size: desktop-vw(20px);
                top: -5%;
                left: 12%;
              }
            }
          }
        }
      }

      input:checked {
        background-color: var(--green);
        color: var(--white);
      }
    }
  }

  :global(.hs-fieldtype-radio) {
    width: 100%;

    div > ul {
      color: var(--black);
      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: mobile-vw(10px);

      @include-media ('desktop') {
        column-gap: desktop-vw(10px);
      }

      > li {
        position: relative;

        @include hover {
          &:hover {
            input {
              &::after {
                opacity: 1;
              }
            }
          }
        }

        > label {
          @extend .check-labels;

          @include-media ('desktop') {
            font-size: desktop-vw(14px);
          }

          input {
            position: relative;
            width: mobile-vw(40px);
            aspect-ratio: 1 / 1;
            background-color: var(--white);
            border: 2px solid var(--gray);
            border-radius: 100%;
            overflow: hidden;
            flex-shrink: 0;

            @include-media ('desktop') {
              width: desktop-vw(40px);
            }

            &::after {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border-radius: 100%;
              background-color: var(--green);
              transform: scale(0.75);
              opacity: 0;
              transition: 500ms opacity var(--gleasing);
            }
          }
        }
      }

      input:checked {
        &::after {
          opacity: 1;
        }
      }
    }
  }

  :global(.hs-fieldtype-booleancheckbox) {
    width: 100%;

    div > ul {
      color: var(--black);

      > li {
        position: relative;

        @include hover {
          &:hover {
            input {
              &::after {
                opacity: 1;
              }
            }
          }
        }

        > label {
          @extend .check-labels;

          input {
            position: relative;
            width: mobile-vw(74px);
            aspect-ratio: 2 / 1;
            border: 2px solid var(--gray);
            overflow: hidden;
            flex-shrink: 0;
            border-radius: mobile-vw(32px);

            @include-media ('desktop') {
              width: desktop-vw(104px);
              border-radius: desktop-vw(32px);
            }

            &::after {
              content: "";
              position: absolute;
              top: 50%;
              transform: translate(15%, -50%);
              left: 0;
              aspect-ratio: 1 / 1;
              border-radius: 100%;
              background-color: var(--gray);
              width: mobile-vw(24px);
              transition: 500ms transform var(--gleasing);

              @include-media ('desktop') {
                width: desktop-vw(36px);
              }
            }
          }
        }
      }

      input:checked {
        background-color: var(--green);

        &::after {
          background-color: var(--white);
          transform: translate(175%, -50%);

          @include-media ('desktop') {
            transform: translate(160%, -50%);
          }
        }
      }
    }
  }

  :global(.hs_error_rollup) {
    margin-bottom: desktop-vw(15px) !important;
    color: red !important;

    @include-media ('desktop') {
      text-align: center;
      font-size: desktop-vw(16px);
    }
  }

  :global(.hs-form-field) {
    @extend .field;
    margin-bottom: mobile-vw(45px);

    @include-media ('desktop') {
      margin-bottom: desktop-vw(25px);
    }

    > label {
      @extend .label-field;
    }

    legend {
      display: none !important;
    }

    &:has([role="alert"]) {
      @extend .errors;
    }
  }

  .custom-form {
    display: flex;
    flex-direction: column;
    max-width: 100%;
  }

  .field {
    position: relative;
    width: 100%;
    font-family: var(--font-mono);
    line-height: 150%;
    color: var(--black) !important;
  }

  .input-field {
    input,
    select {
      width: 100% !important;
      border: 2px solid var(--black);
      padding: mobile-vw(15px) mobile-vw(21px);
      border-radius: mobile-vw(8px);

      @include-media ('desktop') {
        font-size: desktop-vw(14px);
        padding: desktop-vw(10px) desktop-vw(16px);
        border-radius: desktop-vw(8px);
      }
    }
  }

  .label-field {
    position: relative;
    color: var(--black);
    top: mobile-vw(-5px);

    @include-media ('desktop') {
      font-size: desktop-vw(14px);
      top: desktop-vw(-5px);
    }
  }
}

.check-labels {
  display: flex;
  align-items: center;
  position: relative;
  line-height: normal;
  font-size: mobile-vw(14px);

  @include-media ('desktop') {
    font-size: desktop-vw(14px);
  }

  span {
    margin-left: mobile-vw(12px);

    @include-media ('desktop') {
      margin-left: desktop-vw(12px);
    }
  }
}

.errors {
  input {
    border: 2px solid red;
  }

  > ul {
    position: absolute;
    bottom: mobile-vw(-20px);
    left: 0;
    width: 100%;
    color: red;

    @include-media ('desktop') {
      bottom: desktop-vw(-25px);
    }

    li {
      display: flex;
      align-items: flex-end;
      height: desktop-vw(20px);
    }

    label {
      position: relative;
      font-size: mobile-vw(10px) !important;

      @include-media ('desktop') {
        font-size: desktop-vw(10px) !important;
      }
    }
  }
}

.submit {
  cursor: pointer;
  display: flex;
  justify-content: center;
  height: fit-content;
  margin: 0 auto;
  background-color: var(--black);
  color: var(--white) !important;
  border: none !important;
  text-transform: uppercase;
  font-weight: 600;
  font-family: var(--font-mono);
  border-radius: mobile-vw(10px);
  width: mobile-vw(233px);
  padding: mobile-vw(26px) mobile-vw(36px);

  @include-media ('desktop') {
    width: desktop-vw(233px);
    padding: desktop-vw(26px) desktop-vw(36px);
    border-radius: desktop-vw(10px);
    font-size: desktop-vw(16px);
  }
}
