.minimap {
  --width: 400px;
  --height: calc(var(--width) / var(--viewport-ratio));

  position: fixed;
  z-index: 9999;
  top: 50%;
  right: 50px;
  width: var(--width);
  height: var(--height);
  transform: translateY(-50%);

  &::after {
    content: "";
    position: absolute;
    inset: -6px;
    border: 2px solid rgba(0, 0, 0, 0.5);
    border-radius: 6px;
  }

  &::before {
    content: "";
    width: 100%;
    aspect-ratio: var(--body-ratio);
    transform: translateY(
      calc((var(--progress) * -100%) + (var(--progress) * var(--height)))
    );
    position: absolute;
    top: 0;
    background-color: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(4px);
    border-radius: 4px;
  }

  .markers {
    position: absolute;
    top: 0;
    width: 100%;
    aspect-ratio: var(--body-ratio);
    z-index: 1;

    .marker {
      top: 0;
      position: absolute;
      width: 100%;
      height: 2px;
      transform: translateY(-50%) translateY(calc(var(--top) * var(--height)));
    }
  }
}
