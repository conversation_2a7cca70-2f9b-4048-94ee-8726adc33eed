'use client'

import { OrthographicCamera } from '@react-three/drei'
import { Canvas } from '@react-three/fiber'
import { Suspense } from 'react'
import { useOrchestra } from '~/libs/orchestra/react'
import { SheetProvider } from '~/libs/theatre'
import { PostProcessing } from '../postprocessing'
import { RAF } from '../raf'
import { useCanvas } from './'
import s from './webgl.module.css'

type WebGLCanvasProps = React.HTMLAttributes<HTMLDivElement> & {
  render?: boolean
  postprocessing?: boolean
  orbitControls?: boolean
}

// Object3D.DEFAULT_MATRIX_AUTO_UPDATE = false

export function WebGLCanvas({
  render = true,
  postprocessing = false,
  orbitControls = false,
  ...props
}: WebGLCanvasProps) {
  const { WebGLTunnel, DOMTunnel } = useCanvas()

  // const isStudio = useStudio()
  const { webgl } = useOrchestra()

  if (!webgl) return null

  return (
    <div
      className={s.webgl}
      id="webgl"
      // style={{
      //   zIndex: isStudio ? 1 : 0,
      // }}
      {...props}
    >
      <Canvas
        gl={{
          precision: 'highp',
          powerPreference: 'high-performance',
          antialias: false,
          alpha: true,
          logarithmicDepthBuffer: true,
          depth: false,
          stencil: false,
          // ...(postprocessing && { stencil: false, depth: false }),
        }}
        dpr={[1, 2]}
        camera={{ position: [0, 0, 5000], near: 0.001, far: 10000, zoom: 1 }}
        frameloop="never"
        onCreated={({ gl }) => {
          gl.debug.checkShaderErrors = process.env.NODE_ENV === 'development'
        }}
        // linear
        // flat
        // eventSource={document.querySelector('main')}
        // eventPrefix="client"
        // resize={{ scroll: true, debounce: { scroll: 0, resize: 500 } }}
        // camera={{
        //   fov: 35,
        //   near: 0.1,
        //   far: 250,
        // }}
        orthographic
        // shadows
      >
        {/* <StateListener onChange={onChange} /> */}
        <SheetProvider id="webgl">
          <OrthographicCamera
            makeDefault
            position={[0, 0, 5000]}
            near={0.001}
            far={10000}
            zoom={1}
          />
          <RAF render={render} />
          {/* <FlowmapProvider> */}
            {postprocessing && (
              <SheetProvider id="postprocessing">
                <PostProcessing />
              </SheetProvider>
            )}
            <Suspense>
              <WebGLTunnel.Out />
            </Suspense>
            {/* {orbitControls && (
              <MapControls
                // domElement={document.querySelector('main')}
                makeDefault
              />
            )} */}
          {/* </FlowmapProvider> */}
        </SheetProvider>
      </Canvas>
      <DOMTunnel.Out />
    </div>
  )
}
