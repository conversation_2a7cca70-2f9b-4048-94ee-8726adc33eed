import { type ThreeElements, createPortal, useThree } from '@react-three/fiber'
import { EffectComposer, EffectPass, RenderPass } from 'postprocessing'
import {
  type ForwardRefExoticComponent,
  type PropsWithoutRef,
  type ReactNode,
  type RefAttributes,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react'
import * as THREE from 'three'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useBloom } from '../postprocessing/use-bloom'
import { ChapterContext, useChapterFrame } from '../postprocessing/use-chapters'
import { useTone } from '../postprocessing/use-tone-effect'
import { Preload } from '../preload'
type ForwardRefComponent<P, T> = ForwardRefExoticComponent<
  PropsWithoutRef<P> & RefAttributes<T>
>

export type RenderTextureProps = Omit<
  ThreeElements['texture'],
  'ref' | 'args'
> & {
  /** Optional width of the texture, defaults to viewport bounds */
  width?: number
  /** Optional height of the texture, defaults to viewport bounds */
  height?: number
  /** Optional fbo samples */
  samples?: number
  /** Optional stencil buffer, defaults to false */
  stencilBuffer?: boolean
  /** Optional depth buffer, defaults to true */
  depthBuffer?: boolean
  /** Optional generate mipmaps, defaults to false */
  generateMipmaps?: boolean
  /** Optional render priority, defaults to 0 */
  renderPriority?: number
  /** Optional event priority, defaults to 0 */
  eventPriority?: number
  /** Optional frame count, defaults to Infinity. If you set it to 1, it would only render a single frame, etc */
  frames?: number
  /** Optional event compute, defaults to undefined */
  compute?: (event: any, state: any, previous: any) => false | undefined
  /** Optional render, defaults to true */
  render?: boolean
  /** Children will be rendered into a portal */
  children: ReactNode
}

export const RenderTexture: ForwardRefComponent<
  RenderTextureProps,
  // { fbo: THREE.WebGLRenderTarget; fbo2: THREE.WebGLRenderTarget }
  THREE.Texture
> = /* @__PURE__ */ forwardRef(
  (
    {
      children,
      compute,
      width,
      height,
      samples = 8,
      renderPriority = 0,
      eventPriority = 0,
      frames = Number.POSITIVE_INFINITY,
      stencilBuffer = false,
      depthBuffer = false,
      generateMipmaps = false,
      ...props
    },
    forwardRef
  ) => {
    const gl = useThree((state) => state.gl)

    const composer = useMemo(
      () =>
        new EffectComposer(gl, {
          // multisampling: isWebgl2 && needsAA ? maxSamples : 0,
          multisampling: 0,
          frameBufferType: THREE.HalfFloatType,
        }),
      [gl]
    )
    composer.autoRenderToScreen = false

    const [vScene] = useState(() => new THREE.Scene())

    const { getRender } = useContext(ChapterContext)

    const studio = useStudio()

    const uvCompute = useCallback(
      (event, state, previous) => {
        // Since this is only a texture it does not have an easy way to obtain the parent, which we
        // need to transform event coordinates to local coordinates. We use r3f internals to find the
        // next Object3D.
        //   let parent = (fbo.texture as any)?.__r3f.parent?.object
        //   while (parent && !(parent instanceof THREE.Object3D)) {
        //     parent = parent.__r3f.parent?.object
        //   }
        //   if (!parent) return false
        //   // First we call the previous state-onion-layers compute, this is what makes it possible to nest portals
        //   if (!previous.raycaster.camera)
        //     previous.events.compute(
        //       event,
        //       previous,
        //       previous.previousRoot?.getState()
        //     )
        //   // We run a quick check against the parent, if it isn't hit there's no need to raycast at all
        //   const [intersection] = previous.raycaster.intersectObject(parent)
        //   console.log('intersection', intersection, parent)
        //   if (!intersection) return false
        //   // We take that hits uv coords, set up this layers raycaster, et voilà, we have raycasting on arbitrary surfaces
        //   const uv = intersection.uv
        //   if (!uv) return false

        //   state.raycaster.setFromCamera(
        //     state.pointer.set(uv.x * 2 - 1, uv.y * 2 - 1),
        //     state.camera
        //   )

        // console.log('uvCompute', getRender())

        if (!studio) return false

        if (!getRender()) return false

        state.raycaster.setFromCamera(
          state.pointer.set(previous.mouse.x, previous.mouse.y),
          state.camera
        )
      },
      [studio, getRender]
    )

    // useImperativeHandle(forwardRef, () => ({ fbo, fbo2 }), [fbo, fbo2])
    useImperativeHandle(forwardRef, () => composer.outputBuffer.texture, [
      composer,
    ])

    return (
      <>
        {createPortal(
          <Container composer={composer}>
            {children}
            <Preload />
            {/* Without an element that receives pointer events state.pointer will always be 0/0 */}
            <group onPointerOver={() => null} />
          </Container>,
          vScene,
          { events: { compute: compute || uvCompute, priority: eventPriority } }
        )}
        {/* <primitive object={target.texture} {...props} /> */}
      </>
    )
  }
)

// The container component has to be separate, it can not be inlined because "useFrame(state" when run inside createPortal will return
// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function
// it would return the default state.
function Container({
  composer,
  children,
}: {
  composer: EffectComposer
  children: ReactNode
}) {
  // const { getRender, index } = useContext(ChapterContext)

  const scene = useThree((state) => state.scene)
  const camera = useThree((state) => state.camera)
  const size = useThree((state) => state.size)
  const renderPass = useMemo(
    () => new RenderPass(scene, camera),
    [scene, camera]
  )

  const bloomEffect = useBloom()
  const toneEffect = useTone()

  const effectPass = useMemo(
    () => new EffectPass(camera, bloomEffect, toneEffect),
    [camera, bloomEffect, toneEffect]
  )

  useEffect(() => {
    composer.addPass(renderPass)
    composer.addPass(effectPass)

    return () => {
      composer.removePass(renderPass)
      composer.removePass(effectPass)
    }
  }, [composer, renderPass, effectPass])

  useEffect(() => {
    composer.setSize(size.width, size.height)
  }, [composer, size])

  // const isEven = index % 2 === 0

  // const frameCountRef = useRef(0)

  // useFrame((_, deltaTime) => {
  //   const render = getRender()

  //   const list = useChaptersStore.getState().list
  //   const renderingLength = Object.values(list).filter((item) =>
  //     item?.getRender()
  //   ).length

  //   if (render) {
  //     if (
  //       renderingLength <= 1 ||
  //       frameCountRef.current % 2 === (isEven ? 0 : 1)
  //     ) {
  //
  //     }
  //   }

  //   frameCountRef.current++
  // }, 1)

  useChapterFrame((_, deltaTime) => {
    composer.render(deltaTime)
  }, 1)

  return children
}
