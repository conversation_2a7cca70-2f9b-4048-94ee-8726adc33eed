'use client'

import { useRect, useWindowSize } from 'hamo'
import dynamic from 'next/dynamic'
import { type RefObject, useState } from 'react'
import { Image as MSDFImg } from '~/components/image'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import type { ChokeWebGLProps } from './webgl'

const ChokeWebGL = dynamic(
  () => import('./webgl').then(({ ChokeWebGL }) => ChokeWebGL),
  {
    ssr: false,
  }
)

type ChokeProps = Omit<ChokeWebGLProps, 'ref'> & {
  className?: string
  msdf: string
}

export function Choke({
  className,
  msdf,
  ...props
}: Omit<ChokeProps, 'ref'> & {
  ref?: RefObject<{ update: (progress: number) => void }>
  verticalOffset?: number
  scale: { x: number; y: number }
  theaterConfig: string
  delay?: number
}) {
  const [src, setSrc] = useState<string>()
  const [setRectRef, rect] = useRect()
  const { width: windowWidth, height: windowHeight } = useWindowSize()

  return (
    <div className={className}>
      <WebGLTunnel>
        <ChokeWebGL
          rect={rect}
          src={src}
          aspectRatio={(windowWidth ?? 1) / (windowHeight ?? 1)}
          {...props}
        />
      </WebGLTunnel>
      <div ref={setRectRef} style={{ opacity: 0 }}>
        <MSDFImg
          src={msdf}
          onLoad={(img) => {
            setSrc(img.currentTarget.currentSrc)
          }}
          fill
        />
      </div>
    </div>
  )
}
