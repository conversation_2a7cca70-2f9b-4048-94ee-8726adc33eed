import { DoubleSide, MeshBasicMaterial, Vector3 } from 'three'
import { NOISE } from '~/libs/webgl/utils/noise'

export class Material extends MeshBasicMaterial {
  constructor({ ...params } = {}) {
    super({
      ...params,
      transparent: true,
      side: DoubleSide,
    })

    this.uniforms = {
      uTexture: { value: null },
      uCursor: { value: new Vector3() },
      uTime: { value: 0 },
      uProgress: { value: 0 },
      uSharpness: { value: 7 },
      uDelay: { value: 0 },
      uScreenAspectRatio: { value: 1 },
      uNoiseStrength: { value: 1 },
      uNoiseFrequency: { value: 15 },
      uNoiseAmplitude: { value: 0.2 },
    }

    this.defines = {
      USE_UV: '',
    }

    this.speed = 0.0005
  }

  onBeforeCompile = (shader) => {
    console.log(`WebGL: compiling: ${this.constructor.name}`)
    shader.uniforms = {
      ...shader.uniforms,
      ...this.uniforms,
    }

    shader.defines = {
      ...shader.defines,
      ...this.defines,
    }

    shader.vertexShader = shader.vertexShader.replace(
      'void main() {',
      /*glsl*/ `
      varying vec4 vWorldPosition;

      void main() {
        vWorldPosition = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.0);
      `
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      /* glsl */ `void main() {`,
      /* glsl */ `

      varying vec4 vWorldPosition;

      uniform sampler2D uTexture;
      uniform vec3 uCursor;
      uniform float uTime;
      uniform float uProgress;
      uniform float uSharpness;
      uniform float uDelay;
      uniform float uScreenAspectRatio;
      uniform float uNoiseStrength;
      uniform float uNoiseFrequency;
      uniform float uNoiseAmplitude;

      ${NOISE.FBM_3D(5)}

      float distanceToCursor(float radius, vec2 aspectRatio){
        vec2 dist = abs(vec2(uCursor.x, 1. - uCursor.y) - vUv) * aspectRatio;
        float boxDist = max(dist.x, dist.y * .025);
        return clamp(1.0 - boxDist * 1. / radius, 0.0, 1.0);  
      }

      float median(float r, float g, float b) {
        return max(min(r, g), min(max(r, g), b));
    }

    float edgeAlpha(float offset){
      vec3 msd = texture(uTexture, vUv).rgb;
      float sd = median(msd.r, msd.g, msd.b);
      float screenPxDistance = uSharpness * (sd - offset);
 
      return clamp(screenPxDistance, 0., 1.);
    }

    float euclideanDistanceToCursor(float radius, float aspectRatio){
      vec2 dist = vec2(aspectRatio * (uCursor.x - vWorldPosition.x),  uCursor.y - vWorldPosition.y);
      return clamp(1.0 - length(dist)  * 1. / radius, 0.0, 1.0);  
    }

    float remap(float x, float a, float b) {
      return a + (b - a) * ((1.0 - x) / 0.5);
    }

      void main() {`
    )

    shader.fragmentShader = shader.fragmentShader.replace(
      /* glsl */ `vec4 diffuseColor = vec4( diffuse, opacity );`,
      /* glsl */ `vec4 diffuseColor = vec4( diffuse, opacity );

      // Map progress from 1. to 0.5.
      float progress = (1. - uProgress) * 0.5 + 0.5;

      float noise = fbm(vec3(vUv * uNoiseFrequency, uNoiseAmplitude * uTime));
      // Smooth noise in the edges of the domain.
      noise *= uNoiseStrength * sin(uProgress * 3.14159);

      float delayShift = vUv.x * uDelay;
      float delayedProgress = remap(progress, delayShift, 0.5);

      // Ensure offset boundaries are respected.
      float offset = max(0.5, delayedProgress + noise);
      offset = pow(offset, uProgress);

      diffuseColor.a = edgeAlpha(offset);
      `
    )
  }

  raf(time) {
    this.uniforms.uTime.value = time * this.speed
  }

  setTexture(texture) {
    this.uniforms.uTexture.value = texture
  }

  setProgress(value) {
    this.uniforms.uProgress.value = value
  }

  setSharpness(value) {
    this.uniforms.uSharpness.value = value
  }

  setDelay(value) {
    this.uniforms.uDelay.value = value
  }

  setScreenAspectRatio(value) {
    this.uniforms.uScreenAspectRatio.value = value
  }

  setTime(value) {
    this.uniforms.uTime.value = value
  }

  setNoiseStrength(value) {
    this.uniforms.uNoiseStrength.value = value
  }

  setNoiseFrequency(value) {
    this.uniforms.uNoiseFrequency.value = value
  }

  setNoiseAmplitude(value) {
    this.uniforms.uNoiseAmplitude.value = value
  }

  set cursor(value) {
    this.uniforms.uCursor.value = value
  }

  get cursor() {
    return this.uniforms.uCursor.value
  }
}
