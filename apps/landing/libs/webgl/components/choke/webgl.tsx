import { useTexture } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import { types } from '@theatre/core'
import { type RefObject, useCallback, useEffect, useRef, useState } from 'react'
import { LinearFilter, type Mesh } from 'three'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useWebGLRect } from '~/libs/webgl/hooks/use-webgl-rect'
import { Material } from './material'

export type ChokeWebGLRef = {
  material: Material
  sequence: {
    duration: number
    play: (onComplete?: () => void) => void
    getPosition: () => number
    setPosition: (value: number) => void
  }
}

export type ChokeWebGLProps = {
  src: string | undefined
  rect: DOMRect
  ref: RefObject<ChokeWebGLRef | null>
  verticalOffset?: number
  scale: { x: number; y: number }
  delay?: number
  aspectRatio: number
  theaterConfig: {
    sheet: string
    object: string
  }
}

export function ChokeWebGL({
  src,
  rect,
  scale = { x: 1, y: 1 },
  verticalOffset = 0,
  aspectRatio,
  theaterConfig,
}: ChokeWebGLProps) {
  const meshRef = useRef<Mesh>(null!)
  const [material] = useState(() => new Material())

  if (!src) return null

  useTexture(src, (texture) => {
    texture.magFilter = texture.minFilter = LinearFilter
    texture.generateMipmaps = false

    material.setTexture(texture)
    material.needsUpdate = true
  })

  const getRect = useWebGLRect(
    rect,
    ({
      position: rectPosition,
      scale: rectScale,
    }: {
      position: { x: number; y: number; z: number }
      scale: { x: number; y: number; z: number }
    }) => {
      meshRef.current.position.set(
        rectPosition.x,
        verticalOffset - rectScale.y * 0.5,
        rectPosition.z
      )

      meshRef.current.updateMatrix()
    }
  )

  const { scale: rectScale } = getRect()

  const scaleMesh = useCallback(
    (value: number) => {
      const from = [rectScale.x * scale.x, rectScale.y * scale.y, 1]

      meshRef.current.scale.set(from[0] * value, from[1] * value, 1)
      meshRef.current.updateMatrix()
    },
    [rectScale, scale]
  )

  useEffect(() => {
    meshRef.current.scale.set(rectScale.x * scale.x, rectScale.y * scale.y, 1)
  }, [rectScale])

  useEffect(() => {
    material.setScreenAspectRatio(aspectRatio)
  }, [aspectRatio])

  useFrame(({ clock }) => {
    material.setTime(clock.elapsedTime)
  })

  const { sheet, object } = theaterConfig
  const introSheet = useSheet(sheet)

  useTheatre(
    introSheet,
    object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
      sharpness: types.number(7, { range: [2, 20], nudgeMultiplier: 0.5 }),
      scaleFrom: types.number(0.9, { range: [0, 1], nudgeMultiplier: 0.01 }),
      delay: types.number(5, { range: [0, 10], nudgeMultiplier: 0.01 }),
      noiseStrength: types.number(2, { range: [0, 10], nudgeMultiplier: 0.1 }),
      noiseFrequency: types.number(15, { range: [0, 50], nudgeMultiplier: 1 }),
      noiseAmplitude: types.number(0.2, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({
        progress,
        sharpness,
        scaleFrom,
        noiseStrength,
        noiseFrequency,
        noiseAmplitude,
        delay,
      }: {
        progress: number
        sharpness: number
        scaleFrom: number
        noiseStrength: number
        noiseFrequency: number
        noiseAmplitude: number
        delay: number
      }) => {
        material?.setProgress(progress)
        material?.setSharpness(sharpness)
        material?.setNoiseStrength(noiseStrength)
        material?.setNoiseFrequency(noiseFrequency)
        material?.setNoiseAmplitude(noiseAmplitude)
        material?.setDelay(delay)
        scaleMesh(scaleFrom)
      },
    }
  )

  return (
    <mesh ref={meshRef} matrixAutoUpdate={false}>
      <planeGeometry />
      <primitive object={material} />
    </mesh>
  )
}
