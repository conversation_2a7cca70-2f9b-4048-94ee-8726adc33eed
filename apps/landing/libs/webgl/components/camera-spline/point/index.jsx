import { useCursor } from '@react-three/drei'
import { useFrame, useThree } from '@react-three/fiber'
import { types } from '@theatre/core'
import { useEffect, useRef, useState } from 'react'
import {
  useStudio,
  useStudioCurrentObject,
} from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useControlsStore } from '../controls'

export function Point({
  sheet,
  theatreKey = 'point',
  onChange,
  isFirst,
  isLast,
  defaultValue = [0, 0, 0],
}) {
  const ref = useRef()
  const setObject = useControlsStore((state) => state.setObject)
  const object = useControlsStore((state) => state.object)
  const setNextMode = useControlsStore((state) => state.setNextMode)

  const isCurrentObject = object === ref.current?.id

  const [hovered, setHovered] = useState(false)
  useCursor(hovered)

  const { set, object: objectTheatre } = useTheatre(
    sheet,
    theatreKey,
    {
      position: {
        x: types.number(defaultValue[0], {
          nudgeMultiplier: 0.01,
        }),
        y: types.number(defaultValue[1], {
          nudgeMultiplier: 0.01,
        }),
        z: types.number(defaultValue[2], {
          nudgeMultiplier: 0.01,
        }),
      },
      scale: {
        x: types.number(1, {
          nudgeMultiplier: 0.01,
        }),
        y: types.number(1, {
          nudgeMultiplier: 0.01,
        }),
        z: types.number(1, {
          nudgeMultiplier: 0.01,
        }),
      },
      rotation: {
        x: types.number(0, {
          nudgeMultiplier: 0.01,
        }),
        y: types.number(0, {
          nudgeMultiplier: 0.01,
        }),

        z: types.number(0, {
          nudgeMultiplier: 0.01,
        }),
      },
    },
    {
      onValuesChange: ({ position, scale, rotation }) => {
        if (ref.current) {
          ref.current.position.set(position.x, position.y, position.z)
          ref.current.scale.set(scale.x, scale.y, scale.z)
          ref.current.rotation.set(rotation.x, rotation.y, rotation.z)
          ref.current.updateMatrix()
        }

        onChange?.([position.x, position.y, position.z])
      },
    }
  )

  const camera = useThree((state) => state.camera)

  useFrame(() => {
    if (ref.current) {
      const distanceToCamera = ref.current.position.distanceTo(camera.position)
      ref.current.scale.setScalar(Math.min(distanceToCamera * 0.02, 1))
    }
  })

  const studio = useStudio()
  const currentObject = useStudioCurrentObject()

  useEffect(() => {
    if (
      currentObject?.objectKey === theatreKey &&
      sheet.address.sheetId === currentObject?.sheetId
    ) {
      setObject(ref.current?.id)
    }
  }, [currentObject, setObject, theatreKey, sheet])

  useEffect(() => {
    if (object === ref.current?.id) {
      studio?.setSelection([objectTheatre])
    }
  }, [object, studio, objectTheatre])

  if (!studio) return null

  return (
    <>
      {/* biome-ignore lint/a11y/useKeyWithClickEvents: we need to trigger on click */}
      <mesh
        ref={ref}
        onClick={(e) => {
          e.stopPropagation()
          setObject(e.object.id)
        }}
        onPointerMissed={(e) => e.type === 'click' && setObject(null)}
        onPointerOver={(e) => {
          e.stopPropagation()
          setHovered(true)
        }}
        onPointerOut={() => setHovered(false)}
        onContextMenu={(e) => {
          if (isCurrentObject) {
            setNextMode()
          }
        }}
        onTransformControlsMove={(e) => {
          if (!ref.current) return

          onChange?.([
            ref.current.position.x,
            ref.current.position.y,
            ref.current.position.z,
          ])
        }}
        onTransformControlsRelease={(e) => {
          if (!ref.current) return

          set({
            position: {
              x: ref.current.position.x,
              y: ref.current.position.y,
              z: ref.current.position.z,
            },
            scale: {
              x: ref.current.scale.x,
              y: ref.current.scale.y,
              z: ref.current.scale.z,
            },
            rotation: {
              x: ref.current.rotation.x,
              y: ref.current.rotation.y,
              z: ref.current.rotation.z,
            },
          })
        }}
      >
        <boxGeometry />
        <meshBasicMaterial
          transparent
          opacity={0.75}
          depthWrite={false}
          color={isFirst ? 'green' : isLast ? 'red' : 'yellow'}
          fog={false}
        />
      </mesh>
    </>
  )
}
