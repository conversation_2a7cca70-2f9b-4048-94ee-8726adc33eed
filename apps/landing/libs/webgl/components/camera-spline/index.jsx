'use client'

import { MapControls, PerspectiveCamera } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { types } from '@theatre/core'
import gsap from 'gsap'
import { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { Object3D, Vector3 } from 'three'
import { IntroContext } from '~/app/(pages)/home/<USER>/intro'
import { useCurrentSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useChapterFrame } from '~/libs/webgl/components/postprocessing/use-chapters'
import { Spline } from './spline'

const proxyVector = new Vector3()
const originObject = new Object3D()
const orbitObject = new Object3D()

function computeShericalKinesis(
  originPosition,
  orbitPosition,
  [x = 0, y = 0, z = 0],
  distance = 0
) {
  originObject.add(orbitObject)

  const direction = orbitPosition.clone().sub(originPosition)
  const target = originPosition
    .clone()
    .add(direction.clone().multiplyScalar(distance))

  orbitObject.position.copy(orbitPosition).sub(target)
  originObject.rotation.set(x, y, z)
  orbitObject.updateWorldMatrix()
  return orbitObject.getWorldPosition(proxyVector).add(target)
}

export function CameraSpline({ origin = [0, 0, 0] }) {
  const sheet = useCurrentSheet()

  // const [splinePosition, setSplinePosition] = useState()
  // const [splineTarget, setSplineTarget] = useState()

  const [introSplinePositionEnd, setIntroSplinePositionEnd] = useState()
  const [introSplineTargetEnd, setIntroSplineTargetEnd] = useState()

  const controls = useThree((state) => state.controls)
  const camera = useThree((state) => state.camera)

  const targetRef = useRef(new Vector3().fromArray(origin))
  const positionRef = useRef(new Vector3().fromArray(origin))

  const groupRef = useRef()

  const { get } = useTheatre(
    sheet,
    'camera / debug',
    {
      controls: types.boolean(true),
      debug: types.boolean(false),
    },
    {
      onValuesChange: ({ controls: _controls, debug }) => {
        if (controls) {
          controls.enabled = _controls

          groupRef.current.visible = _controls || debug
        }

        camera.rotation.set(0, 0, 0)
      },
      deps: [controls],
    }
  )

  const { sheet: introSheet, end } = useContext(IntroContext)

  const cameraConfig = {
    fov: types.number(35, {
      range: [1, 100],
    }),
    near: types.number(0.001, {
      range: [0.001, 100],
    }),
    far: types.number(500, {
      range: [0.001, 10000],
    }),
  }

  const onCameraValuesChange = useCallback(
    ({ controls: _controls, fov, near, far }) => {
      camera.zoom = 1
      camera.fov = fov
      camera.near = near
      camera.far = far

      camera.updateProjectionMatrix()
    },
    [camera]
  )

  useTheatre(introSheet, 'camera', cameraConfig, {
    onValuesChange: onCameraValuesChange,
  })

  useTheatre(sheet, 'camera', cameraConfig, {
    onValuesChange: onCameraValuesChange,
  })

  const isStudio = useStudio()

  const size = useThree((state) => state.size)

  const cursorRef = useRef({ x: 0, y: 0, intensity: 0.001, distance: 0 })

  useEffect(() => {
    const abortController = new AbortController()

    window.addEventListener(
      'mousemove',
      (e) => {
        const x = (e.clientX / size.width) * 2 - 1
        const y = (e.clientY / size.height) * 2 - 1

        gsap.to(cursorRef.current, {
          x,
          y,
          ease: 'expo.out',
          duration: 4,
        })

        // const spherical = new Spherical(
        //   positionRef.current.distanceTo(targetRef.current)
        // )
        // spherical.phi = y * 0.01
        // spherical.theta = x * 0.01

        // camera.position.setFromSpherical(spherical)
        // console.log(position)
        // camera.position.copy(position)

        // camera.position.set(x, y, 0)
      },
      {
        signal: abortController.signal,
      }
    )

    return () => {
      abortController.abort()
    }
  }, [camera])

  useChapterFrame(() => {
    if (isStudio && get().controls) return

    const position = computeShericalKinesis(
      targetRef.current,
      positionRef.current,
      [
        cursorRef.current.y * -cursorRef.current.intensity,
        cursorRef.current.x * cursorRef.current.intensity,
        0,
      ],
      1 - cursorRef.current.distance
    )
    // console.log(positionRef.current, position)

    // const offset = position.clone().sub(positionRef.current)

    camera.position.copy(position)
    camera.lookAt(targetRef.current)
    if (controls) {
      controls.target.copy(targetRef.current)
    }
  })

  const kinesisConfig = {
    intensity: types.number(0.01, {
      range: [0, 1],
      nudgeMultiplier: 0.01,
    }),
    distance: types.number(1, {
      range: [0, 1],
      nudgeMultiplier: 0.01,
    }),
  }

  const onKinesisValuesChange = useCallback(({ intensity, distance }) => {
    cursorRef.current.intensity = intensity
    cursorRef.current.distance = distance
  }, [])

  useTheatre(sheet, 'camera / kinesis', kinesisConfig, {
    onValuesChange: onKinesisValuesChange,
  })

  useTheatre(introSheet, 'camera / kinesis', kinesisConfig, {
    onValuesChange: onKinesisValuesChange,
  })

  return (
    <>
      {/* <group ref={cameraRef}> */}
      <PerspectiveCamera
        fov={70}
        near={0.001}
        far={500}
        makeDefault
        position={[0, 0, 0]}
      />
      {/* </group> */}
      {isStudio && (
        <MapControls
          makeDefault
          position={origin.map((v) => v + 5)}
          position0={origin.map((v) => v + 5)}
          target={origin}
        />
      )}

      <group
        visible={Boolean(isStudio)}
        matrixAutoUpdate={false}
        userData={{ debug: true }}
      >
        <group ref={groupRef} matrixAutoUpdate={false}>
          <Spline
            sheet={sheet}
            theatreKey="camera / spline / position"
            end={end}
            ref={(node) => {
              // if (node?.catmullRomLine) {
              //   setSplinePosition(node.catmullRomLine)
              // }
              // if (node?.theatreObject) {
              //   setSplinePositionTheatreObject(node.theatreObject)
              // }
            }}
            onProgress={(progress, catmullRomLine) => {
              if (!catmullRomLine) return

              // if (!introEnabled) {
              catmullRomLine.getPointAt(progress, positionRef.current)
              // }
            }}
            onJoinUpdate={(position) => {
              setIntroSplinePositionEnd(position)
            }}
            defaultValues={[
              [0, 0, 0],
              [1, 0, 1],
            ]}
          />
          <Spline
            sheet={sheet}
            theatreKey="camera / spline / target"
            end={end}
            ref={(node) => {
              // if (node?.catmullRomLine) {
              //   setSplineTarget(node.catmullRomLine)
              // }
              // if (node?.theatreObject) {
              //   setSplineTargetTheatreObject(node.theatreObject)
              // }
            }}
            onProgress={(progress, catmullRomLine) => {
              if (!catmullRomLine) return

              // if (!introEnabled) {
              catmullRomLine.getPointAt(progress, targetRef.current)
              // }
            }}
            onJoinUpdate={(position) => {
              setIntroSplineTargetEnd(position)
            }}
            defaultValues={[
              [1, 0, 0],
              [2, 0, 0],
            ]}
          />

          {introSheet && (
            <>
              <Spline
                sheet={introSheet}
                theatreKey="camera / spline / position"
                color="yellow"
                endPoint={introSplinePositionEnd}
                // join={splinePosition}
                // joinSheet={sheet}
                ref={(node) => {
                  // if (node?.catmullRomLine) {
                  //   setIntroSplinePosition(node.catmullRomLine)
                  // }
                }}
                onProgress={(progress, catmullRomLine) => {
                  if (!catmullRomLine) return

                  // if (introEnabled) {
                  catmullRomLine.getPointAt(progress, positionRef.current)
                  // }
                }}
                defaultValues={[
                  [61, 139, -14],
                  [87, 129, -20],
                ]}
              />
              <Spline
                sheet={introSheet}
                theatreKey="camera / spline / target"
                color="yellow"
                endPoint={introSplineTargetEnd}
                // join={splineTarget}
                // joinSheet={sheet}
                ref={(node) => {
                  // console.log(splineTarget.getPointAt(0.15))
                  // if (node?.catmullRomLine) {
                  //   setIntroSplineTarget(node.catmullRomLine)
                  // }
                }}
                onProgress={(progress, catmullRomLine) => {
                  if (!catmullRomLine) return

                  // if (introEnabled) {
                  catmullRomLine.getPointAt(progress, targetRef.current)
                  // }
                }}
                defaultValues={[
                  [52, 5.5, -8],
                  [72, 7, -19],
                ]}
              />
            </>
          )}
        </group>
      </group>
    </>
  )
}
