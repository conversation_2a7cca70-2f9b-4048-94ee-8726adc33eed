import { CatmullRomLine } from '@react-three/drei'
import { useFrame, useThree } from '@react-three/fiber'
import { types, val } from '@theatre/core'
import {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { CatmullRomCurve3, Vector3 } from 'three'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { Point } from '../point'

export function Spline({
  sheet,
  theatreKey = 'spline',
  onProgress,
  onJoinUpdate,
  ref,
  defaultValues = [],
  color = 'white',
  endPoint,
  end,
}) {
  const progressMeshRef = useRef()

  const [count, setCount] = useState(2)
  const [points, setPoints] = useState(defaultValues)

  const catmullRomLine = useMemo(() => {
    if (points?.length > 1) {
      const pt = [...points.map((point) => new Vector3(...point))]

      if (endPoint) {
        pt.push(endPoint)
      }

      return new CatmullRomCurve3(pt)
    }

    return null
  }, [points, endPoint])

  const progressRef = useRef(0)

  const onProgressRef = useRef(onProgress)
  onProgressRef.current = onProgress

  useEffect(() => {
    onProgressRef.current?.(progressRef.current, catmullRomLine)
  }, [catmullRomLine])

  const { object: theatreObject } = useTheatre(
    sheet,
    theatreKey,
    {
      count: types.number(2, {
        nudgeMultiplier: 1,
        range: [1, 10],
      }),
      progress: types.number(0, {
        nudgeMultiplier: 0.01,
        range: [0, 1],
      }),
    },
    {
      onValuesChange: ({ count, progress, visible }) => {
        setCount(count)
        progressRef.current = progress
        // groupRef.current.visible = visible

        onProgressRef.current?.(progress, catmullRomLine)

        if (!catmullRomLine) return

        const point = catmullRomLine.getPointAt(progressRef.current)
        progressMeshRef.current.position.set(point.x, point.y, point.z)
        progressMeshRef.current.updateMatrix()
      },
      deps: [catmullRomLine],
    }
  )

  useImperativeHandle(
    ref,
    () => ({
      catmullRomLine,
      theatreObject,
    }),
    [catmullRomLine, theatreObject]
  )

  const onJoinUpdateRef = useRef(onJoinUpdate)
  onJoinUpdateRef.current = onJoinUpdate

  useEffect(() => {
    if (!catmullRomLine || !theatreObject || !sheet || typeof end !== 'number')
      return

    const oldPosition = sheet.sequence.position

    const length = val(sheet.sequence.pointer.length)

    sheet.sequence.position = length * end

    const position = catmullRomLine.getPointAt(theatreObject.value.progress)

    sheet.sequence.position = oldPosition

    onJoinUpdateRef.current?.(position)
  }, [sheet, catmullRomLine, end])

  useEffect(() => {
    if (!catmullRomLine) return
    const point = catmullRomLine.getPointAt(progressRef.current)
    progressMeshRef.current.position.set(point.x, point.y, point.z)
    progressMeshRef.current.updateMatrix()
  }, [catmullRomLine])

  useEffect(() => {
    setPoints((prev) => {
      const newPoints = [...prev]
      if (count < newPoints.length) {
        newPoints.length = count
      }
      return newPoints
    })
  }, [count])

  const camera = useThree((state) => state.camera)

  useFrame(() => {
    const distanceToCamera = progressMeshRef.current.position.distanceTo(
      camera.position
    )
    progressMeshRef.current.scale.setScalar(
      Math.min(distanceToCamera * 0.02, 1) + 0.01
    )
  })

  return (
    <>
      <mesh ref={progressMeshRef}>
        <boxGeometry />
        <meshBasicMaterial
          color="blue"
          transparent
          opacity={0.75}
          fog={false}
        />
      </mesh>
      {catmullRomLine && (
        <CatmullRomLine points={catmullRomLine.points} color={color} />
      )}
      {Array.from({ length: count }).map((_, index) => (
        <Point
          sheet={sheet}
          // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
          key={index}
          isFirst={index === 0}
          isLast={index === count - 1}
          theatreKey={`${theatreKey} / points / point-${index}`}
          onChange={(point) => {
            setPoints((prev) => {
              const newPoints = [...prev]
              newPoints.length = count
              newPoints[index] = point
              return newPoints
            })
          }}
          defaultValue={defaultValues[index]}
        />
      ))}
    </>
  )
}
