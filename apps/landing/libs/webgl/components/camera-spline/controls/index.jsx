import { TransformControls } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { create } from 'zustand'

export const useControlsStore = create((set, get) => ({
  mode: 'translate',
  setNextMode: () => {
    const mode = get().mode

    const currentMode = MODES.indexOf(mode)
    const nextMode = (currentMode + 1) % MODES.length
    set({ mode: MODES[nextMode] })
  },
  object: null,
  setObject: (object) => set({ object }),
}))

const MODES = ['translate', 'rotate', 'scale']

export function Controls({ children, enabled = true }) {
  const mode = useControlsStore((state) => state.mode)
  const objectId = useControlsStore((state) => state.object)
  const scene = useThree((state) => state.scene)

  // const object = scene.getObjectById(objectId)

  // const [mode, setMode] = useState('translate')
  // const [objectId, setObjectId] = useState(null)
  // const [scene, setScene] = useState(null)

  const object = scene.getObjectById(objectId)

  return (
    <>
      {enabled && object && (
        <TransformControls
          // makeDefault
          mode={mode}
          //   onChange={setMode}
          object={object}
          // onChange={(e) => {
          //   console.log('onChange', e)
          // }}
          // onMouseUp={(e) => {
          //   console.log('onMouseUp', e)
          // }}
          onObjectChange={() => {
            object.onTransformControlsMove?.()
          }}
          onMouseUp={() => {
            object.onTransformControlsRelease?.()
            // controls.enabled = true
          }}
        />
      )}
      {children}
    </>
  )
}
