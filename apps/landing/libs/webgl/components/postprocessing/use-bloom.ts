import { types } from '@theatre/core'
import { BloomEffect } from 'postprocessing'
import { useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export function useBloom() {
  const [effect] = useState(
    () =>
      new BloomEffect({
        mipmapBlur: true,
      })
  )

  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'postprocessing / bloom',
    {
      intensity: types.number(1, {
        range: [0, 10],
        nudgeMultiplier: 0.01,
      }),
      radius: types.number(0.9, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      opacity: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ intensity, radius, opacity }) => {
        effect.intensity = intensity
        effect.mipmapBlurPass.radius = radius
        effect.blendMode.setOpacity(opacity)
      },
    }
  )

  useTheatre(
    sheet,
    'postprocessing / bloom / luminance',
    {
      filter: types.boolean(true),
      threshold: types.number(0.5, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      smoothing: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ filter, threshold, smoothing }) => {
        effect.luminancePass.enabled = filter
        effect.luminanceMaterial.threshold = threshold
        effect.luminanceMaterial.smoothing = smoothing
      },
    }
  )

  return effect
}
