import { type RootState, use<PERSON>rame, useThree } from '@react-three/fiber'
import { types } from '@theatre/core'
import type { Rect } from 'hamo'
import { useLenis } from 'lenis/react'
import { BlendFunction, Effect } from 'postprocessing'
import { createContext, useContext, useRef, useState } from 'react'
import type { Texture, WebGLRenderer } from 'three'
import {
  Color,
  LinearFilter,
  MirroredRepeatWrapping,
  RepeatWrapping,
  Uniform,
} from 'three'
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js'
import { create } from 'zustand'
import { modulo } from '~/libs/maths'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { BLEND } from '../../utils/blend'
import { NOISE } from '../../utils/noise'

const ktx2Loader = new KTX2Loader()
ktx2Loader.setTranscoderPath(
  'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/basis/'
)

export const ChapterContext = createContext({
  getRender: () => true,
  getProgress: () => 0,
  index: 0,
})

export const useChaptersStore = create<{
  list: {
    texture: Texture
    rect: Rect
    getRender: () => boolean
    getProgress: () => number
  }[]
  set: (
    index: number,
    {
      texture,
      rect,
      getRender,
      getProgress,
    }: {
      texture: Texture
      rect: Rect
      getRender: () => boolean
      getProgress: () => number
    }
  ) => void
  index: number
}>((set) => ({
  list: [],
  index: 0,
  set: (
    index: number,
    {
      texture,
      rect,
      getRender,
      getProgress,
    }: {
      texture: Texture
      rect: Rect
      getRender: () => boolean
      getProgress: () => number
    }
  ) => {
    set((state) => {
      const newList = [...state.list]
      if (texture) {
        texture.wrapS = MirroredRepeatWrapping
        texture.wrapT = MirroredRepeatWrapping
        texture.minFilter = LinearFilter
        texture.magFilter = LinearFilter
        texture.needsUpdate = true
      }

      newList[index] = { texture, rect, getRender, getProgress }

      return {
        list: newList,
      }
    })
  },
}))

const fragmentShader = /* glsl */ `

uniform float uProgress;
uniform sampler2D uTextureA;
uniform sampler2D uTextureB;
uniform sampler2D uVoronoiTexture;
uniform float uBlur;
uniform float uUvOffset;
uniform float uSeparatorOffset;
uniform float uDebug;
uniform vec3 uColor;
uniform float uFrequency;
uniform float uThickness;
uniform float uNoiseSpeed;
uniform float uNoiseFrequency;
uniform float uNoiseAmplitude;

uniform sampler2D uDepthTextureA;
uniform sampler2D uDepthTextureB;



vec4 blur13(sampler2D image, vec2 uv, vec2 resolution, vec2 direction) {
  vec4 color = vec4(0.0);
  vec2 off1 = vec2(1.411764705882353) * direction;
  vec2 off2 = vec2(3.2941176470588234) * direction;
  vec2 off3 = vec2(5.176470588235294) * direction;
  color += texture2D(image, uv) * 0.1964825501511404;
  color += texture2D(image, uv + (off1 / resolution)) * 0.2969069646728344;
  color += texture2D(image, uv - (off1 / resolution)) * 0.2969069646728344;
  color += texture2D(image, uv + (off2 / resolution)) * 0.09447039785044732;
  color += texture2D(image, uv - (off2 / resolution)) * 0.09447039785044732;
  color += texture2D(image, uv + (off3 / resolution)) * 0.010381362401148057;
  color += texture2D(image, uv - (off3 / resolution)) * 0.010381362401148057;
  return color;
}

${BLEND.NORMAL}
${NOISE.SIMPLEX_3D}

void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {


  float progress = uProgress - (uv.x - 0.5) * 0.33;

  float aspect = resolution.x / resolution.y;

  vec4 voronoi = texture2D(uVoronoiTexture, uv * uFrequency * vec2(aspect, 1.));

  float cells = voronoi.r;
  float borders = voronoi.g;
  float cellsDistance = voronoi.b;
  float hardBorders = smoothstep(0.9, 1., borders);
  


  float noise = simplex_3d(vec3(uv * uNoiseFrequency, time * uNoiseSpeed));
  progress += noise * uNoiseAmplitude;
 
  

  float fadeProgress = uv.y - progress + 1.;
  float fadeProgress2 = uv.y - progress + 1. + uThickness;

  
  
  

  fadeProgress -= (((borders - 0.5) * 1.) + ((hardBorders - 0.5) * 0.) + ((cellsDistance - 0.5) * 0.))  * uSeparatorOffset;
  // fadeProgress += noise * 0.1;
  fadeProgress2 -= (((borders - 0.5) * 1.) + ((hardBorders - 0.5) * 0.) + ((cellsDistance - 0.5) * 0.))  * uSeparatorOffset;
  // fadeProgress2 += noise* 0.1;

  float distanceToProgress = distance(progress, uv.y) * 4.;
  // distanceToProgress -= ((borders - 0.5) + hardBorders * 0.)  * uSeparatorOffset;
  distanceToProgress = smoothstep(0., 1., distanceToProgress);
  distanceToProgress = 1. - distanceToProgress;
  distanceToProgress = clamp(distanceToProgress, 0., 1.);
  // distanceToProgress += noise* 0.1;




  
  
  
  
  // fadeProgress = 1. - fadeProgress;

  
  // fadeProgress = clamp(fadeProgress,0.0, 1.0);
  fadeProgress = smoothstep(0.99, 1., fadeProgress);
  fadeProgress = 1. - fadeProgress;
  fadeProgress = clamp(fadeProgress,0.0, 1.0);

  fadeProgress2 = smoothstep(0.99, 1., fadeProgress2);
  fadeProgress2 = 1. - fadeProgress2;
  fadeProgress2 = clamp(fadeProgress2,0.0, 1.0);


  

  vec2 uvDisplaced = uv + vec2(0, (borders) + (hardBorders * 10.))  * 0.25 * (1. -fadeProgress) * distanceToProgress * uUvOffset;

    vec4 colorA = blur13(uTextureA, uvDisplaced, resolution, (hardBorders + (borders - 0.5)) * vec2(0,- (1. - fadeProgress) * distanceToProgress * uBlur));
    vec4 colorB = blur13(uTextureB, uvDisplaced, resolution, (hardBorders + (borders - 0.5)) * vec2(0,(1. - fadeProgress) * distanceToProgress * uBlur));

    // vec4 colorA = texture(uTextureA, uv);
    // vec4 colorB = texture(uTextureB, uv);

    // outputColor = colorA;

    // if (progress > uv.y) {
    //   outputColor = colorB;
    // }


    
    float overlayNoise = simplex_3d(vec3(uv, time * 0.1));
    float overlayFade = sin(((uv.x * 2.) - (time * 0.2)) * 3.1415);

    // overlayFade = clamp(overlayFade, 0.0, 1.0);

    outputColor = mix(colorA,colorB,fadeProgress);
    outputColor.rgb = blendNormal(outputColor.rgb, uColor, clamp((fadeProgress - fadeProgress2) * (1. - overlayFade), 0.0, 1.0) );

    // outputColor.rgb = vec3(overlayFade);

    // outputColor = mix(colorA, colorB, clamp(progress-uv.y, 0.0, 1.0));

    if(uDebug == 1.0) {
      outputColor.g = distanceToProgress;
      outputColor.b = fadeProgress;
      if(uProgress > uv.y-0.001) {
        if(uProgress < uv.y+0.001) {
          outputColor.r = 1.0;
        }
      }
    }
}

`

export class ChaptersEffect extends Effect {
  constructor(gl: WebGLRenderer) {
    super('ChaptersEffect', fragmentShader, {
      blendFunction: BlendFunction.NORMAL,
      uniforms: new Map([
        ['uProgress', new Uniform(0)],
        ['uTextureA', new Uniform(null)],
        ['uTextureB', new Uniform(null)],
        ['uVoronoiTexture', new Uniform(null)],
        ['uBlur', new Uniform(10)],
        ['uUvOffset', new Uniform(0.05)],
        ['uSeparatorOffset', new Uniform(0.25)],
        ['uDebug', new Uniform(0)],
        ['uUiTexture', new Uniform(null)],
        ['uColor', new Uniform(new Color('#ACFF46').convertLinearToSRGB())],
        ['uFrequency', new Uniform(1)],
        ['uThickness', new Uniform(0.02)],
        ['uDepthTextureA', new Uniform(null)],
        ['uDepthTextureB', new Uniform(null)],
        ['uNoiseSpeed', new Uniform(0.1)],
        ['uNoiseFrequency', new Uniform(1)],
        ['uNoiseAmplitude', new Uniform(0.1)],
      ]),
    })

    ktx2Loader.detectSupport(gl)

    ktx2Loader.load('/textures/noise/voronoi_ibi.ktx2', (texture) => {
      texture.wrapS = RepeatWrapping
      texture.wrapT = RepeatWrapping
      texture.minFilter = LinearFilter
      texture.magFilter = LinearFilter
      this.uniforms.get('uVoronoiTexture')!.value = texture
    })

    // new TextureLoader().load('/textures/noise/voronoi_ibi.png', (texture) => {
    //   texture.wrapS = RepeatWrapping
    //   texture.wrapT = RepeatWrapping
    //   texture.minFilter = LinearFilter
    //   texture.magFilter = LinearFilter
    //   this.uniforms.get('uVoronoiTexture')!.value = texture
    // })
  }
}

export function useChaptersEffect() {
  const gl = useThree((state) => state.gl)
  const [effect] = useState(() => new ChaptersEffect(gl))

  const list = useChaptersStore((state) => state.list)

  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'chapters',
    {
      progress: types.number(0, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      frequency: types.number(1.5, {
        range: [0, 10],
        nudgeMultiplier: 0.1,
      }),
      thickness: types.number(0.02, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      blur: types.number(10, {
        range: [0, 100],
        nudgeMultiplier: 1,
      }),
      uvOffset: types.number(5, {
        range: [0, 100],
        nudgeMultiplier: 0.1,
      }),
      separatorHeight: types.number(0.25, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      noiseSpeed: types.number(0.1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      noiseFrequency: types.number(1, {
        range: [0, 100],
        nudgeMultiplier: 1,
      }),
      noiseAmplitude: types.number(0.1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      debug: false,
    },
    {
      onValuesChange: ({
        progress,
        blur,
        uvOffset,
        separatorHeight,
        debug,
        frequency,
        thickness,
        noiseSpeed,
        noiseFrequency,
        noiseAmplitude,
      }) => {
        effect.uniforms.get('uProgress')!.value = progress
        effect.uniforms.get('uBlur')!.value = blur
        effect.uniforms.get('uUvOffset')!.value = uvOffset * 0.01
        effect.uniforms.get('uSeparatorOffset')!.value = separatorHeight
        effect.uniforms.get('uDebug')!.value = debug ? 1 : 0
        effect.uniforms.get('uFrequency')!.value = frequency
        effect.uniforms.get('uThickness')!.value = thickness
        effect.uniforms.get('uNoiseSpeed')!.value = noiseSpeed
        effect.uniforms.get('uNoiseFrequency')!.value = noiseFrequency
        effect.uniforms.get('uNoiseAmplitude')!.value = noiseAmplitude
      },
    }
  )

  const size = useThree((state) => state.size)

  useLenis(
    ({ scroll, animatedScroll, limit }) => {
      scroll = modulo(Math.floor(animatedScroll - size.height / 2), limit)

      const currentIndex = list.findIndex((item) => {
        if (!item) return false

        return (
          scroll <=
          (item?.rect?.top ?? 0) + (item?.rect?.height ?? 0) + size.height / 2
        )
      })

      const currentItem = list[currentIndex]

      // console.log(currentIndex)

      if (!currentItem) {
        return
      }

      const progress =
        ((currentItem?.rect?.top ?? 0) +
          (currentItem?.rect?.height ?? 0) -
          scroll) /
          size.height -
        0.5

      effect.uniforms.get('uProgress')!.value = 1 - progress
      effect.uniforms.get('uTextureA')!.value = list[currentIndex]?.texture
      effect.uniforms.get('uTextureB')!.value =
        list[(currentIndex + 1) % list.length]?.texture
    },
    [list, size]
  )

  return effect
}

export function useChapterFrame(
  callback: (_: RootState, deltaTime: number) => void,
  priority = 0
) {
  const { getRender, index } = useContext(ChapterContext)

  const isEven = index % 2 === 0

  const frameCountRef = useRef(0)

  useFrame((_, deltaTime) => {
    const render = getRender()

    const list = useChaptersStore.getState().list
    const renderingLength = Object.values(list).filter((item) =>
      item?.getRender()
    ).length

    if (render) {
      if (
        renderingLength <= 1 ||
        frameCountRef.current % 2 === (isEven ? 0 : 1)
      ) {
        callback?.(_, deltaTime)
      }
    }

    frameCountRef.current++
  }, priority)
}
