import { types } from '@theatre/core'
import { BlendFunction } from 'postprocessing'
import { useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

import { Effect } from 'postprocessing'
import { Uniform } from 'three'

const fragmentShader = /* glsl */ `
void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {

  vec2 _uv = floor(uv * resolution) / resolution;

	float r = rand(_uv);
  float g = rand(_uv + 1.0);
  float b = rand(_uv - 1.0);

	outputColor = vec4(vec3(r, g, b), inputColor.a);
}

`

export class NoiseEffect extends Effect {
  constructor({ blendFunction = BlendFunction.NORMAL } = {}) {
    super('NoiseEffect', fragmentShader, {
      blendFunction,
      uniforms: new Map([['intensity', new Uniform(0.2)]]),
    })
  }
}

export function useNoiseEffect() {
  const [effect] = useState(
    () =>
      new NoiseEffect({
        blendFunction: BlendFunction.ADD,
      })
  )

  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'noise',
    {
      opacity: types.number(0.03, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      blendFunction: types.stringLiteral(
        'COLOR_DODGE',
        Object.fromEntries(Object.keys(BlendFunction).map((v) => [v, v]))
      ),
    },
    {
      onValuesChange: ({ opacity, blendFunction }) => {
        effect.blendMode.setOpacity(opacity)
        effect.blendMode.setBlendFunction(BlendFunction[blendFunction])
      },
    }
  )

  return effect
}
