import { types } from '@theatre/core'
import { BlendFunction, Effect } from 'postprocessing'
import { useState } from 'react'
import { Color, Uniform } from 'three'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { BLEND } from '../../utils/blend'

const fragmentShader = /* glsl */ `

uniform float uCircle;
uniform float uRectangle;
uniform vec3 uColor;

${BLEND.NORMAL}


void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {
  float aspect = resolution.x / resolution.y;
  float circleMask = 1. - length(((uv - 0.5) / vec2(1.0, aspect )));
  circleMask = smoothstep(uCircle, 1.0, circleMask);
  circleMask = clamp(circleMask, 0., 1.);

  vec2 st = uv.xy;

  // https://thebookofshaders.com/07/?lan=fr
  // bottom-left
  vec2 bl = smoothstep(vec2(0.),vec2(uRectangle),st);
  float pct = bl.x * bl.y;

  // top-right
  vec2 tr = smoothstep(vec2(0.),vec2(uRectangle),1.0-st);
  pct *= tr.x * tr.y;
  float rectangleMask = pct;
  rectangleMask = clamp(rectangleMask, 0., 1.);

  outputColor = vec4(inputColor.rgb, inputColor.a);
  // outputColor.rgb = circleMask - random(uv) * 0.05;
  
  outputColor.rgb = blendNormal(outputColor.rgb, uColor, (1. - circleMask));
  outputColor.rgb = blendNormal(outputColor.rgb, uColor, (1. - rectangleMask));
}

`

export class VignetteEffect extends Effect {
  constructor() {
    super('VignetteEffect', fragmentShader, {
      blendFunction: BlendFunction.NORMAL,
      uniforms: new Map([
        ['uCircle', new Uniform(0.2)],
        ['uRectangle', new Uniform(0.2)],
        ['uColor', new Uniform(new Color('#231F20').convertLinearToSRGB())],
      ]),
    })
  }

  get circle() {
    return this.uniforms.get('uCircle').value
  }

  set circle(value) {
    this.uniforms.get('uCircle').value = value
  }

  get rectangle() {
    return this.uniforms.get('uRectangle').value
  }

  set rectangle(value) {
    this.uniforms.get('uRectangle').value = value
  }

  get color() {
    return this.uniforms.get('uColor').value
  }

  set color(value) {
    this.uniforms.get('uColor').value.set(value)
  }
}

export function useVignette() {
  const [effect] = useState(() => new VignetteEffect())

  const sheet = useCurrentSheet()
  useTheatre(
    sheet,
    'vignette',
    {
      circle: types.number(0.2, {
        range: [0, 1],
      }),
      rectangle: types.number(0.2, {
        range: [0, 1],
      }),
      color: types.rgba({
        r: 0,
        g: 0,
        b: 0,
        a: 1,
      }),
      opacity: types.number(0.9, {
        range: [0, 1],
      }),
    },
    {
      onValuesChange: ({ circle, rectangle, color, opacity }) => {
        effect.circle = circle
        effect.rectangle = rectangle
        effect.color = color.toString()
        effect.blendMode.setOpacity(opacity)
      },
    }
  )

  return effect
}
