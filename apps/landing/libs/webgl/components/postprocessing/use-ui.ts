import { types } from '@theatre/core'
import { BlendFunction, TextureEffect } from 'postprocessing'
import { useState } from 'react'
import type { Texture } from 'three'
import { useOrchestra } from '~/libs/orchestra/react'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export function useUi(texture: Texture) {
  const [effect] = useState(
    () =>
      new TextureEffect({
        texture,
        blendFunction: BlendFunction.ALPHA,
      })
  )

  const sheet = useCurrentSheet()

  const { cinematic } = useOrchestra()

  useTheatre(
    sheet,
    'ui',
    {
      opacity: types.number(1, {
        range: [0, 1],
        nudgeMultiplier: 0.01,
      }),
      blendFunction: types.stringLiteral(
        'ALPHA',
        Object.fromEntries(Object.keys(BlendFunction).map((v) => [v, v]))
      ),
    },
    {
      onValuesChange: ({ opacity, blendFunction }) => {
        effect.blendMode.setOpacity(cinematic ? 0 : opacity)
        effect.blendMode.setBlendFunction(BlendFunction[blendFunction])
      },
      deps: [cinematic],
    }
  )

  return effect
}
