import { types } from '@theatre/core'
import { BlendFunction, Effect } from 'postprocessing'
import { useState } from 'react'
import { Uniform } from 'three'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

const fragmentShader = /* glsl */ `

vec2 barrelDistortion(vec2 coord, float amt) {
	vec2 cc = coord - 0.5;
	float dist = dot(cc, cc);
	return coord + cc * dist * amt;
}

float sat( float t )
{
	return clamp( t, 0.0, 1.0 );
}

float linterp( float t ) {
	return sat( 1.0 - abs( 2.0*t - 1.0 ) );
}

float remap( float t, float a, float b ) {
	return sat( (t - a) / (b - a) );
}

vec4 spectrum_offset( float t ) {
	vec4 ret;
	float lo = step(t,0.5);
	float hi = 1.0-lo;
	float w = linterp( remap( t, 1.0/6.0, 5.0/6.0 ) );
	ret = vec4(lo,1.0,hi, 1.) * vec4(1.0-w, w, 1.0-w, 1.);

	return pow( ret, vec4(1.0/2.2) );
}

uniform float intensity;
const int num_iter = 3;
const float reci_num_iter_f = 1.0 / float(num_iter);


void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {

    vec4 sumcol = vec4(0.0);
	vec4 sumw = vec4(0.0);	
	for ( int i=0; i<num_iter;++i )
	{
		float t = float(i) * reci_num_iter_f;
		vec4 w = spectrum_offset( t );
		sumw += w;
		sumcol += w * texture2D( inputBuffer, barrelDistortion(uv, .6 * intensity*t ) );
	}

	outputColor = sumcol / sumw;

}

`

export class ChromaticAberrationEffect extends Effect {
  constructor() {
    super('ChromaticAberrationEffect', fragmentShader, {
      blendFunction: BlendFunction.NORMAL,
      uniforms: new Map([['intensity', new Uniform(0.2)]]),
    })
  }
}

export function useChromaticAberration() {
  const [effect] = useState(() => new ChromaticAberrationEffect())

  const sheet = useCurrentSheet()

  useTheatre(
    sheet,
    'chromaticAberration',
    {
      intensity: types.number(-0.2, {
        range: [-2, 2],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ intensity }) => {
        effect.uniforms.get('intensity').value = intensity
      },
    }
  )

  return effect
}
