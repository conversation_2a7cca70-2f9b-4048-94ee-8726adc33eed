import { useThree } from '@react-three/fiber'
import { types } from '@theatre/core'
import { ToneMappingEffect, ToneMappingMode } from 'postprocessing'
import { useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export function useTone() {
  const [effect] = useState(
    () =>
      new ToneMappingEffect({
        mode: ToneMappingMode.ACES_FILMIC,
        resolution: 256,
        whitePoint: 16.0,
        middleGrey: 0.6,
        minLuminance: 0.01,
        averageLuminance: 0.01,
        adaptationRate: 1.0,
      })
  )

  const gl = useThree((state) => state.gl)

  const sheet = useCurrentSheet()
  useTheatre(
    sheet,
    'postprocessing / tone',
    {
      mode: types.stringLiteral(
        'NEUTRAL',
        Object.fromEntries(
          Object.entries(ToneMappingMode).map(([key]) => [key, key])
        )
      ),
      exposure: types.number(1, {
        range: [0, 2],
        nudgeMultiplier: 0.01,
      }),
    },
    {
      onValuesChange: ({ mode, exposure }) => {
        effect.mode = ToneMappingMode[mode]
        gl.toneMappingExposure = exposure
      },
    }
  )

  return effect
}
