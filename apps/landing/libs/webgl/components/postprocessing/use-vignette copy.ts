import { types } from '@theatre/core'
import { VignetteEffect, VignetteTechnique } from 'postprocessing'
import { useState } from 'react'
import { useCurrentSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export function useVignette() {
  const [effect] = useState(
    () =>
      new VignetteEffect({
        technique: VignetteTechnique.DEFAULT,
        offset: 0.0,
        darkness: 1.0,
      })
  )

  const sheet = useCurrentSheet()
  useTheatre(
    sheet,
    'vignette',
    {
      technique: types.stringLiteral(
        'DEFAULT',
        Object.fromEntries(
          Object.entries(VignetteTechnique).map(([key]) => [key, key])
        )
      ),
      offset: types.number(0, {
        range: [0, 1],
      }),
      darkness: types.number(1, {
        range: [0, 1],
      }),
    },
    {
      onValuesChange: ({ offset, darkness, technique }) => {
        effect.technique = VignetteTechnique[technique]
        effect.offset = offset
        effect.darkness = darkness
      },
    }
  )

  return effect
}
