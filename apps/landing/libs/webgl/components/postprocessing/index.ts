import { use<PERSON><PERSON><PERSON>, useThree } from '@react-three/fiber'
import {
  <PERSON>pyPass,
  EffectComposer,
  EffectPass,
  RenderPass,
} from 'postprocessing'
import { useEffect, useMemo, useState } from 'react'
import { HalfFloatType } from 'three'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { useCurrentSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useChaptersEffect } from './use-chapters'
import { useChromaticAberration } from './use-chromatic-aberration'
import { useNoiseEffect } from './use-noise'
import { useUi } from './use-ui'
import { useVignette } from './use-vignette'

export function PostProcessing() {
  console.log('WebGL: PostProcessing enabled')

  const gl = useThree((state) => state.gl)
  const viewport = useThree((state) => state.viewport)
  const camera = useThree((state) => state.camera)
  const scene = useThree((state) => state.scene)
  const setDpr = useThree((state) => state.setDpr)
  const size = useThree((state) => state.size)
  const initialDpr = useThree((state) => state.viewport.initialDpr)

  const isWebgl2 = gl.capabilities.isWebGL2
  const dpr = viewport.dpr
  const maxSamples = gl.capabilities.maxSamples
  const needsAA = dpr < 2

  const composer = useMemo(
    () =>
      new EffectComposer(gl, {
        // multisampling: isWebgl2 && needsAA ? maxSamples : 0,
        frameBufferType: HalfFloatType,
      }),
    [gl, needsAA, isWebgl2, maxSamples]
  )

  const renderPass = useMemo(
    () => new RenderPass(scene, camera),
    [scene, camera]
  )

  const copyPass = useMemo(() => new CopyPass(), [])

  const chaptersEffect = useChaptersEffect()

  const vignetteEffect = useVignette()

  const chromaticAberrationEffect = useChromaticAberration()

  const chaptersPass = useMemo(
    () => new EffectPass(camera, chaptersEffect),
    [camera, chaptersEffect]
  )

  const noiseEffect = useNoiseEffect()

  const uiEffect = useUi(copyPass.texture)

  const effectPass = useMemo(
    () =>
      new EffectPass(
        camera,
        chromaticAberrationEffect,
        // bloomEffect,
        vignetteEffect,
        // toneEffect,
        noiseEffect,
        uiEffect
      ),
    [
      camera,
      chromaticAberrationEffect,
      vignetteEffect,
      // toneEffect,
      // bloomEffect,
      noiseEffect,
      uiEffect,
    ]
  )

  const uiPass = useMemo(
    () => new EffectPass(camera, uiEffect),
    [camera, uiEffect]
  )

  const sheet = useCurrentSheet()

  const [enabled, setEnabled] = useState(true)

  const isStudio = useStudio()

  useTheatre(
    sheet,
    'postprocessing',
    {
      enabled: true,
    },
    {
      onValuesChange: ({ enabled }) => {
        if (isStudio) {
          setEnabled(enabled)
        }
      },
      deps: [isStudio],
    }
  )

  useEffect(() => {
    composer.addPass(renderPass)
    composer.addPass(copyPass)
    composer.addPass(chaptersPass)

    if (enabled) {
      // composer.addPass(copyPass)
      composer.addPass(effectPass)
      // composer.addPass(normalPass)
      // composer.addPass(depthDownsamplingPass)
    } else {
      composer.addPass(uiPass)
    }

    // composer.addPass(effectPass)

    return () => composer.removeAllPasses()
  }, [
    composer,
    renderPass,
    effectPass,
    chaptersPass,
    uiPass,
    enabled,
    copyPass,
    isStudio,
  ])

  // const { width: windowWidth = 0, height: windowHeight = 0 } = useWindowSize()

  const { isMobile } = useDeviceDetection()

  useEffect(() => {
    const dpr = size.width <= 2048 ? initialDpr : 1
    setDpr(isMobile ? 1 : dpr)

    composer.setSize(size.width, size.height)
  }, [composer, size, setDpr, initialDpr, isMobile])

  useFrame((_, deltaTime) => {
    composer.render(deltaTime)
  }, Number.POSITIVE_INFINITY)

  return null
}
