'use client'

import { use<PERSON><PERSON><PERSON>, useThree } from '@react-three/fiber'
import { useWindowSize } from 'hamo'
import {
  Children,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { suspend } from 'suspend-react'
import { MeshBasicMaterial } from 'three'
import { Text as TextMeshImpl, preloadFont } from 'troika-three-text'
import { lerp } from '~/libs/maths'
import { createTextDerivedMaterial } from './text-derived-material'
import { useTheaterText } from './theater'

export function Text({
  children,
  characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  font,
  fontSize,
  sdfGlyphSize = 64,
  position = [0, 0, -1],
  scale = [1, 1, 1],
  rect,
  ref,
  verticalOffset = 0,
  theaterConfig,
  ...props
}) {
  const stickyOffset = useRef(0)

  const [scaleFrom, setScaleFrom] = useState([0.95, 0.95, 1])
  const [troikaMesh] = useState(() => new TextMeshImpl())
  const [material] = useState(() =>
    createTextDerivedMaterial(new MeshBasicMaterial(), fontSize)
  )
  const { width: windowWidth, height: windowHeight } = useWindowSize()

  // Font preloading
  suspend(
    () =>
      new Promise((res) => {
        preloadFont({ font, characters }, res)
      }),
    ['troika-text', font, characters]
  )

  useEffect(() => {
    if (!material) return

    troikaMesh.material = material
  }, [troikaMesh, material])

  useEffect(() => {
    return () => troikaMesh.dispose()
  }, [troikaMesh])

  useEffect(() => {
    if (fontSize) {
      material.fontSize = fontSize
    }
  }, [fontSize])

  useEffect(() => {
    material.screenAspectRatio = windowWidth / windowHeight
  }, [windowWidth, windowHeight])

  const text = useMemo(() => {
    let t = ''
    Children.forEach(children, (child) => {
      if (typeof child === 'string' || typeof child === 'number') {
        t += child
      }
    })

    material.wordLength = t.length
    return t
  }, [children])

  const scaleMesh = useCallback(
    (value) => {
      troikaMesh.scale.set(
        lerp(scaleFrom[0], scale[0], value),
        lerp(scaleFrom[1], scale[1], value),
        1
      )
    },
    [troikaMesh, scaleFrom, scale]
  )

  useImperativeHandle(ref, () => ({
    mesh: troikaMesh,
    material,
    progress: (p) => {
      material.progress = p
    },
    stickyOffset: (y) => {
      stickyOffset.current = y
    },
  }))

  useFrame(({ clock }) => {
    material.time = clock.elapsedTime
  })

  useRectPosition(rect, troikaMesh, stickyOffset, verticalOffset)

  useTheaterText({
    theaterConfig,
    scaleMesh,
    setScaleFrom,
    material,
  })

  return (
    <primitive
      ref={ref}
      object={troikaMesh}
      sdfGlyphSize={sdfGlyphSize}
      text={text}
      font={font}
      fontSize={fontSize}
      position={position}
      scale={scaleFrom}
      {...props}
    />
  )
}

function useRectPosition(rect, target, stickyOffset = 0, verticalOffset = 0) {
  const size = useThree((state) => state.size)

  useFrame(() => {
    const x = -size.width / 2 + (rect?.left + rect?.width / 2)
    const y =
      size.height / 2 -
      rect.height / 2 -
      rect.top +
      verticalOffset +
      stickyOffset.current

    target.position.set(x, y, -2)
    target.updateMatrix()
  })
}
