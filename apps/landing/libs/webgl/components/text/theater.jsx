import { types } from '@theatre/core'
import { useIntroTheatre } from '~/app/(pages)/home/<USER>/intro'
import { useCurrentSheet, useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'

export function useTheaterText({
  theaterConfig,
  scaleMesh,
  setScaleFrom,
  material,
}) {
  const { sheet, object } = theaterConfig
  const currentSheet = useCurrentSheet()
  const instancedSheet = useSheet('Text', `${sheet}-${object}`)

  useTheatre(
    instancedSheet,
    'text',
    {
      frequency: types.number(5, { range: [0, 100], nudgeMultiplier: 0.01 }),
      amplitude: types.number(35, { range: [0, 100], nudgeMultiplier: 0.01 }),
      noiseSpeed: types.number(0.5, { range: [0, 5], nudgeMultiplier: 0.1 }),
      glyphDelay: types.number(0.15, { range: [0, 1], nudgeMultiplier: 0.01 }),
      scaleFrom: types.number(0.95, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({
        frequency,
        amplitude,
        noiseSpeed,
        glyphDelay,
        scaleFrom,
      }) => {
        setScaleFrom([scaleFrom, scaleFrom, 1])
        material.frequency = frequency
        material.amplitude = amplitude
        material.noiseSpeed = noiseSpeed
        material.glyphDelay = glyphDelay
      },
    }
  )

  useTheatre(
    currentSheet,
    object,
    {
      progress: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ progress }) => {
        material.progress = progress
        scaleMesh(progress)
      },
    }
  )

  useIntroTheatre({
    theaterConfig,
    onValuesChange: ({ progress }) => {
      material.progress = progress
      scaleMesh(progress)
    },
  })
}
