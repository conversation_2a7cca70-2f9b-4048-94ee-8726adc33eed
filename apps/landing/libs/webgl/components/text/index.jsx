'use client'

import cn from 'clsx'
import gsap from 'gsap'
import { useRect, useWindowSize } from 'hamo'
import dynamic from 'next/dynamic'
import { useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useTempus } from 'tempus/react'
import { useDeviceDetection } from '~/hooks/use-device-detection'
import { SheetProvider } from '~/libs/theatre'
import { desktopVW, mobileVW } from '~/libs/utils'
import { WebGLTunnel } from '~/libs/webgl/components/tunnel'
import s from './text.module.css'

const Text = dynamic(() => import('./text').then(({ Text }) => Text), {
  ssr: false,
})

export default function WebGLText({
  children,
  className,
  fontSize = 128,
  theaterConfig = {
    sheet: 'Text',
    object: 'text',
  },
  verticalOffset = 0,
  fontVars,
  tag = 'h3',
  ref,
}) {
  const Tag = tag
  const textRef = useRef()
  const { width: windowWidth, height: windowHeight } = useWindowSize()
  const [textRectRef, textRect] = useRect({
    ignoreSticky: true,
  })
  const [responsiveFontSize, setResponsiveFontSize] = useState(fontSize)
  const { isMobile } = useDeviceDetection()

  useEffect(() => {
    const scaled = isMobile
      ? mobileVW(fontSize * 0.5, windowWidth)
      : desktopVW(fontSize, windowWidth)
    setResponsiveFontSize(scaled)
  }, [windowWidth, isMobile])

  // Force rect update when font size changes
  useEffect(() => {
    if (textRect?.resize) {
      textRect.resize()
    }
  }, [responsiveFontSize, textRect])

  useImperativeHandle(ref, () => ({
    text: textRef.current,
  }))

  const enterPointerRef = useRef(false)
  const leavePointerRef = useRef(0)

  useEffect(() => {
    const handleWindowPointer = (event) => {
      const x = (event.clientX / windowWidth) * 2 - 1
      const y = -(event.clientY / windowHeight) * 2 + 1

      gsap.to(textRef.current?.material?.cursor, {
        x,
        y,
        ease: 'power.inOut',
        duration: 0.25,
      })

      enterPointerRef.current = true
    }

    window.addEventListener('pointermove', handleWindowPointer)
    return () => window.removeEventListener('pointermove', handleWindowPointer)
  }, [textRect, windowWidth, windowHeight])

  useTempus((_, deltaTime) => {
    if (!textRef.current?.material) return

    const time = deltaTime * 0.005

    if (enterPointerRef.current) {
      textRef.current.material.cursor.z += time
      textRef.current.material.cursor.z = Math.min(
        textRef.current.material.cursor.z,
        1
      )

      enterPointerRef.current = false
      leavePointerRef.current = 0
    }

    leavePointerRef.current += time

    // avoids flicker when moving the mouse frequently
    if (leavePointerRef.current < 1.5) return

    textRef.current.material.cursor.z -= time * 0.25
    textRef.current.material.cursor.z = Math.max(
      textRef.current.material.cursor.z,
      0
    )
  })

  return (
    <>
      <Tag
        className={cn(
          s.title,
          //  'desktop-only',
          className
        )}
        ref={textRectRef}
        data-cursor="pointer"
      >
        {children}
      </Tag>
      <WebGLTunnel>
        <SheetProvider id={theaterConfig?.sheet}>
          <Text
            ref={textRef}
            rect={textRect}
            fontSize={responsiveFontSize}
            verticalOffset={verticalOffset}
            theaterConfig={theaterConfig}
            {...fontVars}
          >
            {children}
          </Text>
        </SheetProvider>
      </WebGLTunnel>
    </>
  )
}
