import { LinearMipmapLinearFilter, SRGBColorSpace } from 'three'

export function updateTextures(
  textures = [],
  {
    minFilter = LinearMipmapLinearFilter,
    magFilter = LinearMipmapLinearFilter,
    colorSpace = SRGBColorSpace,
    flipY = false,
  } = {}
) {
  return textures.map((texture) => {
    texture.generateMipmaps = true
    texture.minFilter = minFilter
    texture.magFilter = magFilter
    texture.colorSpace = colorSpace
    texture.flipY = flipY
    texture.needsUpdate = true
    return texture
  })
}
