import { BLEND } from './blend'

export const getWorldPositionShader = () => {
  const uniforms = {}

  const defines = {
    DISTANCE: '',
  }

  return {
    uniforms,
    defines,
    extend: (shader) => {
      shader.defines.DISTANCE = ''

      shader.vertexShader = shader.vertexShader.replace(
        'void main() {',
        /*glsl*/ `
        varying vec4 vWorldPosition;
  
        void main() {
        `
      )

      shader.vertexShader = shader.vertexShader.replace(
        '#include <worldpos_vertex>',
        /*glsl*/ `
        #include <worldpos_vertex>
        vWorldPosition = worldPosition;
        `
      )

      shader.fragmentShader = shader.fragmentShader.replace(
        'void main() {',
        /*glsl*/ `
        varying vec4 vWorldPosition;
        
        void main() {
        `
      )
    },
  }
}

export const getNormalsShader = () => {
  const uniforms = {}

  const defines = {
    FLAT_SHADED: '',
  }

  return {
    uniforms,
    defines,
    extend: (shader) => {
      shader.defines.FLAT_SHADED = ''

      shader.vertexShader = shader.vertexShader.replace(
        'void main() {',
        /*glsl*/ `
        varying vec3 vNormal;
  
        void main() {

            vNormal = normal;
      `
      )

      shader.fragmentShader = shader.fragmentShader.replace(
        'void main() {',
        /*glsl*/ `
      varying vec3 vNormal;

      void main() {
    `
      )
    },
  }
}

export const getTimeShader = () => {
  const uniforms = {
    uTime: { value: 0.0 },
  }

  const defines = {}

  return {
    uniforms,
    defines,
    raf: (deltaTime) => {
      uniforms.uTime.value += deltaTime
    },
    extend: (shader) => {
      shader.fragmentShader = shader.fragmentShader.replace(
        'void main() {',
        /*glsl*/ `
                uniform float uTime;
        
                void main() {
                `
      )
    },
  }
}

export const shadowsUniforms = {
  uShadowsNoiseTexture: { value: null },
  uShadowsSpeed: { value: 0.01 },
  uShadowsFrequency: { value: 0.001 },
  uShadowAmplitude: { value: 1 },
}

export const getShadowsShader = () => {
  const defines = {
    MAP_UV: 'uv',
    USE_MAP: '',
  }

  return {
    uniforms: shadowsUniforms,
    defines,
    set noiseTexture(value) {
      this.uniforms.uShadowsNoiseTexture.value = value
    },
    get noiseTexture() {
      return this.uniforms.uShadowsNoiseTexture.value
    },
    set speed(value) {
      this.uniforms.uShadowsSpeed.value = value
    },
    get speed() {
      return this.uniforms.uShadowsSpeed.value
    },
    set frequency(value) {
      this.uniforms.uShadowsFrequency.value = value
    },
    get frequency() {
      return this.uniforms.uShadowsFrequency.value
    },
    set amplitude(value) {
      this.uniforms.uShadowAmplitude.value = value
    },
    get amplitude() {
      return this.uniforms.uShadowAmplitude.value
    },
    extend: (shader) => {
      shader.fragmentShader = shader.fragmentShader.replace(
        'void main() {',
        /*glsl*/ `

        ${BLEND.NORMAL}
        
        uniform sampler2D uShadowsNoiseTexture;
        uniform float uShadowsSpeed;
        uniform float uShadowsFrequency;
        uniform float uShadowAmplitude;
    
        void main() {
          
          float shadows = texture2D( uShadowsNoiseTexture, vWorldPosition.xz * uShadowsFrequency + vec2(uTime * uShadowsSpeed,0.) ).r * uShadowAmplitude;
          shadows = clamp(shadows, 0., 1.);
          `
      )

      shader.fragmentShader = shader.fragmentShader.replace(
        'vec4 diffuseColor = vec4( diffuse, opacity );',
        /*glsl*/ `
        vec4 diffuseColor = vec4( diffuse, opacity );
         diffuseColor.rgb = blendNormal(diffuseColor.rgb, vec3(0.,0.,0.), shadows);
          `
      )

      shader.fragmentShader = shader.fragmentShader.replace(
        'vec3 totalDiffuse = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse;',
        /*glsl*/ `

        reflectedLight.directDiffuse = blendNormal(reflectedLight.directDiffuse, vec3(0.,0.,0.), shadows);

        vec3 totalDiffuse = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse;
          `
      )

      shader.fragmentShader = shader.fragmentShader.replace(
        'vec3 totalSpecular = reflectedLight.directSpecular + reflectedLight.indirectSpecular;',
        /*glsl*/ `
        reflectedLight.directSpecular = blendNormal(reflectedLight.directSpecular, vec3(0.,0.,0.), shadows);

        vec3 totalSpecular = reflectedLight.directSpecular + reflectedLight.indirectSpecular;
          `
      )
    },
  }
}
