/*!
 *  Filters Plugin - Adds support for filters (lowpass, high pass, band pass, or notch) on individual sounds when using WebAudio.
 *                 - <PERSON> <EMAIL>
 *
 *  howler.js v2.0.4
 *  howlerjs.com
 *
 *  (c) 2013-2017, <PERSON> of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */
;(() => {
  /** Group Methods **/
  /***************************************************************************/

  /**
   * Add new properties to the core init.
   * @param  {Function} _super Core init method.
   * @return {Howl}
   */
  Howl.prototype.init = ((_super) =>
    function (o) {
      this._q = o.qFactor || 1.0
      this._filterType = o.filterType || 'lowpass'
      this._frequency = o.frequency || 1000.0

      // Setup event listeners.
      this._onqFactor = o.onqFactor ? [{ fn: o.onqFactor }] : []
      this._onfrequency = o.onfrequency ? [{ fn: o.onfrequency }] : []
      this._onaddFilter = o.onaddFilter ? [{ fn: o.onaddFilter }] : []
      this._onfilterType = o.onfilterType ? [{ fn: o.onfilterType }] : []

      // Complete initilization with howler.js core's init function.
      return _super.call(this, o)
    })(Howl.prototype.init)

  /**
   * Sets or gets Q factor for the Howl's filter.
   * Future Howls will not use this value unless explicitly set.
   * @param  {Number} q Q Factor, a value between 0.001 - 1000.0; determines resonance peak at cutoff for LPF and HPF or bandwidth for notch and BPF.
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl/Number}     Self or current Q value.
   */
  Howl.prototype.qFactor = function (q, id) {
    // Stop right here if not using Web Audio.
    if (!this._webAudio) {
      return this
    }

    // If the sound hasn't loaded, add it to the load queue to change q when capable.
    if (this._state !== 'loaded') {
      this._queue.push({
        event: 'qFactor',
        action: () => {
          this.qFactor(q, id)
        },
      })

      return this
    }

    if (typeof id === 'undefined') {
      if (typeof q === 'number') {
        this._q = q
      } else {
        return this._q
      }
    }

    var ids = this._getSoundIds(id)
    for (var i = 0; i < ids.length; i++) {
      // Get the sound.
      var sound = this._soundById(ids[i])

      if (sound) {
        if (typeof q === 'number') {
          if (sound._node) {
            this._q = q
            if (!sound._filterNode) {
              setupFilter(sound)
            }

            sound._filterNode.Q.value = q
            this._emit('qFactor', sound._id)
          }
        } else {
          return sound._q
        }
      }
    }

    return this
  }

  /**
   * Helper method to update the frequency of all current Howl filters. Depending on the filter type, this will either be the cutoff (for HPF and LPF)
   * or center frequency (for notch and BPF).
   * Future Howls will not use this value unless explicitly set.
   * @param  {String} f Frequency between 10 and Nyquist.
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl/String}     Self or current filter type.
   */
  Howl.prototype.filterType = function (type, id) {
    // Stop right here if not using Web Audio.
    if (!this._webAudio) {
      return this
    }

    // If the sound hasn't loaded, add it to the load queue to change q when capable.
    if (this._state !== 'loaded') {
      this._queue.push({
        event: 'filterType',
        action: () => {
          this.filterType(type, id)
        },
      })

      return this
    }

    if (typeof id === 'undefined') {
      if (
        type === 'lowpass' ||
        type === 'highpass' ||
        type === 'bandpass' ||
        type === 'notch'
      ) {
        this._filterType = type
      } else {
        return this._filterType
      }
    }

    var ids = this._getSoundIds(id)
    for (var i = 0; i < ids.length; i++) {
      // Get the sound.
      var sound = this._soundById(ids[i])

      if (sound) {
        if (
          type === 'lowpass' ||
          type === 'highpass' ||
          type === 'bandpass' ||
          type === 'notch'
        ) {
          sound._filterType = type

          if (sound._node) {
            if (!sound._filterNode) {
              setupFilter(sound)
            }

            sound._filterNode.type = type
            this._emit('filterType', sound._id)
          }
        } else {
          return sound._filterType
        }
      }
    }

    return this
  }

  /**
   * Helper method to update the frequency of current Howl filter. Depending on the filter type, this will either be the cutoff (for HPF and LPF)
   * or center frequency (for notch and BPF).
   * Future Howls will not use this value unless explicitly set.
   * @param  {Number} f Frequency between 10 and Nyquist.
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl/Number}     Self or current frequency value.
   */
  Howl.prototype.frequency = function (f, id) {
    // Stop right here if not using Web Audio.
    if (!this._webAudio) {
      return this
    }

    // If the sound hasn't loaded, add it to the load queue to change q when capable.
    if (this._state !== 'loaded') {
      this._queue.push({
        event: 'frequency',
        action: () => {
          this.frequency(f, id)
        },
      })

      return this
    }

    if (typeof id === 'undefined') {
      if (typeof f === 'number') {
        this._frequency = f
      } else {
        return this._frequency
      }
    }

    var ids = this._getSoundIds(id)
    for (var i = 0; i < ids.length; i++) {
      // Get the sound.
      var sound = this._soundById(ids[i])

      if (sound) {
        if (typeof f === 'number') {
          sound._frequency = f

          if (sound._node) {
            if (!sound._filterNode) {
              setupFilter(sound)
            }

            sound._filterNode.frequency.value = f
            this._emit('frequency', sound._id)
          }
        } else {
          return sound._frequency
        }
      }
    }

    return this
  }

  /**
   * Helper method to update multiple filter parameters at once
   * filterType is a string describing the type of filter; 'highpass', 'lowpass', 'bandpass', or 'notch'
   * Q is the quality factor of the filter. Depending on the type, it affects resonance peak at the cutoff or bandwidth
   * frequency is either the cutoff or center frequency, depending on filter type
   * @param  {Object} filterParams Object of parameters to set for filter. {filterType: string, Q: number, frequency: number}
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl}     Self.
   */
  Howl.prototype.addFilter = function (filterParams, id) {
    // Stop right here if not using Web Audio.
    if (!this._webAudio) {
      return this
    }

    // If the sound hasn't loaded, add it to the load queue to change q when capable.
    if (this._state !== 'loaded') {
      this._queue.push({
        event: 'addFilter',
        action: () => {
          this.addFilter(filterParams, id)
        },
      })

      return this
    }

    var ids = this._getSoundIds(id)
    for (var i = 0; i < ids.length; i++) {
      // Get the sound.
      var sound = this._soundById(ids[i])

      if (sound) {
        if (sound._node) {
          if (!sound._filterNode) {
            setupFilter(sound)
          }
          sound._filterNode.frequency.value =
            filterParams.frequency || sound._frequency
          sound._filterNode.Q.value = filterParams.Q || sound._q
          sound._filterNode.type = filterParams.filterType || sound._filterType
          this._emit('addFilter', sound._id)
        }
      }
    }

    return this
  }

  /** Single Sound Methods **/
  /***************************************************************************/

  /**
   * Add new properties to the core Sound init.
   * @param  {Function} _super Core Sound init method.
   * @return {Sound}
   */
  Sound.prototype.init = ((_super) =>
    function () {
      var parent = this._parent

      // Setup user-defined default properties.
      this._q = parent._q
      this._filterType = parent._filterType
      this._frequency = parent._frequency

      // Complete initilization with howler.js core Sound's init function.
      _super.call(this)
    })(Sound.prototype.init)

  /**
   * Override the Sound.reset method to clean up properties from the filters plugin.
   * @param  {Function} _super Sound reset method.
   * @return {Sound}
   */
  Sound.prototype.reset = ((_super) =>
    function () {
      var parent = this._parent

      // Reset all filters plugin properties on this sound.
      this._q = parent._q
      this._filterType = parent._filterType
      this._frequency = parent._frequency

      // Complete resetting of the sound.
      return _super.call(this)
    })(Sound.prototype.reset)

  /** Helper Methods **/
  /***************************************************************************/

  var setupFilter = (sound) => {
    // Create the new convolver send gain node.
    sound._filterNode = Howler.ctx.createBiquadFilter()
    // set default gain node values
    sound._filterNode.gain.value = 1.0
    sound._filterNode.frequency.value = sound._frequency || 1000.0
    sound._filterNode.type = sound._filterType || 'lowpass'
    sound._filterNode.Q.value = sound._q || 1.0
    // connect sound's gain node to convolver send gain node
    sound._fxInsertIn.disconnect()
    sound._fxInsertIn.connect(sound._filterNode)
    sound._filterNode.connect(sound._fxInsertOut)
    // Update the connections.
    if (!sound._paused) {
      sound._parent.pause(sound._id, true).play(sound._id)
    }
  }
})()
