import { create } from 'zustand'

type Store = {
  isNavOpened: boolean
  setIsNavOpened: (value: boolean) => void
  loaderLoaded: boolean
  setLoaderLoaded: (value: boolean) => void
  introCompleted: boolean
  setIntroCompleted: (value: boolean) => void
}

export const useStore = create<Store>((set) => ({
  isNavOpened: false,
  setIsNavOpened: (value: boolean) => set({ isNavOpened: value }),
  loaderLoaded: false,
  setLoaderLoaded: (value: boolean) => set({ loaderLoaded: value }),
  introCompleted: false,
  setIntroCompleted: (value: boolean) => set({ introCompleted: value }),
}))
