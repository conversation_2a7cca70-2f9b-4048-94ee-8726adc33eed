node_modules/
/node_modules
*/node_modules/
**/node_modules/

package-lock.json
yarn.lock

.next/
dist/
build/
out/

.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

.vscode/
.idea/
*.swp
*.swo

.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

pids
*.pid
*.seed
*.pid.lock

coverage/

*.tsbuildinfo

tmp/
temp/

.vercel

.turbo

**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map